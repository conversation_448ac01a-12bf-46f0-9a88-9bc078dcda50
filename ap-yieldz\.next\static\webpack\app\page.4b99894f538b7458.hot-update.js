"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Dashboard */ \"(app-pages-browser)/./app/components/Dashboard.tsx\");\n/* harmony import */ var _components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/APYComparisonTable */ \"(app-pages-browser)/./app/components/APYComparisonTable.tsx\");\n/* harmony import */ var _components_Portfolio__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Portfolio */ \"(app-pages-browser)/./app/components/Portfolio.tsx\");\n/* harmony import */ var _components_TradingModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/TradingModal */ \"(app-pages-browser)/./app/components/TradingModal.tsx\");\n/* harmony import */ var _components_ContractTest__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/ContractTest */ \"(app-pages-browser)/./app/components/ContractTest.tsx\");\n/* harmony import */ var _components_ContractSetup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ContractSetup */ \"(app-pages-browser)/./app/components/ContractSetup.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [tradingModal, setTradingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        asset: '',\n        action: 'supply'\n    });\n    const handleAssetSelect = (asset)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action: 'supply'\n        });\n    };\n    const handlePortfolioAction = (asset, action)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action\n        });\n    };\n    const closeTradingModal = ()=>{\n        setTradingModal((prev)=>({\n                ...prev,\n                isOpen: false\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-green-50 to-green-100 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n                                children: [\n                                    \"Find the Best \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-700\",\n                                        children: \"APY Rates\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Compare lending rates across Aave and Morpho protocols. Optimize your DeFi strategy with real-time APY data and seamless cross-chain bridging.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('dashboard'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'dashboard' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('markets'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'markets' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Markets\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('portfolio'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'portfolio' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 rounded-full p-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-yellow-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-yellow-800\",\n                                        children: \"Setup Required\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-700 mb-4\",\n                                children: \"Before you can see APY rates and use the trading features, you need to add supported assets to your contract.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-700 text-sm\",\n                                children: 'Scroll down to the \"Contract Setup\" section and click \"Add All Assets\" to get started.'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractTest__WEBPACK_IMPORTED_MODULE_6__.ContractTest, {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractSetup__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_2__.Dashboard, {}, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 39\n                    }, this),\n                    activeTab === 'markets' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__.APYComparisonTable, {\n                        onAssetSelect: handleAssetSelect\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 37\n                    }, this),\n                    activeTab === 'portfolio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Portfolio__WEBPACK_IMPORTED_MODULE_4__.Portfolio, {\n                        onActionClick: handlePortfolioAction\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 39\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Alligator?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: \"The most comprehensive DeFi APY aggregator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Real-time APY Comparison\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Compare rates across Aave and Morpho protocols in real-time to maximize your yields.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Cross-chain Bridging\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Seamlessly bridge assets between Avalanche and Base networks for optimal positioning.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Portfolio Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Track your positions across protocols and optimize your DeFi portfolio in one place.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"\\uD83D\\uDC0A Alligator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Your DeFi yield optimization companion\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Docs\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"GitHub\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradingModal__WEBPACK_IMPORTED_MODULE_5__.TradingModal, {\n                isOpen: tradingModal.isOpen,\n                onClose: closeTradingModal,\n                selectedAsset: tradingModal.asset,\n                defaultAction: tradingModal.action\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"3Dqkv54y2l/XSZczm1G4HWmloIc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});