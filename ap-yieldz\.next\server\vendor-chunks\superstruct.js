"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/superstruct";
exports.ids = ["vendor-chunks/superstruct"];
exports.modules = {

/***/ "(ssr)/./node_modules/superstruct/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/superstruct/dist/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Struct: () => (/* binding */ Struct),\n/* harmony export */   StructError: () => (/* binding */ StructError),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   array: () => (/* binding */ array),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   assign: () => (/* binding */ assign),\n/* harmony export */   bigint: () => (/* binding */ bigint),\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   defaulted: () => (/* binding */ defaulted),\n/* harmony export */   define: () => (/* binding */ define),\n/* harmony export */   deprecated: () => (/* binding */ deprecated),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   enums: () => (/* binding */ enums),\n/* harmony export */   func: () => (/* binding */ func),\n/* harmony export */   instance: () => (/* binding */ instance),\n/* harmony export */   integer: () => (/* binding */ integer),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   literal: () => (/* binding */ literal),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   mask: () => (/* binding */ mask),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   nonempty: () => (/* binding */ nonempty),\n/* harmony export */   nullable: () => (/* binding */ nullable),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   object: () => (/* binding */ object),\n/* harmony export */   omit: () => (/* binding */ omit),\n/* harmony export */   optional: () => (/* binding */ optional),\n/* harmony export */   partial: () => (/* binding */ partial),\n/* harmony export */   pattern: () => (/* binding */ pattern),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   record: () => (/* binding */ record),\n/* harmony export */   refine: () => (/* binding */ refine),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   struct: () => (/* binding */ struct),\n/* harmony export */   trimmed: () => (/* binding */ trimmed),\n/* harmony export */   tuple: () => (/* binding */ tuple),\n/* harmony export */   type: () => (/* binding */ type),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   unknown: () => (/* binding */ unknown),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\nclass StructError extends TypeError {\n    constructor(failure, failures) {\n        let cached;\n        const { message, explanation, ...rest } = failure;\n        const { path } = failure;\n        const msg = path.length === 0 ? message : `At path: ${path.join('.')} -- ${message}`;\n        super(explanation ?? msg);\n        if (explanation != null)\n            this.cause = msg;\n        Object.assign(this, rest);\n        this.name = this.constructor.name;\n        this.failures = () => {\n            return (cached ?? (cached = [failure, ...failures()]));\n        };\n    }\n}\n\n/**\n * Check if a value is an iterator.\n */\nfunction isIterable(x) {\n    return isObject(x) && typeof x[Symbol.iterator] === 'function';\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isObject(x) {\n    return typeof x === 'object' && x != null;\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isPlainObject(x) {\n    if (Object.prototype.toString.call(x) !== '[object Object]') {\n        return false;\n    }\n    const prototype = Object.getPrototypeOf(x);\n    return prototype === null || prototype === Object.prototype;\n}\n/**\n * Return a value as a printable string.\n */\nfunction print(value) {\n    if (typeof value === 'symbol') {\n        return value.toString();\n    }\n    return typeof value === 'string' ? JSON.stringify(value) : `${value}`;\n}\n/**\n * Shifts (removes and returns) the first value from the `input` iterator.\n * Like `Array.prototype.shift()` but for an `Iterator`.\n */\nfunction shiftIterator(input) {\n    const { done, value } = input.next();\n    return done ? undefined : value;\n}\n/**\n * Convert a single validation result to a failure.\n */\nfunction toFailure(result, context, struct, value) {\n    if (result === true) {\n        return;\n    }\n    else if (result === false) {\n        result = {};\n    }\n    else if (typeof result === 'string') {\n        result = { message: result };\n    }\n    const { path, branch } = context;\n    const { type } = struct;\n    const { refinement, message = `Expected a value of type \\`${type}\\`${refinement ? ` with refinement \\`${refinement}\\`` : ''}, but received: \\`${print(value)}\\``, } = result;\n    return {\n        value,\n        type,\n        refinement,\n        key: path[path.length - 1],\n        path,\n        branch,\n        ...result,\n        message,\n    };\n}\n/**\n * Convert a validation result to an iterable of failures.\n */\nfunction* toFailures(result, context, struct, value) {\n    if (!isIterable(result)) {\n        result = [result];\n    }\n    for (const r of result) {\n        const failure = toFailure(r, context, struct, value);\n        if (failure) {\n            yield failure;\n        }\n    }\n}\n/**\n * Check a value against a struct, traversing deeply into nested values, and\n * returning an iterator of failures or success.\n */\nfunction* run(value, struct, options = {}) {\n    const { path = [], branch = [value], coerce = false, mask = false } = options;\n    const ctx = { path, branch };\n    if (coerce) {\n        value = struct.coercer(value, ctx);\n        if (mask &&\n            struct.type !== 'type' &&\n            isObject(struct.schema) &&\n            isObject(value) &&\n            !Array.isArray(value)) {\n            for (const key in value) {\n                if (struct.schema[key] === undefined) {\n                    delete value[key];\n                }\n            }\n        }\n    }\n    let status = 'valid';\n    for (const failure of struct.validator(value, ctx)) {\n        failure.explanation = options.message;\n        status = 'not_valid';\n        yield [failure, undefined];\n    }\n    for (let [k, v, s] of struct.entries(value, ctx)) {\n        const ts = run(v, s, {\n            path: k === undefined ? path : [...path, k],\n            branch: k === undefined ? branch : [...branch, v],\n            coerce,\n            mask,\n            message: options.message,\n        });\n        for (const t of ts) {\n            if (t[0]) {\n                status = t[0].refinement != null ? 'not_refined' : 'not_valid';\n                yield [t[0], undefined];\n            }\n            else if (coerce) {\n                v = t[1];\n                if (k === undefined) {\n                    value = v;\n                }\n                else if (value instanceof Map) {\n                    value.set(k, v);\n                }\n                else if (value instanceof Set) {\n                    value.add(v);\n                }\n                else if (isObject(value)) {\n                    if (v !== undefined || k in value)\n                        value[k] = v;\n                }\n            }\n        }\n    }\n    if (status !== 'not_valid') {\n        for (const failure of struct.refiner(value, ctx)) {\n            failure.explanation = options.message;\n            status = 'not_refined';\n            yield [failure, undefined];\n        }\n    }\n    if (status === 'valid') {\n        yield [undefined, value];\n    }\n}\n\n/**\n * `Struct` objects encapsulate the validation logic for a specific type of\n * values. Once constructed, you use the `assert`, `is` or `validate` helpers to\n * validate unknown input data against the struct.\n */\nclass Struct {\n    constructor(props) {\n        const { type, schema, validator, refiner, coercer = (value) => value, entries = function* () { }, } = props;\n        this.type = type;\n        this.schema = schema;\n        this.entries = entries;\n        this.coercer = coercer;\n        if (validator) {\n            this.validator = (value, context) => {\n                const result = validator(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.validator = () => [];\n        }\n        if (refiner) {\n            this.refiner = (value, context) => {\n                const result = refiner(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.refiner = () => [];\n        }\n    }\n    /**\n     * Assert that a value passes the struct's validation, throwing if it doesn't.\n     */\n    assert(value, message) {\n        return assert(value, this, message);\n    }\n    /**\n     * Create a value with the struct's coercion logic, then validate it.\n     */\n    create(value, message) {\n        return create(value, this, message);\n    }\n    /**\n     * Check if a value passes the struct's validation.\n     */\n    is(value) {\n        return is(value, this);\n    }\n    /**\n     * Mask a value, coercing and validating it, but returning only the subset of\n     * properties defined by the struct's schema.\n     */\n    mask(value, message) {\n        return mask(value, this, message);\n    }\n    /**\n     * Validate a value with the struct's validation logic, returning a tuple\n     * representing the result.\n     *\n     * You may optionally pass `true` for the `withCoercion` argument to coerce\n     * the value before attempting to validate it. If you do, the result will\n     * contain the coerced result when successful.\n     */\n    validate(value, options = {}) {\n        return validate(value, this, options);\n    }\n}\n/**\n * Assert that a value passes a struct, throwing if it doesn't.\n */\nfunction assert(value, struct, message) {\n    const result = validate(value, struct, { message });\n    if (result[0]) {\n        throw result[0];\n    }\n}\n/**\n * Create a value with the coercion logic of struct and validate it.\n */\nfunction create(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Mask a value, returning only the subset of properties defined by a struct.\n */\nfunction mask(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, mask: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Check if a value passes a struct.\n */\nfunction is(value, struct) {\n    const result = validate(value, struct);\n    return !result[0];\n}\n/**\n * Validate a value against a struct, returning an error if invalid, or the\n * value (with potential coercion) if valid.\n */\nfunction validate(value, struct, options = {}) {\n    const tuples = run(value, struct, options);\n    const tuple = shiftIterator(tuples);\n    if (tuple[0]) {\n        const error = new StructError(tuple[0], function* () {\n            for (const t of tuples) {\n                if (t[0]) {\n                    yield t[0];\n                }\n            }\n        });\n        return [error, undefined];\n    }\n    else {\n        const v = tuple[1];\n        return [undefined, v];\n    }\n}\n\nfunction assign(...Structs) {\n    const isType = Structs[0].type === 'type';\n    const schemas = Structs.map((s) => s.schema);\n    const schema = Object.assign({}, ...schemas);\n    return isType ? type(schema) : object(schema);\n}\n/**\n * Define a new struct type with a custom validation function.\n */\nfunction define(name, validator) {\n    return new Struct({ type: name, schema: null, validator });\n}\n/**\n * Create a new struct based on an existing struct, but the value is allowed to\n * be `undefined`. `log` will be called if the value is not `undefined`.\n */\nfunction deprecated(struct, log) {\n    return new Struct({\n        ...struct,\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n        validator(value, ctx) {\n            if (value === undefined) {\n                return true;\n            }\n            else {\n                log(value, ctx);\n                return struct.validator(value, ctx);\n            }\n        },\n    });\n}\n/**\n * Create a struct with dynamic validation logic.\n *\n * The callback will receive the value currently being validated, and must\n * return a struct object to validate it with. This can be useful to model\n * validation logic that changes based on its input.\n */\nfunction dynamic(fn) {\n    return new Struct({\n        type: 'dynamic',\n        schema: null,\n        *entries(value, ctx) {\n            const struct = fn(value, ctx);\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a struct with lazily evaluated validation logic.\n *\n * The first time validation is run with the struct, the callback will be called\n * and must return a struct object to use. This is useful for cases where you\n * want to have self-referential structs for nested data structures to avoid a\n * circular definition problem.\n */\nfunction lazy(fn) {\n    let struct;\n    return new Struct({\n        type: 'lazy',\n        schema: null,\n        *entries(value, ctx) {\n            struct ?? (struct = fn());\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a new struct based on an existing object struct, but excluding\n * specific properties.\n *\n * Like TypeScript's `Omit` utility.\n */\nfunction omit(struct, keys) {\n    const { schema } = struct;\n    const subschema = { ...schema };\n    for (const key of keys) {\n        delete subschema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Create a new struct based on an existing object struct, but with all of its\n * properties allowed to be `undefined`.\n *\n * Like TypeScript's `Partial` utility.\n */\nfunction partial(struct) {\n    const isStruct = struct instanceof Struct;\n    const schema = isStruct ? { ...struct.schema } : { ...struct };\n    for (const key in schema) {\n        schema[key] = optional(schema[key]);\n    }\n    if (isStruct && struct.type === 'type') {\n        return type(schema);\n    }\n    return object(schema);\n}\n/**\n * Create a new struct based on an existing object struct, but only including\n * specific properties.\n *\n * Like TypeScript's `Pick` utility.\n */\nfunction pick(struct, keys) {\n    const { schema } = struct;\n    const subschema = {};\n    for (const key of keys) {\n        subschema[key] = schema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Define a new struct type with a custom validation function.\n *\n * @deprecated This function has been renamed to `define`.\n */\nfunction struct(name, validator) {\n    console.warn('superstruct@0.11 - The `struct` helper has been renamed to `define`.');\n    return define(name, validator);\n}\n\n/**\n * Ensure that any value passes validation.\n */\nfunction any() {\n    return define('any', () => true);\n}\nfunction array(Element) {\n    return new Struct({\n        type: 'array',\n        schema: Element,\n        *entries(value) {\n            if (Element && Array.isArray(value)) {\n                for (const [i, v] of value.entries()) {\n                    yield [i, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return Array.isArray(value) ? value.slice() : value;\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array value, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a bigint.\n */\nfunction bigint() {\n    return define('bigint', (value) => {\n        return typeof value === 'bigint';\n    });\n}\n/**\n * Ensure that a value is a boolean.\n */\nfunction boolean() {\n    return define('boolean', (value) => {\n        return typeof value === 'boolean';\n    });\n}\n/**\n * Ensure that a value is a valid `Date`.\n *\n * Note: this also ensures that the value is *not* an invalid `Date` object,\n * which can occur when parsing a date fails but still returns a `Date`.\n */\nfunction date() {\n    return define('date', (value) => {\n        return ((value instanceof Date && !isNaN(value.getTime())) ||\n            `Expected a valid \\`Date\\` object, but received: ${print(value)}`);\n    });\n}\nfunction enums(values) {\n    const schema = {};\n    const description = values.map((v) => print(v)).join();\n    for (const key of values) {\n        schema[key] = key;\n    }\n    return new Struct({\n        type: 'enums',\n        schema,\n        validator(value) {\n            return (values.includes(value) ||\n                `Expected one of \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a function.\n */\nfunction func() {\n    return define('func', (value) => {\n        return (typeof value === 'function' ||\n            `Expected a function, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an instance of a specific class.\n */\nfunction instance(Class) {\n    return define('instance', (value) => {\n        return (value instanceof Class ||\n            `Expected a \\`${Class.name}\\` instance, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an integer.\n */\nfunction integer() {\n    return define('integer', (value) => {\n        return ((typeof value === 'number' && !isNaN(value) && Number.isInteger(value)) ||\n            `Expected an integer, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value matches all of a set of types.\n */\nfunction intersection(Structs) {\n    return new Struct({\n        type: 'intersection',\n        schema: null,\n        *entries(value, ctx) {\n            for (const S of Structs) {\n                yield* S.entries(value, ctx);\n            }\n        },\n        *validator(value, ctx) {\n            for (const S of Structs) {\n                yield* S.validator(value, ctx);\n            }\n        },\n        *refiner(value, ctx) {\n            for (const S of Structs) {\n                yield* S.refiner(value, ctx);\n            }\n        },\n    });\n}\nfunction literal(constant) {\n    const description = print(constant);\n    const t = typeof constant;\n    return new Struct({\n        type: 'literal',\n        schema: t === 'string' || t === 'number' || t === 'boolean' ? constant : null,\n        validator(value) {\n            return (value === constant ||\n                `Expected the literal \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\nfunction map(Key, Value) {\n    return new Struct({\n        type: 'map',\n        schema: null,\n        *entries(value) {\n            if (Key && Value && value instanceof Map) {\n                for (const [k, v] of value.entries()) {\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Map ? new Map(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Map ||\n                `Expected a \\`Map\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that no value ever passes validation.\n */\nfunction never() {\n    return define('never', () => false);\n}\n/**\n * Augment an existing struct to allow `null` values.\n */\nfunction nullable(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === null || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === null || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is a number.\n */\nfunction number() {\n    return define('number', (value) => {\n        return ((typeof value === 'number' && !isNaN(value)) ||\n            `Expected a number, but received: ${print(value)}`);\n    });\n}\nfunction object(schema) {\n    const knowns = schema ? Object.keys(schema) : [];\n    const Never = never();\n    return new Struct({\n        type: 'object',\n        schema: schema ? schema : null,\n        *entries(value) {\n            if (schema && isObject(value)) {\n                const unknowns = new Set(Object.keys(value));\n                for (const key of knowns) {\n                    unknowns.delete(key);\n                    yield [key, value[key], schema[key]];\n                }\n                for (const key of unknowns) {\n                    yield [key, value[key], Never];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Augment a struct to allow `undefined` values.\n */\nfunction optional(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === undefined || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is an object with keys and values of specific types, but\n * without ensuring any specific shape of properties.\n *\n * Like TypeScript's `Record` utility.\n */\nfunction record(Key, Value) {\n    return new Struct({\n        type: 'record',\n        schema: null,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k in value) {\n                    const v = value[k];\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a `RegExp`.\n *\n * Note: this does not test the value against the regular expression! For that\n * you need to use the `pattern()` refinement.\n */\nfunction regexp() {\n    return define('regexp', (value) => {\n        return value instanceof RegExp;\n    });\n}\nfunction set(Element) {\n    return new Struct({\n        type: 'set',\n        schema: null,\n        *entries(value) {\n            if (Element && value instanceof Set) {\n                for (const v of value) {\n                    yield [v, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Set ? new Set(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Set ||\n                `Expected a \\`Set\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a string.\n */\nfunction string() {\n    return define('string', (value) => {\n        return (typeof value === 'string' ||\n            `Expected a string, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is a tuple of a specific length, and that each of its\n * elements is of a specific type.\n */\nfunction tuple(Structs) {\n    const Never = never();\n    return new Struct({\n        type: 'tuple',\n        schema: null,\n        *entries(value) {\n            if (Array.isArray(value)) {\n                const length = Math.max(Structs.length, value.length);\n                for (let i = 0; i < length; i++) {\n                    yield [i, value[i], Structs[i] || Never];\n                }\n            }\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value has a set of known properties of specific types.\n *\n * Note: Unrecognized properties are allowed and untouched. This is similar to\n * how TypeScript's structural typing works.\n */\nfunction type(schema) {\n    const keys = Object.keys(schema);\n    return new Struct({\n        type: 'type',\n        schema,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k of keys) {\n                    yield [k, value[k], schema[k]];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Ensure that a value matches one of a set of types.\n */\nfunction union(Structs) {\n    const description = Structs.map((s) => s.type).join(' | ');\n    return new Struct({\n        type: 'union',\n        schema: null,\n        coercer(value) {\n            for (const S of Structs) {\n                const [error, coerced] = S.validate(value, { coerce: true });\n                if (!error) {\n                    return coerced;\n                }\n            }\n            return value;\n        },\n        validator(value, ctx) {\n            const failures = [];\n            for (const S of Structs) {\n                const [...tuples] = run(value, S, ctx);\n                const [first] = tuples;\n                if (!first[0]) {\n                    return [];\n                }\n                else {\n                    for (const [failure] of tuples) {\n                        if (failure) {\n                            failures.push(failure);\n                        }\n                    }\n                }\n            }\n            return [\n                `Expected the value to satisfy a union of \\`${description}\\`, but received: ${print(value)}`,\n                ...failures,\n            ];\n        },\n    });\n}\n/**\n * Ensure that any value passes validation, without widening its type to `any`.\n */\nfunction unknown() {\n    return define('unknown', () => true);\n}\n\n/**\n * Augment a `Struct` to add an additional coercion step to its input.\n *\n * This allows you to transform input data before validating it, to increase the\n * likelihood that it passes validation—for example for default values, parsing\n * different formats, etc.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction coerce(struct, condition, coercer) {\n    return new Struct({\n        ...struct,\n        coercer: (value, ctx) => {\n            return is(value, condition)\n                ? struct.coercer(coercer(value, ctx), ctx)\n                : struct.coercer(value, ctx);\n        },\n    });\n}\n/**\n * Augment a struct to replace `undefined` values with a default.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction defaulted(struct, fallback, options = {}) {\n    return coerce(struct, unknown(), (x) => {\n        const f = typeof fallback === 'function' ? fallback() : fallback;\n        if (x === undefined) {\n            return f;\n        }\n        if (!options.strict && isPlainObject(x) && isPlainObject(f)) {\n            const ret = { ...x };\n            let changed = false;\n            for (const key in f) {\n                if (ret[key] === undefined) {\n                    ret[key] = f[key];\n                    changed = true;\n                }\n            }\n            if (changed) {\n                return ret;\n            }\n        }\n        return x;\n    });\n}\n/**\n * Augment a struct to trim string inputs.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction trimmed(struct) {\n    return coerce(struct, string(), (x) => x.trim());\n}\n\n/**\n * Ensure that a string, array, map, or set is empty.\n */\nfunction empty(struct) {\n    return refine(struct, 'empty', (value) => {\n        const size = getSize(value);\n        return (size === 0 ||\n            `Expected an empty ${struct.type} but received one with a size of \\`${size}\\``);\n    });\n}\nfunction getSize(value) {\n    if (value instanceof Map || value instanceof Set) {\n        return value.size;\n    }\n    else {\n        return value.length;\n    }\n}\n/**\n * Ensure that a number or date is below a threshold.\n */\nfunction max(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'max', (value) => {\n        return exclusive\n            ? value < threshold\n            : value <= threshold ||\n                `Expected a ${struct.type} less than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a number or date is above a threshold.\n */\nfunction min(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'min', (value) => {\n        return exclusive\n            ? value > threshold\n            : value >= threshold ||\n                `Expected a ${struct.type} greater than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a string, array, map or set is not empty.\n */\nfunction nonempty(struct) {\n    return refine(struct, 'nonempty', (value) => {\n        const size = getSize(value);\n        return (size > 0 || `Expected a nonempty ${struct.type} but received an empty one`);\n    });\n}\n/**\n * Ensure that a string matches a regular expression.\n */\nfunction pattern(struct, regexp) {\n    return refine(struct, 'pattern', (value) => {\n        return (regexp.test(value) ||\n            `Expected a ${struct.type} matching \\`/${regexp.source}/\\` but received \"${value}\"`);\n    });\n}\n/**\n * Ensure that a string, array, number, date, map, or set has a size (or length, or time) between `min` and `max`.\n */\nfunction size(struct, min, max = min) {\n    const expected = `Expected a ${struct.type}`;\n    const of = min === max ? `of \\`${min}\\`` : `between \\`${min}\\` and \\`${max}\\``;\n    return refine(struct, 'size', (value) => {\n        if (typeof value === 'number' || value instanceof Date) {\n            return ((min <= value && value <= max) ||\n                `${expected} ${of} but received \\`${value}\\``);\n        }\n        else if (value instanceof Map || value instanceof Set) {\n            const { size } = value;\n            return ((min <= size && size <= max) ||\n                `${expected} with a size ${of} but received one with a size of \\`${size}\\``);\n        }\n        else {\n            const { length } = value;\n            return ((min <= length && length <= max) ||\n                `${expected} with a length ${of} but received one with a length of \\`${length}\\``);\n        }\n    });\n}\n/**\n * Augment a `Struct` to add an additional refinement to the validation.\n *\n * The refiner function is guaranteed to receive a value of the struct's type,\n * because the struct's existing validation will already have passed. This\n * allows you to layer additional validation on top of existing structs.\n */\nfunction refine(struct, name, refiner) {\n    return new Struct({\n        ...struct,\n        *refiner(value, ctx) {\n            yield* struct.refiner(value, ctx);\n            const result = refiner(value, ctx);\n            const failures = toFailures(result, ctx, struct, value);\n            for (const failure of failures) {\n                yield { ...failure, refinement: name };\n            }\n        },\n    });\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superstruct/dist/index.mjs\n");

/***/ })

};
;