# Aave-Only Lending Aggregator Deployment Guide

## Prerequisites

1. **Install Foundry** (if not already installed):
```bash
curl -L https://foundry.paradigm.xyz | bash
foundryup
```

2. **Get Avalanche Fuji Testnet AVAX**:
   - Visit [Avalanche Faucet](https://faucet.avax.network/)
   - Request testnet AVAX for your wallet

3. **Get Test Tokens**:
   - USDC Fuji: `******************************************`
   - WAVAX Fuji: `******************************************`

## Setup Environment

1. **Copy environment file**:
```bash
cp .env.example .env
```

2. **Edit `.env` file** with your private key:
```bash
# Replace with your actual private key (NEVER commit this)
PRIVATE_KEY=0x1234567890abcdef...
```

## Deployment Steps

### Step 1: Run Tests
```bash
# Run the Aave-only tests
forge test --match-contract AaveOnlyTest -vv

# Run all tests
forge test -vv
```

### Step 2: Deploy to Fuji Testnet
```bash
# Deploy the Aave-only aggregator
forge script script/DeployLendingAPYAggregator.s.sol \
  --rpc-url $AVALANCHE_FUJI_RPC \
  --broadcast \
  --verify

# Or using the environment variable
forge script script/DeployLendingAPYAggregator.s.sol \
  --rpc-url https://api.avax-test.network/ext/bc/C/rpc \
  --broadcast
```

### Step 3: Configure Assets
After deployment, note the contract address and run:
```bash
# Set the deployed contract address
export AGGREGATOR_ADDRESS=0x... # Your deployed contract address

# Add supported assets (run this script after deployment)
forge script script/ConfigureAssets.s.sol \
  --rpc-url $AVALANCHE_FUJI_RPC \
  --broadcast
```

## Testing the Deployed Contract

### Using Cast (Foundry CLI)

1. **Check supported assets**:
```bash
cast call $AGGREGATOR_ADDRESS "getSupportedAssets()" --rpc-url $AVALANCHE_FUJI_RPC
```

2. **Check if USDC is supported**:
```bash
cast call $AGGREGATOR_ADDRESS "supportedAssets(address)" ****************************************** --rpc-url $AVALANCHE_FUJI_RPC
```

3. **Get user position**:
```bash
cast call $AGGREGATOR_ADDRESS "getAggregatorUserPosition(address,address)" YOUR_ADDRESS ****************************************** --rpc-url $AVALANCHE_FUJI_RPC
```

### Testing Supply to Aave

1. **Get test USDC** (if you don't have any):
   - Use Aave Faucet or DEX on Fuji testnet

2. **Approve the aggregator to spend your USDC**:
```bash
cast send ****************************************** \
  "approve(address,uint256)" \
  $AGGREGATOR_ADDRESS \
  1000000000000000000 \
  --private-key $PRIVATE_KEY \
  --rpc-url $AVALANCHE_FUJI_RPC
```

3. **Supply USDC to Aave**:
```bash
cast send $AGGREGATOR_ADDRESS \
  "supplyToAave(address,uint256)" \
  ****************************************** \
  1000000000000000000 \
  --private-key $PRIVATE_KEY \
  --rpc-url $AVALANCHE_FUJI_RPC
```

## Frontend Configuration

After successful deployment, update the frontend configuration:

1. **Update contract address** in `ap-yieldz/app/blockchain/config/wagmi.ts`
2. **Update supported tokens** with Fuji testnet addresses
3. **Test the frontend** with the deployed contract

## Troubleshooting

### Common Issues:

1. **"Insufficient funds"**: Make sure you have enough AVAX for gas fees
2. **"Unsupported asset"**: Ensure the asset is added via `addSupportedAsset()`
3. **"Insufficient allowance"**: Approve the aggregator contract first
4. **RPC errors**: Try different RPC endpoints or wait and retry

### Useful Commands:

```bash
# Check your AVAX balance
cast balance YOUR_ADDRESS --rpc-url $AVALANCHE_FUJI_RPC

# Check USDC balance
cast call ****************************************** "balanceOf(address)" YOUR_ADDRESS --rpc-url $AVALANCHE_FUJI_RPC

# Check allowance
cast call ****************************************** "allowance(address,address)" YOUR_ADDRESS $AGGREGATOR_ADDRESS --rpc-url $AVALANCHE_FUJI_RPC
```

## Next Steps

Once Aave functionality is working:
1. Test all Aave operations (supply, borrow, withdraw, repay)
2. Integrate with frontend
3. Add Morpho cross-chain functionality
4. Deploy to mainnet
