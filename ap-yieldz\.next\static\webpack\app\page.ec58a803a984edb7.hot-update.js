"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts":
/*!********************************************!*\
  !*** ./app/blockchain/hooks/useAPYData.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAPYData: () => (/* binding */ useAPYData),\n/* harmony export */   useHistoricalAPY: () => (/* binding */ useHistoricalAPY)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/aaveAPI */ \"(app-pages-browser)/./app/services/aaveAPI.ts\");\n\n\n// For Aave-only integration, we'll use mock data for now\n// Real Aave API integration can be added later\n// const AAVE_API_URL = 'https://aave-api-v2.aave.com/data/liquidity/v2';\n// Mapping of asset symbols to their respective IDs on Aave V3\nconst AAVE_RESERVE_IDS = {\n    'USDC': '******************************************',\n    'USDT': '******************************************',\n    'WETH': '******************************************',\n    'WBTC': '******************************************'\n};\n// Aave-only APY data for Fuji testnet (mock data for demonstration)\nconst AAVE_ONLY_APY_DATA = [\n    {\n        asset: 'usdc',\n        symbol: 'USDC',\n        aaveSupplyAPY: 4.25,\n        aaveBorrowAPY: 5.15,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    },\n    {\n        asset: 'wavax',\n        symbol: 'WAVAX',\n        aaveSupplyAPY: 2.85,\n        aaveBorrowAPY: 4.25,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    },\n    {\n        asset: 'usdt',\n        symbol: 'USDT',\n        aaveSupplyAPY: 4.15,\n        aaveBorrowAPY: 5.25,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    }\n];\nfunction useAPYData() {\n    const [apyData, setApyData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAPYData.useEffect\": ()=>{\n            const fetchAPYData = {\n                \"useAPYData.useEffect.fetchAPYData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        console.log('Fetching live Aave APY data...');\n                        // Fetch live rates from Aave API\n                        const liveRates = await (0,_services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__.fetchLiveAaveRates)();\n                        console.log('Raw live rates from API:', liveRates);\n                        // Check if we got valid rates\n                        const hasValidRates = Object.values(liveRates).some({\n                            \"useAPYData.useEffect.fetchAPYData.hasValidRates\": (token)=>token.supplyAPY > 0 || token.borrowAPY > 0\n                        }[\"useAPYData.useEffect.fetchAPYData.hasValidRates\"]);\n                        if (!hasValidRates) {\n                            console.log('API returned zero rates, using fallback data');\n                            throw new Error('API returned invalid rates (all zeros)');\n                        }\n                        // Convert to our APYData format\n                        const liveAPYData = [\n                            {\n                                asset: 'usdc',\n                                symbol: 'USDC',\n                                aaveSupplyAPY: liveRates.USDC.supplyAPY,\n                                aaveBorrowAPY: liveRates.USDC.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            },\n                            {\n                                asset: 'wavax',\n                                symbol: 'WAVAX',\n                                aaveSupplyAPY: liveRates.WAVAX.supplyAPY,\n                                aaveBorrowAPY: liveRates.WAVAX.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            },\n                            {\n                                asset: 'usdt',\n                                symbol: 'USDT',\n                                aaveSupplyAPY: liveRates.USDT.supplyAPY,\n                                aaveBorrowAPY: liveRates.USDT.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            }\n                        ];\n                        setApyData(liveAPYData);\n                        setError(null);\n                        console.log('Live APY data loaded successfully:', liveAPYData);\n                    } catch (err) {\n                        console.error('Error loading live APY data:', err);\n                        console.log('Falling back to mock data...');\n                        // Fallback to mock data if API fails\n                        setApyData(AAVE_ONLY_APY_DATA);\n                        setError('Using fallback APY data - live rates unavailable');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useAPYData.useEffect.fetchAPYData\"];\n            fetchAPYData();\n            // Refresh data every 60 seconds for live rates\n            const interval = setInterval(fetchAPYData, 60000);\n            return ({\n                \"useAPYData.useEffect\": ()=>clearInterval(interval)\n            })[\"useAPYData.useEffect\"];\n        }\n    }[\"useAPYData.useEffect\"], []);\n    const getAPYForAsset = (asset)=>{\n        const result = apyData.find((data)=>data.asset === asset.toLowerCase() || data.symbol === asset.toUpperCase());\n        console.log(\"Getting APY for \".concat(asset, \":\"), result);\n        return result;\n    };\n    return {\n        apyData,\n        loading,\n        error,\n        getAPYForAsset,\n        refresh: async ()=>{\n            setLoading(true);\n            try {\n                console.log('Manually refreshing APY data...');\n                const liveRates = await (0,_services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__.fetchLiveAaveRates)();\n                const liveAPYData = [\n                    {\n                        asset: 'usdc',\n                        symbol: 'USDC',\n                        aaveSupplyAPY: liveRates.USDC.supplyAPY,\n                        aaveBorrowAPY: liveRates.USDC.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    },\n                    {\n                        asset: 'wavax',\n                        symbol: 'WAVAX',\n                        aaveSupplyAPY: liveRates.WAVAX.supplyAPY,\n                        aaveBorrowAPY: liveRates.WAVAX.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    },\n                    {\n                        asset: 'usdt',\n                        symbol: 'USDT',\n                        aaveSupplyAPY: liveRates.USDT.supplyAPY,\n                        aaveBorrowAPY: liveRates.USDT.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    }\n                ];\n                setApyData(liveAPYData);\n                setError(null);\n            } catch (error) {\n                console.error('Manual refresh failed:', error);\n                setApyData(AAVE_ONLY_APY_DATA);\n                setError('Refresh failed - using fallback data');\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n}\n// Historical APY data for charts\nfunction useHistoricalAPY(asset, protocol) {\n    let days = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useHistoricalAPY.useEffect\": ()=>{\n            const generateMockHistoricalData = {\n                \"useHistoricalAPY.useEffect.generateMockHistoricalData\": ()=>{\n                    // Get base APY from Aave-only data\n                    const assetData = AAVE_ONLY_APY_DATA.find({\n                        \"useHistoricalAPY.useEffect.generateMockHistoricalData.assetData\": (d)=>d.asset === asset.toLowerCase() || d.symbol === asset.toUpperCase()\n                    }[\"useHistoricalAPY.useEffect.generateMockHistoricalData.assetData\"]);\n                    if (!assetData) {\n                        setData([]);\n                        setLoading(false);\n                        return;\n                    }\n                    const baseSupplyAPY = protocol === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY;\n                    const baseBorrowAPY = protocol === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY;\n                    // Generate realistic historical data with small variations\n                    const historicalData = Array.from({\n                        length: days\n                    }).map({\n                        \"useHistoricalAPY.useEffect.generateMockHistoricalData.historicalData\": (_, i)=>{\n                            const date = new Date();\n                            date.setDate(date.getDate() - (days - i));\n                            // Add small random variations to make it look realistic\n                            const supplyVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation\n                            const borrowVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation\n                            return {\n                                date: date.toISOString().split('T')[0],\n                                supplyAPY: Math.max(0, baseSupplyAPY + supplyVariation),\n                                borrowAPY: Math.max(0, baseBorrowAPY + borrowVariation)\n                            };\n                        }\n                    }[\"useHistoricalAPY.useEffect.generateMockHistoricalData.historicalData\"]);\n                    setData(historicalData);\n                    setLoading(false);\n                }\n            }[\"useHistoricalAPY.useEffect.generateMockHistoricalData\"];\n            if (asset) {\n                generateMockHistoricalData();\n            }\n        }\n    }[\"useHistoricalAPY.useEffect\"], [\n        asset,\n        protocol,\n        days\n    ]);\n    return {\n        data,\n        loading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\n"));

/***/ })

});