"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_de_DE-P43L3PR7_js"],{

/***/ "(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/de_DE-P43L3PR7.js":
/*!********************************************************************!*\
  !*** ./node_modules/@rainbow-me/rainbowkit/dist/de_DE-P43L3PR7.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ de_DE_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/locales/de_DE.json\nvar de_DE_default = '{\\n  \"connect_wallet\": {\\n    \"label\": \"Wallet verbinden\",\\n    \"wrong_network\": {\\n      \"label\": \"Falsches Netzwerk\"\\n    }\\n  },\\n  \"intro\": {\\n    \"title\": \"Was ist ein Wallet?\",\\n    \"description\": \"Ein Wallet wird verwendet, um digitale Assets zu senden, empfangen, speichern und anzeigen. Es ist auch eine neue M\\xF6glichkeit, sich anzumelden, ohne auf jeder Website neue Konten und Passw\\xF6rter erstellen zu m\\xFCssen.\",\\n    \"digital_asset\": {\\n      \"title\": \"Ein Zuhause f\\xFCr Ihre digitalen Verm\\xF6genswerte\",\\n      \"description\": \"Wallets werden verwendet, um digitale Assets wie Ethereum und NFTs zu senden, empfangen, speichern und anzeigen.\"\\n    },\\n    \"login\": {\\n      \"title\": \"Eine neue M\\xF6glichkeit, sich anzumelden\",\\n      \"description\": \"Anstatt auf jeder Website neue Konten und Passw\\xF6rter zu erstellen, verbinden Sie einfach Ihr Wallet.\"\\n    },\\n    \"get\": {\\n      \"label\": \"Ein Wallet holen\"\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Mehr erfahren\"\\n    }\\n  },\\n  \"sign_in\": {\\n    \"label\": \"Verifizieren Sie Ihr Konto\",\\n    \"description\": \"Um die Verbindung abzuschlie\\xDFen, m\\xFCssen Sie eine Nachricht in Ihrem Wallet signieren, um zu verifizieren, dass Sie der Inhaber dieses Kontos sind.\",\\n    \"message\": {\\n      \"send\": \"Nachricht signieren\",\\n      \"preparing\": \"Nachricht wird vorbereitet...\",\\n      \"cancel\": \"Abbrechen\",\\n      \"preparing_error\": \"Fehler beim Vorbereiten der Nachricht, bitte erneut versuchen!\"\\n    },\\n    \"signature\": {\\n      \"waiting\": \"Warten auf Signatur...\",\\n      \"verifying\": \"Signatur wird \\xFCberpr\\xFCft...\",\\n      \"signing_error\": \"Fehler beim Signieren der Nachricht, bitte erneut versuchen!\",\\n      \"verifying_error\": \"Fehler bei der \\xDCberpr\\xFCfung der Signatur, bitte erneut versuchen!\",\\n      \"oops_error\": \"Oops, etwas ist schiefgelaufen!\"\\n    }\\n  },\\n  \"connect\": {\\n    \"label\": \"Verbinden\",\\n    \"title\": \"Ein Wallet verbinden\",\\n    \"new_to_ethereum\": {\\n      \"description\": \"Neu bei Ethereum-Wallets?\",\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Mehr erfahren\"\\n    },\\n    \"recent\": \"Zuletzt\",\\n    \"status\": {\\n      \"opening\": \"%{wallet} wird ge\\xF6ffnet...\",\\n      \"connecting\": \"Verbinden\",\\n      \"connect_mobile\": \"Fahren Sie in %{wallet} fort\",\\n      \"not_installed\": \"%{wallet} ist nicht installiert\",\\n      \"not_available\": \"%{wallet} ist nicht verf\\xFCgbar\",\\n      \"confirm\": \"Best\\xE4tigen Sie die Verbindung in der Erweiterung\",\\n      \"confirm_mobile\": \"Akzeptieren Sie die Verbindungsanfrage im Wallet\"\\n    },\\n    \"secondary_action\": {\\n      \"get\": {\\n        \"description\": \"Haben Sie kein %{wallet}?\",\\n        \"label\": \"HOLEN\"\\n      },\\n      \"install\": {\\n        \"label\": \"INSTALLIEREN\"\\n      },\\n      \"retry\": {\\n        \"label\": \"ERNEUT VERSUCHEN\"\\n      }\\n    },\\n    \"walletconnect\": {\\n      \"description\": {\\n        \"full\": \"Ben\\xF6tigen Sie das offizielle WalletConnect-Modul?\",\\n        \"compact\": \"Ben\\xF6tigen Sie das WalletConnect-Modul?\"\\n      },\\n      \"open\": {\\n        \"label\": \"\\xD6FFNEN\"\\n      }\\n    }\\n  },\\n  \"connect_scan\": {\\n    \"title\": \"Mit %{wallet} scannen\",\\n    \"fallback_title\": \"Mit Ihrem Telefon scannen\"\\n  },\\n  \"connector_group\": {\\n    \"installed\": \"Installiert\",\\n    \"recommended\": \"Empfohlen\",\\n    \"other\": \"Andere\",\\n    \"popular\": \"Beliebt\",\\n    \"more\": \"Mehr\",\\n    \"others\": \"Andere\"\\n  },\\n  \"get\": {\\n    \"title\": \"Ein Wallet holen\",\\n    \"action\": {\\n      \"label\": \"HOLEN\"\\n    },\\n    \"mobile\": {\\n      \"description\": \"Mobiles Wallet\"\\n    },\\n    \"extension\": {\\n      \"description\": \"Browser-Erweiterung\"\\n    },\\n    \"mobile_and_extension\": {\\n      \"description\": \"Mobiles Wallet und Erweiterung\"\\n    },\\n    \"mobile_and_desktop\": {\\n      \"description\": \"Mobile und Desktop Wallet\"\\n    },\\n    \"looking_for\": {\\n      \"title\": \"Nicht das, wonach Sie suchen?\",\\n      \"mobile\": {\\n        \"description\": \"W\\xE4hlen Sie auf dem Hauptbildschirm ein Wallet aus, um mit einem anderen Wallet-Anbieter zu beginnen.\"\\n      },\\n      \"desktop\": {\\n        \"compact_description\": \"W\\xE4hlen Sie auf dem Hauptbildschirm ein Wallet aus, um mit einem anderen Wallet-Anbieter zu beginnen.\",\\n        \"wide_description\": \"W\\xE4hlen Sie links ein Wallet aus, um mit einem anderen Wallet-Anbieter zu beginnen.\"\\n      }\\n    }\\n  },\\n  \"get_options\": {\\n    \"title\": \"Beginnen Sie mit %{wallet}\",\\n    \"short_title\": \"%{wallet} besorgen\",\\n    \"mobile\": {\\n      \"title\": \"%{wallet} f\\xFCr Mobilger\\xE4te\",\\n      \"description\": \"Verwenden Sie das mobile Wallet, um die Welt von Ethereum zu erkunden.\",\\n      \"download\": {\\n        \"label\": \"App herunterladen\"\\n      }\\n    },\\n    \"extension\": {\\n      \"title\": \"%{wallet} f\\xFCr %{browser}\",\\n      \"description\": \"Greifen Sie direkt von Ihrem bevorzugten Webbrowser auf Ihr Wallet zu.\",\\n      \"download\": {\\n        \"label\": \"Zu %{browser} hinzuf\\xFCgen\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"title\": \"%{wallet} f\\xFCr %{platform}\",\\n      \"description\": \"Greifen Sie nativ von Ihrem leistungsstarken Desktop auf Ihr Wallet zu.\",\\n      \"download\": {\\n        \"label\": \"Hinzuf\\xFCgen zu %{platform}\"\\n      }\\n    }\\n  },\\n  \"get_mobile\": {\\n    \"title\": \"%{wallet} installieren\",\\n    \"description\": \"Scannen Sie mit Ihrem Telefon, um auf iOS oder Android herunterzuladen\",\\n    \"continue\": {\\n      \"label\": \"Fortfahren\"\\n    }\\n  },\\n  \"get_instructions\": {\\n    \"mobile\": {\\n      \"connect\": {\\n        \"label\": \"Verbinden\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    },\\n    \"extension\": {\\n      \"refresh\": {\\n        \"label\": \"Aktualisieren\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"connect\": {\\n        \"label\": \"Verbinden\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    }\\n  },\\n  \"chains\": {\\n    \"title\": \"Netzwerke wechseln\",\\n    \"wrong_network\": \"Falsches Netzwerk erkannt, wechseln oder trennen Sie die Verbindung, um fortzufahren.\",\\n    \"confirm\": \"Im Wallet best\\xE4tigen\",\\n    \"switching_not_supported\": \"Ihr Wallet unterst\\xFCtzt das Wechseln von Netzwerken von %{appName} aus nicht. Versuchen Sie stattdessen, innerhalb Ihres Wallets die Netzwerke zu wechseln.\",\\n    \"switching_not_supported_fallback\": \"Ihr Wallet unterst\\xFCtzt das Wechseln von Netzwerken von dieser App aus nicht. Versuchen Sie stattdessen, innerhalb Ihres Wallets die Netzwerke zu wechseln.\",\\n    \"disconnect\": \"Trennen\",\\n    \"connected\": \"Verbunden\"\\n  },\\n  \"profile\": {\\n    \"disconnect\": {\\n      \"label\": \"Trennen\"\\n    },\\n    \"copy_address\": {\\n      \"label\": \"Adresse kopieren\",\\n      \"copied\": \"Kopiert!\"\\n    },\\n    \"explorer\": {\\n      \"label\": \"Mehr im Explorer ansehen\"\\n    },\\n    \"transactions\": {\\n      \"description\": \"%{appName}-Transaktionen werden hier angezeigt...\",\\n      \"description_fallback\": \"Ihre Transaktionen werden hier angezeigt...\",\\n      \"recent\": {\\n        \"title\": \"Neueste Transaktionen\"\\n      },\\n      \"clear\": {\\n        \"label\": \"Alles l\\xF6schen\"\\n      }\\n    }\\n  },\\n  \"wallet_connectors\": {\\n    \"argent\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"F\\xFCgen Sie Argent zu Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Argent-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein Wallet und einen Benutzernamen oder importieren Sie ein bestehendes Wallet.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Schaltfl\\xE4che QR-Scan\"\\n        }\\n      }\\n    },\\n    \"berasig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die BeraSig-Erweiterung\",\\n          \"description\": \"Wir empfehlen, BeraSig an die Taskleiste anzuheften, um leichter auf Ihre Brieftasche zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie eine Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"best\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Best Wallet-App\",\\n          \"description\": \"F\\xFCgen Sie die Best Wallet zu Ihrem Startbildschirm hinzu, um schneller auf Ihre Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"bifrost\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Bifrost Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Bifrost Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen oder importieren Sie ein Wallet mit Ihrer Wiederherstellungsphrase.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"bitget\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Bitget Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Bitget Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Bitget Wallet in Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Bitget Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"bitski\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Bitski in Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Bitski-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"bitverse\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Bitverse Wallet App\",\\n          \"description\": \"F\\xFCgen Sie die Bitverse Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"bloom\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Bloom Wallet-App\",\\n          \"description\": \"Wir empfehlen, Bloom Wallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen oder importieren Sie ein Wallet mit Ihrer Wiederherstellungsphrase.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nachdem Sie ein Wallet haben, klicken Sie auf \\u201EVerbinden\\u201C, um \\xFCber Bloom eine Verbindung herzustellen. Eine Verbindungsmeldung in der App wird angezeigt, um die Verbindung zu best\\xE4tigen.\",\\n          \"title\": \"Klicken Sie auf Verbinden\"\\n        }\\n      }\\n    },\\n    \"bybit\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Bybit auf Ihrem Startbildschirm abzulegen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Bybit App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und heften Sie Bybit Wallet f\\xFCr einfachen Zugriff an.\",\\n          \"title\": \"Installieren Sie die Bybit Wallet Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie das Bybit Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"binance\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Binance auf Ihrem Startbildschirm abzulegen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Binance App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"coin98\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Coin98 Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Coin98 Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und heften Sie die Coin98 Wallet an, um einen einfachen Zugriff zu erm\\xF6glichen.\",\\n          \"title\": \"Installieren Sie die Coin98 Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie die Coin98 Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"coinbase\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Coinbase Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Coinbase Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet problemlos mit der Cloud-Backup-Funktion sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Coinbase Wallet in Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Coinbase Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"compass\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Compass Wallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Compass Wallet Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"core\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Core auf Ihren Startbildschirm zu legen, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Core-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Core an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Core-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"fox\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, FoxWallet auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\",\\n          \"title\": \"\\xD6ffnen Sie die FoxWallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"frontier\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Frontier Wallet auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\",\\n          \"title\": \"\\xD6ffnen Sie die Frontier Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Frontier Wallet an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Frontier Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"im_token\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die imToken-App\",\\n          \"description\": \"Platzieren Sie die imToken-App auf Ihrem Startbildschirm f\\xFCr schnelleren Zugriff auf Ihr Wallet.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das Scanner-Symbol in der oberen rechten Ecke\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"iopay\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, ioPay auf Ihrem Startbildschirm abzulegen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die ioPay App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"kaikas\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Kaikas an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Kaikas Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kaikas App\",\\n          \"description\": \"Legen Sie die Kaikas App auf Ihrem Startbildschirm ab, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das Scanner-Symbol in der oberen rechten Ecke\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"kaia\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Kaia an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Kaia Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kaia-App\",\\n          \"description\": \"Legen Sie die Kaia-App auf Ihren Startbildschirm f\\xFCr schnelleren Zugriff auf Ihre Wallet.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das Scanner-Symbol in der oberen rechten Ecke\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"kraken\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kraken Wallet App\",\\n          \"description\": \"F\\xFCgen Sie die Kraken Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"kresus\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kresus Wallet App\",\\n          \"description\": \"F\\xFCgen Sie die Kresus Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"magicEden\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Magic Eden Erweiterung\",\\n          \"description\": \"Wir empfehlen, Magic Eden an Ihre Taskleiste zu heften, um einfacher auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihren geheimen Wiederherstellungssatz mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"metamask\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die MetaMask-App\",\\n          \"description\": \"Wir empfehlen, MetaMask auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die MetaMask-Erweiterung\",\\n          \"description\": \"Wir empfehlen, MetaMask an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"nestwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die NestWallet Erweiterung\",\\n          \"description\": \"Wir empfehlen, die NestWallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"okx\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die OKX Wallet-App\",\\n          \"description\": \"Wir empfehlen, OKX Wallet auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die OKX Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, OKX Wallet an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"omni\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Omni-App\",\\n          \"description\": \"F\\xFCgen Sie Omni Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"1inch\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Legen Sie die 1inch Wallet auf Ihrem Startbildschirm ab, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die 1inch Wallet App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein Wallet und einen Benutzernamen oder importieren Sie ein bestehendes Wallet.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Schaltfl\\xE4che QR-Scan\"\\n        }\\n      }\\n    },\\n    \"token_pocket\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die TokenPocket-App\",\\n          \"description\": \"Wir empfehlen, TokenPocket auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die TokenPocket-Erweiterung\",\\n          \"description\": \"Wir empfehlen, TokenPocket an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"trust\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Trust Wallet-App\",\\n          \"description\": \"Platzieren Sie Trust Wallet auf Ihrem Startbildschirm f\\xFCr schnelleren Zugriff auf Ihr Wallet.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf WalletConnect in den Einstellungen\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Trust Wallet-Erweiterung\",\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und pinnen Sie Trust Wallet f\\xFCr einfachen Zugriff.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Trust Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"uniswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Uniswap-App\",\\n          \"description\": \"F\\xFCgen Sie Uniswap Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"zerion\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Zerion-App\",\\n          \"description\": \"Wir empfehlen, Zerion auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Zerion-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Zerion an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"rainbow\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Rainbow-App\",\\n          \"description\": \"Wir empfehlen, Rainbow auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"enkrypt\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Enkrypt Wallet an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\",\\n          \"title\": \"Installieren Sie die Enkrypt Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"frame\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Frame an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\",\\n          \"title\": \"Installieren Sie Frame und die zugeh\\xF6rige Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"one_key\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die OneKey Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, die OneKey Wallet an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"paraswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die ParaSwap-App\",\\n          \"description\": \"F\\xFCgen Sie die ParaSwap Wallet zu Ihrem Startbildschirm hinzu, um schneller auf Ihre Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"phantom\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Phantom-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Phantom an Ihre Taskleiste anzuheften, um leichteren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihren geheimen Wiederherstellungssatz mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"rabby\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Rabby-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Rabby an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"ronin\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Ronin Wallet Ihrem Startbildschirm hinzuzuf\\xFCgen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Ronin Wallet App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Ronin Wallet an Ihre Taskleiste zu heften, um schneller darauf zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Ronin Wallet Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"ramper\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Ramper Erweiterung\",\\n          \"description\": \"Wir empfehlen, Ramper an Ihre Taskleiste zu heften, um einfacher darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie eine Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"safeheron\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Core-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Safeheron an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"taho\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Taho-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Taho an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"wigwam\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Wigwam-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Wigwam an Ihre Taskleiste anzuheften, um einen schnelleren Zugriff auf Ihr Wallet zu erm\\xF6glichen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"talisman\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Talisman-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Talisman an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder Importieren Sie eine Ethereum-Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihre Wiederherstellungsphrase mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"xdefi\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die XDEFI Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, die XDEFI Wallet an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"zeal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Zeal App\",\\n          \"description\": \"F\\xFCgen Sie die Zeal Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Zeal-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Zeal an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"safepal\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die SafePal Wallet-Erweiterung\",\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und heften Sie SafePal Wallet f\\xFCr einfachen Zugriff an.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie die SafePal Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die SafePal Wallet-App\",\\n          \"description\": \"Platzieren Sie SafePal Wallet auf Ihrem Startbildschirm, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf WalletConnect in den Einstellungen\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"desig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Desig-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Desig an Ihre Taskleiste anzuheften, um leichteren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie eine Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"subwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die SubWallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, SubWallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihre Wiederherstellungsphrase mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die SubWallet-App\",\\n          \"description\": \"Wir empfehlen, SubWallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"clv\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die CLV Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, CLV Wallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die CLV Wallet-App\",\\n          \"description\": \"Wir empfehlen, CLV Wallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"okto\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Okto-App\",\\n          \"description\": \"F\\xFCgen Sie Okto Ihrem Startbildschirm hinzu, um schnellen Zugriff zu erhalten\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie ein MPC-Wallet\",\\n          \"description\": \"Erstellen Sie ein Konto und generieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf WalletConnect in den Einstellungen\",\\n          \"description\": \"Tippen Sie auf das QR-Scan-Symbol oben rechts und best\\xE4tigen Sie die Aufforderung zum Verbinden.\"\\n        }\\n      }\\n    },\\n    \"ledger\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Ledger Live-App\",\\n          \"description\": \"Wir empfehlen, Ledger Live auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Richten Sie Ihr Ledger ein\",\\n          \"description\": \"Richten Sie ein neues Ledger ein oder verbinden Sie sich mit einem bestehenden.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Verbinden\",\\n          \"description\": \"Eine Verbindungsmeldung erscheint, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Ledger Live-App\",\\n          \"description\": \"Wir empfehlen, Ledger Live auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Richten Sie Ihr Ledger ein\",\\n          \"description\": \"Sie k\\xF6nnen entweder mit der Desktop-App synchronisieren oder Ihr Ledger verbinden.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Code scannen\",\\n          \"description\": \"Tippen Sie auf WalletConnect und wechseln Sie dann zum Scanner. Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"valora\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Valora-App\",\\n          \"description\": \"Wir empfehlen, Valora f\\xFCr einen schnelleren Zugriff auf Ihrem Startbildschirm zu platzieren.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie eine Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"gate\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Gate-App\",\\n          \"description\": \"Wir empfehlen, Gate auf Ihrem Startbildschirm abzulegen, um schnellen Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Gate-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Gate in Ihre Taskleiste zu heften, um leichteren Zugriff auf Ihr Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihren geheimen Wiederherstellungssatz mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"xportal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Legen Sie xPortal auf Ihrem Startbildschirm ab, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die xPortal-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Schaltfl\\xE4che QR-Scan\"\\n        }\\n      }\\n    },\\n    \"mew\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, MEW Wallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die MEW Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet problemlos mit der Cloud-Backup-Funktion sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      }\\n    }\\n  },\\n  \"zilpay\": {\\n    \"qr_code\": {\\n      \"step1\": {\\n        \"title\": \"\\xD6ffne die ZilPay-App\",\\n        \"description\": \"F\\xFCge ZilPay zu deinem Startbildschirm hinzu, um schneller auf dein Wallet zuzugreifen.\"\\n      },\\n      \"step2\": {\\n        \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n        \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n      },\\n      \"step3\": {\\n        \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n        \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n      }\\n    }\\n  }\\n}\\n';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/de_DE-P43L3PR7.js\n"));

/***/ })

}]);