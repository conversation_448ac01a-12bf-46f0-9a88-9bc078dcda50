# Aave Operations Testing Guide

## Prerequisites
- Contract deployed via Remix (see REMIX_DEPLOYMENT_GUIDE.md)
- MetaMask connected to Avalanche Fuji
- Test AVAX and USDC tokens

## Test Sequence

### 1. Initial Setup Verification

#### Check Contract State
```solidity
// In Remix, call these functions:
getSupportedAssets() // Should return [USDC_ADDRESS, WAVAX_ADDRESS]
supportedAssets(******************************************) // Should return true for USDC
```

#### Check Your Token Balances
Visit [Fuji Snowtrace](https://testnet.snowtrace.io/) and check:
- Your AVAX balance (for gas)
- Your USDC balance (for testing)

### 2. Test Supply Operation

#### Step 2.1: Approve Contract
Before supplying, approve the contract to spend your USDC:

**Via Snowtrace:**
1. Go to USDC contract: `******************************************`
2. Go to "Write Contract" tab
3. Connect your wallet
4. Call `approve`:
   - `spender`: Your deployed contract address
   - `amount`: `10000000` (10 USDC with 6 decimals)

**Via Remix (if you have USDC contract):**
```solidity
approve(YOUR_CONTRACT_ADDRESS, 10000000)
```

#### Step 2.2: Supply to Aave
```solidity
// In your deployed contract in Remix:
supplyToAave(
    "******************************************", // USDC address
    "1000000" // 1 USDC (6 decimals)
)
```

#### Step 2.3: Verify Supply
```solidity
getUserPosition(YOUR_ADDRESS, "******************************************")
// Should return: [1000000, 0, timestamp] (supplied, borrowed, lastUpdate)
```

### 3. Test Borrow Operation

#### Step 3.1: Supply Collateral First
You need collateral to borrow. Supply WAVAX as collateral:

```solidity
// First approve WAVAX
// Then supply WAVAX
supplyToAave(
    "******************************************", // WAVAX address
    "1000000000000000000" // 1 WAVAX (18 decimals)
)
```

#### Step 3.2: Borrow USDC
```solidity
borrowFromAave(
    "******************************************", // USDC address
    "500000" // 0.5 USDC (6 decimals)
)
```

#### Step 3.3: Verify Borrow
```solidity
getUserPosition(YOUR_ADDRESS, "******************************************")
// Should show borrowed amount in the second value
```

### 4. Test Withdraw Operation

#### Step 4.1: Withdraw Supplied Assets
```solidity
withdrawFromAave(
    "******************************************", // USDC address
    "500000" // 0.5 USDC (6 decimals)
)
```

#### Step 4.2: Verify Withdrawal
```solidity
getUserPosition(YOUR_ADDRESS, "******************************************")
// Supplied amount should decrease
```

### 5. Test Repay Operation

#### Step 5.1: Approve for Repayment
Approve the contract to spend USDC for repayment:
```solidity
// Via USDC contract
approve(YOUR_CONTRACT_ADDRESS, 1000000) // 1 USDC
```

#### Step 5.2: Repay Debt
```solidity
repayToAave(
    "******************************************", // USDC address
    "500000" // 0.5 USDC (6 decimals)
)
```

#### Step 5.3: Verify Repayment
```solidity
getUserPosition(YOUR_ADDRESS, "******************************************")
// Borrowed amount should decrease or become 0
```

## Expected Results

### Successful Supply
- Transaction succeeds
- `SupplyExecuted` event emitted
- `getUserPosition` shows increased supplied amount
- Your token balance decreases
- You receive aTokens in your wallet

### Successful Borrow
- Transaction succeeds
- `BorrowExecuted` event emitted
- `getUserPosition` shows borrowed amount
- Your token balance increases
- You have debt tokens in your wallet

### Successful Withdraw
- Transaction succeeds
- `WithdrawExecuted` event emitted
- `getUserPosition` shows decreased supplied amount
- Your token balance increases
- aTokens are burned

### Successful Repay
- Transaction succeeds
- `RepayExecuted` event emitted
- `getUserPosition` shows decreased borrowed amount
- Your token balance decreases
- Debt tokens are burned

## Common Issues and Solutions

### Issue: "Insufficient allowance"
**Solution**: Approve the contract to spend your tokens first

### Issue: "Unsupported asset"
**Solution**: Ensure the asset was added via `addSupportedAsset`

### Issue: "Transfer amount exceeds balance"
**Solution**: Check your token balance, get more test tokens

### Issue: "Borrow failed"
**Solution**: 
- Ensure you have sufficient collateral
- Check your health factor
- Try borrowing a smaller amount

### Issue: "Withdraw failed"
**Solution**:
- You might have borrowed against this collateral
- Try withdrawing a smaller amount
- Repay some debt first

## Monitoring Tools

### Check Aave Position Directly
Visit [Aave Fuji App](https://app.aave.com/?marketName=proto_avalanche_v3) to see your positions directly in Aave.

### Check Transactions
Monitor your transactions on [Fuji Snowtrace](https://testnet.snowtrace.io/) to see:
- Gas usage
- Event logs
- Function calls
- Token transfers

### Check Token Balances
Look for these tokens in your wallet:
- **aTokens**: Represent your supplied assets (e.g., aUSDC)
- **Debt Tokens**: Represent your borrowed assets (e.g., variableDebtUSDC)

## Advanced Testing

### Test Edge Cases
1. **Supply 0 amount**: Should revert
2. **Borrow without collateral**: Should revert
3. **Withdraw more than supplied**: Should revert
4. **Repay more than owed**: Should work (repays only what's owed)

### Test Multiple Assets
1. Supply both USDC and WAVAX
2. Borrow against multiple collaterals
3. Test cross-asset operations

### Test Position Tracking
1. Verify position updates after each operation
2. Check timestamp updates
3. Test with multiple users

## Success Criteria

✅ **Contract deployed successfully**
✅ **Assets added and verified**
✅ **Supply operation works**
✅ **Borrow operation works**
✅ **Withdraw operation works**
✅ **Repay operation works**
✅ **Position tracking accurate**
✅ **Events emitted correctly**

Once all tests pass, you're ready to integrate with the frontend and test the complete user experience!
