"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_three-dots_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   threeDotsSvg: () => (/* binding */ threeDotsSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst threeDotsSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"14\" height=\"15\" viewBox=\"0 0 14 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n  <path d=\"M7 3.71875C6.0335 3.71875 5.25 2.93525 5.25 1.96875C5.25 1.00225 6.0335 0.21875 7 0.21875C7.9665 0.21875 8.75 1.00225 8.75 1.96875C8.75 2.93525 7.9665 3.71875 7 3.71875Z\" fill=\"#949E9E\"/>\n  <path d=\"M7 8.96875C6.0335 8.96875 5.25 8.18525 5.25 7.21875C5.25 6.25225 6.0335 5.46875 7 5.46875C7.9665 5.46875 8.75 6.25225 8.75 7.21875C8.75 8.18525 7.9665 8.96875 7 8.96875Z\" fill=\"#949E9E\"/>\n  <path d=\"M5.25 12.4688C5.25 13.4352 6.0335 14.2187 7 14.2187C7.9665 14.2187 8.75 13.4352 8.75 12.4688C8.75 11.5023 7.9665 10.7188 7 10.7188C6.0335 10.7188 5.25 11.5023 5.25 12.4688Z\" fill=\"#949E9E\"/>\n</svg>`;\n//# sourceMappingURL=three-dots.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3RocmVlLWRvdHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIscUJBQXFCLHdDQUFHO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHJlb3duXFxhcHBraXQtdWlcXGRpc3RcXGVzbVxcc3JjXFxhc3NldHNcXHN2Z1xcdGhyZWUtZG90cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IHRocmVlRG90c1N2ZyA9IHN2ZyBgPHN2ZyB3aWR0aD1cIjE0XCIgaGVpZ2h0PVwiMTVcIiB2aWV3Qm94PVwiMCAwIDE0IDE1XCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XG4gIDxwYXRoIGQ9XCJNNyAzLjcxODc1QzYuMDMzNSAzLjcxODc1IDUuMjUgMi45MzUyNSA1LjI1IDEuOTY4NzVDNS4yNSAxLjAwMjI1IDYuMDMzNSAwLjIxODc1IDcgMC4yMTg3NUM3Ljk2NjUgMC4yMTg3NSA4Ljc1IDEuMDAyMjUgOC43NSAxLjk2ODc1QzguNzUgMi45MzUyNSA3Ljk2NjUgMy43MTg3NSA3IDMuNzE4NzVaXCIgZmlsbD1cIiM5NDlFOUVcIi8+XG4gIDxwYXRoIGQ9XCJNNyA4Ljk2ODc1QzYuMDMzNSA4Ljk2ODc1IDUuMjUgOC4xODUyNSA1LjI1IDcuMjE4NzVDNS4yNSA2LjI1MjI1IDYuMDMzNSA1LjQ2ODc1IDcgNS40Njg3NUM3Ljk2NjUgNS40Njg3NSA4Ljc1IDYuMjUyMjUgOC43NSA3LjIxODc1QzguNzUgOC4xODUyNSA3Ljk2NjUgOC45Njg3NSA3IDguOTY4NzVaXCIgZmlsbD1cIiM5NDlFOUVcIi8+XG4gIDxwYXRoIGQ9XCJNNS4yNSAxMi40Njg4QzUuMjUgMTMuNDM1MiA2LjAzMzUgMTQuMjE4NyA3IDE0LjIxODdDNy45NjY1IDE0LjIxODcgOC43NSAxMy40MzUyIDguNzUgMTIuNDY4OEM4Ljc1IDExLjUwMjMgNy45NjY1IDEwLjcxODggNyAxMC43MTg4QzYuMDMzNSAxMC43MTg4IDUuMjUgMTEuNTAyMyA1LjI1IDEyLjQ2ODhaXCIgZmlsbD1cIiM5NDlFOUVcIi8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dGhyZWUtZG90cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js\n"));

/***/ })

}]);