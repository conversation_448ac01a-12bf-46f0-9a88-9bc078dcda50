"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/proxy-compare";
exports.ids = ["vendor-chunks/proxy-compare"];
exports.modules = {

/***/ "(ssr)/./node_modules/proxy-compare/dist/index.modern.js":
/*!*********************************************************!*\
  !*** ./node_modules/proxy-compare/dist/index.modern.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   affectedToPathList: () => (/* binding */ w),\n/* harmony export */   createProxy: () => (/* binding */ a),\n/* harmony export */   getUntracked: () => (/* binding */ y),\n/* harmony export */   isChanged: () => (/* binding */ p),\n/* harmony export */   markToTrack: () => (/* binding */ h),\n/* harmony export */   replaceNewProxy: () => (/* binding */ O),\n/* harmony export */   trackMemo: () => (/* binding */ g)\n/* harmony export */ });\nconst e=Symbol(),t=Symbol(),r=\"a\",n=\"w\";let o=(e,t)=>new Proxy(e,t);const s=Object.getPrototypeOf,c=new WeakMap,l=e=>e&&(c.has(e)?c.get(e):s(e)===Object.prototype||s(e)===Array.prototype),f=e=>\"object\"==typeof e&&null!==e,i=e=>{if(Array.isArray(e))return Array.from(e);const t=Object.getOwnPropertyDescriptors(e);return Object.values(t).forEach(e=>{e.configurable=!0}),Object.create(s(e),t)},u=e=>e[t]||e,a=(s,c,f,p)=>{if(!l(s))return s;let g=p&&p.get(s);if(!g){const e=u(s);g=(e=>Object.values(Object.getOwnPropertyDescriptors(e)).some(e=>!e.configurable&&!e.writable))(e)?[e,i(e)]:[e],null==p||p.set(s,g)}const[y,h]=g;let w=f&&f.get(y);return w&&w[1].f===!!h||(w=((o,s)=>{const c={f:s};let l=!1;const f=(e,t)=>{if(!l){let s=c[r].get(o);if(s||(s={},c[r].set(o,s)),e===n)s[n]=!0;else{let r=s[e];r||(r=new Set,s[e]=r),r.add(t)}}},i={get:(e,n)=>n===t?o:(f(\"k\",n),a(Reflect.get(e,n),c[r],c.c,c.t)),has:(t,n)=>n===e?(l=!0,c[r].delete(o),!0):(f(\"h\",n),Reflect.has(t,n)),getOwnPropertyDescriptor:(e,t)=>(f(\"o\",t),Reflect.getOwnPropertyDescriptor(e,t)),ownKeys:e=>(f(n),Reflect.ownKeys(e))};return s&&(i.set=i.deleteProperty=()=>!1),[i,c]})(y,!!h),w[1].p=o(h||y,w[0]),f&&f.set(y,w)),w[1][r]=c,w[1].c=f,w[1].t=p,w[1].p},p=(e,t,r,o,s=Object.is)=>{if(s(e,t))return!1;if(!f(e)||!f(t))return!0;const c=r.get(u(e));if(!c)return!0;if(o){const r=o.get(e);if(r&&r.n===t)return r.g;o.set(e,{n:t,g:!1})}let l=null;try{for(const r of c.h||[])if(l=Reflect.has(e,r)!==Reflect.has(t,r),l)return l;if(!0===c[n]){if(l=((e,t)=>{const r=Reflect.ownKeys(e),n=Reflect.ownKeys(t);return r.length!==n.length||r.some((e,t)=>e!==n[t])})(e,t),l)return l}else for(const r of c.o||[])if(l=!!Reflect.getOwnPropertyDescriptor(e,r)!=!!Reflect.getOwnPropertyDescriptor(t,r),l)return l;for(const n of c.k||[])if(l=p(e[n],t[n],r,o,s),l)return l;return null===l&&(l=!0),l}finally{o&&o.set(e,{n:t,g:l})}},g=t=>!!l(t)&&e in t,y=e=>l(e)&&e[t]||null,h=(e,t=!0)=>{c.set(e,t)},w=(e,t,r)=>{const o=[],s=new WeakSet,c=(e,l)=>{if(s.has(e))return;f(e)&&s.add(e);const i=f(e)&&t.get(u(e));if(i){var a,p;if(null==(a=i.h)||a.forEach(e=>{const t=`:has(${String(e)})`;o.push(l?[...l,t]:[t])}),!0===i[n]){const e=\":ownKeys\";o.push(l?[...l,e]:[e])}else{var g;null==(g=i.o)||g.forEach(e=>{const t=`:hasOwn(${String(e)})`;o.push(l?[...l,t]:[t])})}null==(p=i.k)||p.forEach(t=>{r&&!(\"value\"in(Object.getOwnPropertyDescriptor(e,t)||{}))||c(e[t],l?[...l,t]:[t])})}else l&&o.push(l)};return c(e),o},O=e=>{o=e};\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/proxy-compare/dist/index.modern.js\n");

/***/ })

};
;