"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ContractSetup.tsx":
/*!******************************************!*\
  !*** ./app/components/ContractSetup.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContractSetup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useAccount.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useWriteContract.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useReadContract.js\");\n/* harmony import */ var _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../blockchain/abi/SimpleLendingAggregator */ \"(app-pages-browser)/./app/blockchain/abi/SimpleLendingAggregator.ts\");\n/* harmony import */ var _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../blockchain/config/wagmi */ \"(app-pages-browser)/./app/blockchain/config/wagmi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ContractSetup() {\n    _s();\n    const { address, isConnected } = (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.useAccount)();\n    const { writeContract } = (0,wagmi__WEBPACK_IMPORTED_MODULE_5__.useWriteContract)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const tokens = (0,_blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_3__.getCurrentTokens)();\n    // Check current supported assets\n    const { data: supportedAssets, refetch: refetchAssets } = (0,wagmi__WEBPACK_IMPORTED_MODULE_6__.useReadContract)({\n        address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_3__.LENDING_APY_AGGREGATOR_ADDRESS,\n        abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_2__.SIMPLE_LENDING_AGGREGATOR_ABI,\n        functionName: 'getSupportedAssets'\n    });\n    const currentAssets = supportedAssets || [];\n    const hasUSDC = currentAssets.includes(tokens.USDC);\n    const hasWAVAX = currentAssets.includes(tokens.WAVAX);\n    const addSupportedAsset = async (tokenAddress, tokenSymbol)=>{\n        if (!isConnected) {\n            setStatus('Please connect your wallet first');\n            return;\n        }\n        setIsLoading(true);\n        setStatus(\"Adding \".concat(tokenSymbol, \" as supported asset...\"));\n        try {\n            await writeContract({\n                address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_3__.LENDING_APY_AGGREGATOR_ADDRESS,\n                abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_2__.SIMPLE_LENDING_AGGREGATOR_ABI,\n                functionName: 'addSupportedAsset',\n                args: [\n                    tokenAddress\n                ]\n            });\n            setStatus(\"\".concat(tokenSymbol, \" added successfully! Please wait for transaction confirmation.\"));\n        } catch (error) {\n            console.error('Error adding supported asset:', error);\n            setStatus(\"Failed to add \".concat(tokenSymbol, \". Check console for details.\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const addAllAssets = async ()=>{\n        if (!isConnected) {\n            setStatus('Please connect your wallet first');\n            return;\n        }\n        setIsLoading(true);\n        setStatus('Adding all supported assets...');\n        try {\n            // Add USDC first\n            setStatus('Adding USDC...');\n            const usdcTx = await writeContract({\n                address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_3__.LENDING_APY_AGGREGATOR_ADDRESS,\n                abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_2__.SIMPLE_LENDING_AGGREGATOR_ABI,\n                functionName: 'addSupportedAsset',\n                args: [\n                    tokens.USDC\n                ]\n            });\n            console.log('USDC transaction:', usdcTx);\n            // Wait a bit between transactions\n            setStatus('USDC added! Adding WAVAX...');\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // Add WAVAX\n            const wavaxTx = await writeContract({\n                address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_3__.LENDING_APY_AGGREGATOR_ADDRESS,\n                abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_2__.SIMPLE_LENDING_AGGREGATOR_ABI,\n                functionName: 'addSupportedAsset',\n                args: [\n                    tokens.WAVAX\n                ]\n            });\n            console.log('WAVAX transaction:', wavaxTx);\n            setStatus('✅ All assets added successfully! Please wait for transaction confirmations, then refresh the page.');\n            // Refetch assets after a delay\n            setTimeout(()=>{\n                refetchAssets();\n            }, 5000);\n        } catch (error) {\n            console.error('Error adding assets:', error);\n            setStatus(\"❌ Failed to add assets: \".concat(error.message || 'Unknown error'));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!isConnected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 bg-yellow-50 border border-yellow-200 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                    children: \"Contract Setup Required\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-yellow-700\",\n                    children: \"Please connect your wallet to set up the contract with supported assets.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-blue-50 border border-blue-200 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-blue-800 mb-4\",\n                children: \"Contract Setup\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-700 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Contract Address:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_3__.LENDING_APY_AGGREGATOR_ADDRESS\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-700 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Connected as:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            address\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-blue-700 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Current Assets:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(hasUSDC ? 'bg-green-500' : 'bg-red-500')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"USDC \",\n                                                    hasUSDC ? '✅' : '❌'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(hasWAVAX ? 'bg-green-500' : 'bg-red-500')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"WAVAX \",\n                                                    hasWAVAX ? '✅' : '❌'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: [\n                                            \"Total: \",\n                                            currentAssets.length,\n                                            \" assets configured\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-blue-800\",\n                        children: \"Add Individual Assets:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addSupportedAsset(tokens.USDC, 'USDC'),\n                        disabled: isLoading,\n                        className: \"w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50\",\n                        children: [\n                            \"Add USDC (\",\n                            tokens.USDC,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addSupportedAsset(tokens.WAVAX, 'WAVAX'),\n                        disabled: isLoading,\n                        className: \"w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50\",\n                        children: [\n                            \"Add WAVAX (\",\n                            tokens.WAVAX,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    tokens.USDT && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>addSupportedAsset(tokens.USDT, 'USDT'),\n                        disabled: isLoading,\n                        className: \"w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50\",\n                        children: [\n                            \"Add USDT (\",\n                            tokens.USDT,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-blue-800 mb-2\",\n                        children: \"Quick Setup:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    hasUSDC && hasWAVAX ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 font-medium\",\n                                children: \"✅ All assets are configured!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-700 text-sm mt-1\",\n                                children: \"Your contract is ready to use. You can now supply and borrow assets.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: addAllAssets,\n                        disabled: isLoading,\n                        className: \"w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 font-medium\",\n                        children: isLoading ? 'Adding Assets...' : 'Add All Assets (Recommended)'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-100 rounded border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Status:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 34\n                        }, this),\n                        \" \",\n                        status\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 text-sm text-blue-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"font-medium mb-2\",\n                        children: \"Instructions:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Make sure you're the contract owner\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: 'Click \"Add All Assets\" for quick setup'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Wait for transaction confirmations\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Refresh the page to see the assets in the dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"You can then start using supply/borrow functions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\ContractSetup.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(ContractSetup, \"pPwdvLvV2scNlj600TDB4R/8eZ4=\", false, function() {\n    return [\n        wagmi__WEBPACK_IMPORTED_MODULE_4__.useAccount,\n        wagmi__WEBPACK_IMPORTED_MODULE_5__.useWriteContract,\n        wagmi__WEBPACK_IMPORTED_MODULE_6__.useReadContract\n    ];\n});\n_c = ContractSetup;\nvar _c;\n$RefreshReg$(_c, \"ContractSetup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ContractSetup.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Dashboard */ \"(app-pages-browser)/./app/components/Dashboard.tsx\");\n/* harmony import */ var _components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/APYComparisonTable */ \"(app-pages-browser)/./app/components/APYComparisonTable.tsx\");\n/* harmony import */ var _components_Portfolio__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Portfolio */ \"(app-pages-browser)/./app/components/Portfolio.tsx\");\n/* harmony import */ var _components_TradingModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/TradingModal */ \"(app-pages-browser)/./app/components/TradingModal.tsx\");\n/* harmony import */ var _components_ContractTest__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/ContractTest */ \"(app-pages-browser)/./app/components/ContractTest.tsx\");\n/* harmony import */ var _components_ContractSetup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ContractSetup */ \"(app-pages-browser)/./app/components/ContractSetup.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [tradingModal, setTradingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        asset: '',\n        action: 'supply'\n    });\n    const handleAssetSelect = (asset)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action: 'supply'\n        });\n    };\n    const handlePortfolioAction = (asset, action)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action\n        });\n    };\n    const closeTradingModal = ()=>{\n        setTradingModal((prev)=>({\n                ...prev,\n                isOpen: false\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-green-50 to-green-100 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n                                children: [\n                                    \"Find the Best \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-700\",\n                                        children: \"APY Rates\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Compare lending rates across Aave and Morpho protocols. Optimize your DeFi strategy with real-time APY data and seamless cross-chain bridging.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('dashboard'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'dashboard' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('markets'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'markets' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Markets\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('portfolio'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'portfolio' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractTest__WEBPACK_IMPORTED_MODULE_6__.ContractTest, {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractSetup__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_2__.Dashboard, {}, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 39\n                    }, this),\n                    activeTab === 'markets' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__.APYComparisonTable, {\n                        onAssetSelect: handleAssetSelect\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 37\n                    }, this),\n                    activeTab === 'portfolio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Portfolio__WEBPACK_IMPORTED_MODULE_4__.Portfolio, {\n                        onActionClick: handlePortfolioAction\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 39\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Alligator?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: \"The most comprehensive DeFi APY aggregator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Real-time APY Comparison\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Compare rates across Aave and Morpho protocols in real-time to maximize your yields.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Cross-chain Bridging\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Seamlessly bridge assets between Avalanche and Base networks for optimal positioning.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Portfolio Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Track your positions across protocols and optimize your DeFi portfolio in one place.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"\\uD83D\\uDC0A Alligator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Your DeFi yield optimization companion\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Docs\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"GitHub\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradingModal__WEBPACK_IMPORTED_MODULE_5__.TradingModal, {\n                isOpen: tradingModal.isOpen,\n                onClose: closeTradingModal,\n                selectedAsset: tradingModal.asset,\n                defaultAction: tradingModal.action\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"3Dqkv54y2l/XSZczm1G4HWmloIc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});