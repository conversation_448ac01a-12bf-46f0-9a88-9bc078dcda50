"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/APYComparisonTable.tsx":
/*!***********************************************!*\
  !*** ./app/components/APYComparisonTable.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APYComparisonTable: () => (/* binding */ APYComparisonTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../blockchain/hooks/useAPYData */ \"(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Alert */ \"(app-pages-browser)/./app/components/Alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ APYComparisonTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction APYComparisonTable(param) {\n    let { onAssetSelect } = param;\n    _s();\n    const { apyData, loading, error, refresh } = (0,_blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData)();\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asset');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    // Debug logging\n    console.log('APYComparisonTable - APY Data:', apyData);\n    console.log('APYComparisonTable - Loading:', loading);\n    console.log('APYComparisonTable - Error:', error);\n    const sortedData = [\n        ...apyData\n    ].sort((a, b)=>{\n        let valueA;\n        let valueB;\n        switch(sortBy){\n            case 'asset':\n                valueA = a.symbol;\n                valueB = b.symbol;\n                break;\n            case 'aaveSupply':\n                valueA = a.aaveSupplyAPY;\n                valueB = b.aaveSupplyAPY;\n                break;\n            case 'morphoSupply':\n                valueA = a.morphoSupplyAPY;\n                valueB = b.morphoSupplyAPY;\n                break;\n            case 'aaveBorrow':\n                valueA = a.aaveBorrowAPY;\n                valueB = b.aaveBorrowAPY;\n                break;\n            case 'morphoBorrow':\n                valueA = a.morphoBorrowAPY;\n                valueB = b.morphoBorrowAPY;\n                break;\n            default:\n                valueA = a.symbol;\n                valueB = b.symbol;\n        }\n        if (typeof valueA === 'string' && typeof valueB === 'string') {\n            return sortOrder === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);\n        }\n        const numA = Number(valueA);\n        const numB = Number(valueB);\n        return sortOrder === 'asc' ? numA - numB : numB - numA;\n    });\n    const handleSort = (column)=>{\n        if (sortBy === column) {\n            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortBy(column);\n            setSortOrder('desc'); // Default to desc for APY columns\n        }\n    };\n    const getBestRate = (aaveRate, morphoRate, type)=>{\n        if (type === 'supply') {\n            return aaveRate > morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        } else {\n            return aaveRate < morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        }\n    };\n    // Loading state\n    if (loading && apyData.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"APY Comparison\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingState, {\n                    message: \"Loading APY data...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Error loading APY data\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refresh,\n                        className: \"mt-4 bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Alert__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    type: \"warning\",\n                    message: error,\n                    dismissible: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"APY Comparison\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refresh,\n                                className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16,\n                                        className: loading ? 'animate-spin' : ''\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Compare lending and borrowing rates across protocols\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('asset'),\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveSupply'),\n                                        children: \"Aave Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoSupply'),\n                                        children: \"Morpho Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveBorrow'),\n                                        children: \"Aave Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoBorrow'),\n                                        children: \"Morpho Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Best Protocol\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: sortedData.map((asset)=>{\n                                const bestSupply = getBestRate(asset.aaveSupplyAPY, asset.morphoSupplyAPY, 'supply');\n                                const bestBorrow = getBestRate(asset.aaveBorrowAPY, asset.morphoBorrowAPY, 'borrow');\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: asset.symbol\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Supply:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestSupply.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestSupply.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Borrow:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestBorrow.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestBorrow.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onAssetSelect === null || onAssetSelect === void 0 ? void 0 : onAssetSelect(asset.symbol),\n                                                className: \"text-green-600 hover:text-green-900 flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Trade\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(APYComparisonTable, \"buV6zq4itXyq2gJ/L7TMuIIDt7Q=\", false, function() {\n    return [\n        _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData\n    ];\n});\n_c = APYComparisonTable;\nvar _c;\n$RefreshReg$(_c, \"APYComparisonTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/APYComparisonTable.tsx\n"));

/***/ })

});