"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/APYComparisonTable.tsx":
/*!***********************************************!*\
  !*** ./app/components/APYComparisonTable.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APYComparisonTable: () => (/* binding */ APYComparisonTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../blockchain/hooks/useAPYData */ \"(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Alert */ \"(app-pages-browser)/./app/components/Alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ APYComparisonTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction APYComparisonTable(param) {\n    let { onAssetSelect } = param;\n    _s();\n    const { apyData, loading, error, refresh } = (0,_blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData)();\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asset');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    // Debug logging\n    console.log('APYComparisonTable - APY Data:', apyData);\n    console.log('APYComparisonTable - Loading:', loading);\n    console.log('APYComparisonTable - Error:', error);\n    console.log('APYComparisonTable - Data length:', apyData.length);\n    // Log individual asset data\n    apyData.forEach((asset, index)=>{\n        console.log(\"Asset \".concat(index, \":\"), {\n            symbol: asset.symbol,\n            aaveSupplyAPY: asset.aaveSupplyAPY,\n            aaveBorrowAPY: asset.aaveBorrowAPY,\n            morphoSupplyAPY: asset.morphoSupplyAPY,\n            morphoBorrowAPY: asset.morphoBorrowAPY\n        });\n    });\n    const sortedData = [\n        ...apyData\n    ].sort((a, b)=>{\n        let valueA;\n        let valueB;\n        switch(sortBy){\n            case 'asset':\n                valueA = a.symbol;\n                valueB = b.symbol;\n                break;\n            case 'aaveSupply':\n                valueA = a.aaveSupplyAPY;\n                valueB = b.aaveSupplyAPY;\n                break;\n            case 'morphoSupply':\n                valueA = a.morphoSupplyAPY;\n                valueB = b.morphoSupplyAPY;\n                break;\n            case 'aaveBorrow':\n                valueA = a.aaveBorrowAPY;\n                valueB = b.aaveBorrowAPY;\n                break;\n            case 'morphoBorrow':\n                valueA = a.morphoBorrowAPY;\n                valueB = b.morphoBorrowAPY;\n                break;\n            default:\n                valueA = a.symbol;\n                valueB = b.symbol;\n        }\n        if (typeof valueA === 'string' && typeof valueB === 'string') {\n            return sortOrder === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);\n        }\n        const numA = Number(valueA);\n        const numB = Number(valueB);\n        return sortOrder === 'asc' ? numA - numB : numB - numA;\n    });\n    const handleSort = (column)=>{\n        if (sortBy === column) {\n            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortBy(column);\n            setSortOrder('desc'); // Default to desc for APY columns\n        }\n    };\n    const getBestRate = (aaveRate, morphoRate, type)=>{\n        if (type === 'supply') {\n            return aaveRate > morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        } else {\n            return aaveRate < morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        }\n    };\n    // Loading state\n    if (loading && apyData.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"APY Comparison\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingState, {\n                    message: \"Loading APY data...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Error loading APY data\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refresh,\n                        className: \"mt-4 bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    // Check if APY data is empty or all zeros\n    if (apyData.length === 0 || apyData.every((asset)=>asset.aaveSupplyAPY === 0 && asset.aaveBorrowAPY === 0 && asset.morphoSupplyAPY === 0 && asset.morphoBorrowAPY === 0)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"APY Comparison\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"No APY data available. This might be because:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-left text-gray-600 space-y-2 max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Contract has no supported assets configured\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• API is temporarily unavailable\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Network connection issues\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refresh,\n                        className: \"mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\",\n                        children: \"Retry Loading Data\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Alert__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    type: \"warning\",\n                    message: error,\n                    dismissible: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"APY Comparison\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refresh,\n                                className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16,\n                                        className: loading ? 'animate-spin' : ''\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Compare lending and borrowing rates across protocols\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('asset'),\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveSupply'),\n                                        children: \"Aave Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoSupply'),\n                                        children: \"Morpho Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveBorrow'),\n                                        children: \"Aave Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoBorrow'),\n                                        children: \"Morpho Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Best Protocol\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: sortedData.map((asset)=>{\n                                const bestSupply = getBestRate(asset.aaveSupplyAPY, asset.morphoSupplyAPY, 'supply');\n                                const bestBorrow = getBestRate(asset.aaveBorrowAPY, asset.morphoBorrowAPY, 'borrow');\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: asset.symbol\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Supply:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestSupply.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestSupply.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Borrow:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestBorrow.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestBorrow.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onAssetSelect === null || onAssetSelect === void 0 ? void 0 : onAssetSelect(asset.symbol),\n                                                className: \"text-green-600 hover:text-green-900 flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Trade\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(APYComparisonTable, \"buV6zq4itXyq2gJ/L7TMuIIDt7Q=\", false, function() {\n    return [\n        _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData\n    ];\n});\n_c = APYComparisonTable;\nvar _c;\n$RefreshReg$(_c, \"APYComparisonTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/APYComparisonTable.tsx\n"));

/***/ })

});