"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/APYComparisonTable.tsx":
/*!***********************************************!*\
  !*** ./app/components/APYComparisonTable.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APYComparisonTable: () => (/* binding */ APYComparisonTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../blockchain/hooks/useAPYData */ \"(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Alert */ \"(app-pages-browser)/./app/components/Alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ APYComparisonTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction APYComparisonTable(param) {\n    let { onAssetSelect } = param;\n    _s();\n    const { apyData, loading, error, refresh } = (0,_blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData)();\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asset');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    // Debug logging\n    console.log('APYComparisonTable - APY Data:', apyData);\n    console.log('APYComparisonTable - Loading:', loading);\n    console.log('APYComparisonTable - Error:', error);\n    const sortedData = [\n        ...apyData\n    ].sort((a, b)=>{\n        let valueA;\n        let valueB;\n        switch(sortBy){\n            case 'asset':\n                valueA = a.symbol;\n                valueB = b.symbol;\n                break;\n            case 'aaveSupply':\n                valueA = a.aaveSupplyAPY;\n                valueB = b.aaveSupplyAPY;\n                break;\n            case 'morphoSupply':\n                valueA = a.morphoSupplyAPY;\n                valueB = b.morphoSupplyAPY;\n                break;\n            case 'aaveBorrow':\n                valueA = a.aaveBorrowAPY;\n                valueB = b.aaveBorrowAPY;\n                break;\n            case 'morphoBorrow':\n                valueA = a.morphoBorrowAPY;\n                valueB = b.morphoBorrowAPY;\n                break;\n            default:\n                valueA = a.symbol;\n                valueB = b.symbol;\n        }\n        if (typeof valueA === 'string' && typeof valueB === 'string') {\n            return sortOrder === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);\n        }\n        const numA = Number(valueA);\n        const numB = Number(valueB);\n        return sortOrder === 'asc' ? numA - numB : numB - numA;\n    });\n    const handleSort = (column)=>{\n        if (sortBy === column) {\n            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortBy(column);\n            setSortOrder('desc'); // Default to desc for APY columns\n        }\n    };\n    const getBestRate = (aaveRate, morphoRate, type)=>{\n        if (type === 'supply') {\n            return aaveRate > morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        } else {\n            return aaveRate < morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        }\n    };\n    // Loading state\n    if (loading && apyData.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"APY Comparison\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingState, {\n                    message: \"Loading APY data...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Error loading APY data\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refresh,\n                        className: \"mt-4 bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    // Check if APY data is empty or all zeros\n    if (apyData.length === 0 || apyData.every((asset)=>asset.aaveSupplyAPY === 0 && asset.aaveBorrowAPY === 0 && asset.morphoSupplyAPY === 0 && asset.morphoBorrowAPY === 0)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"APY Comparison\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"No APY data available. This might be because:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-left text-gray-600 space-y-2 max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Contract has no supported assets configured\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• API is temporarily unavailable\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Network connection issues\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refresh,\n                        className: \"mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\",\n                        children: \"Retry Loading Data\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Alert__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    type: \"warning\",\n                    message: error,\n                    dismissible: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"APY Comparison\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refresh,\n                                className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16,\n                                        className: loading ? 'animate-spin' : ''\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Compare lending and borrowing rates across protocols\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('asset'),\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveSupply'),\n                                        children: \"Aave Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoSupply'),\n                                        children: \"Morpho Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveBorrow'),\n                                        children: \"Aave Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoBorrow'),\n                                        children: \"Morpho Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Best Protocol\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: sortedData.map((asset)=>{\n                                const bestSupply = getBestRate(asset.aaveSupplyAPY, asset.morphoSupplyAPY, 'supply');\n                                const bestBorrow = getBestRate(asset.aaveBorrowAPY, asset.morphoBorrowAPY, 'borrow');\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: asset.symbol\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Supply:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestSupply.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestSupply.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Borrow:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestBorrow.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestBorrow.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onAssetSelect === null || onAssetSelect === void 0 ? void 0 : onAssetSelect(asset.symbol),\n                                                className: \"text-green-600 hover:text-green-900 flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Trade\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(APYComparisonTable, \"buV6zq4itXyq2gJ/L7TMuIIDt7Q=\", false, function() {\n    return [\n        _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData\n    ];\n});\n_c = APYComparisonTable;\nvar _c;\n$RefreshReg$(_c, \"APYComparisonTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/APYComparisonTable.tsx\n"));

/***/ })

});