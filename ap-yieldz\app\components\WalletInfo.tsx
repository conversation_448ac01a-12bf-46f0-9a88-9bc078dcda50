'use client';

import { useState } from 'react';
import { useAccount, useConnect } from 'wagmi';
import { Wallet, ExternalLink, CheckCircle, AlertCircle, Info } from 'lucide-react';

export default function WalletInfo() {
  const { address, isConnected, connector } = useAccount();
  const { connectors } = useConnect();
  const [showDetails, setShowDetails] = useState(false);

  const walletInfo = [
    {
      name: 'Core Wallet',
      description: 'Native Avalanche wallet - Best for AVAX ecosystem',
      icon: '🔥',
      recommended: true,
      downloadUrl: 'https://core.app/',
      features: ['Native AVAX support', 'Built-in staking', 'Cross-chain bridge']
    },
    {
      name: 'MetaMask',
      description: 'Most popular Ethereum wallet',
      icon: '🦊',
      recommended: false,
      downloadUrl: 'https://metamask.io/',
      features: ['Wide compatibility', 'Browser extension', 'Mobile app']
    },
    {
      name: 'WalletConnect',
      description: 'Connect mobile wallets via QR code',
      icon: '📱',
      recommended: false,
      downloadUrl: 'https://walletconnect.com/',
      features: ['Mobile wallets', 'QR code connection', '100+ supported wallets']
    }
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Wallet size={20} className="text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-800">Wallet Information</h3>
        </div>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      </div>

      {/* Current Connection Status */}
      {isConnected ? (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle size={16} className="text-green-600" />
            <span className="font-medium text-green-800">Wallet Connected</span>
          </div>
          <div className="text-sm text-green-700 space-y-1">
            <p><strong>Wallet:</strong> {connector?.name || 'Unknown'}</p>
            <p><strong>Address:</strong> {address}</p>
            <p><strong>Network:</strong> Avalanche Fuji Testnet</p>
          </div>
        </div>
      ) : (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <AlertCircle size={16} className="text-yellow-600" />
            <span className="font-medium text-yellow-800">No Wallet Connected</span>
          </div>
          <p className="text-sm text-yellow-700">
            Connect a wallet to start using the DeFi features
          </p>
        </div>
      )}

      {/* Available Connectors */}
      <div className="mb-4">
        <h4 className="font-medium text-gray-800 mb-2">Available Wallets:</h4>
        <div className="space-y-2">
          {connectors.map((connector) => (
            <div key={connector.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span className="text-sm font-medium text-gray-700">{connector.name}</span>
              <span className="text-xs text-gray-500">
                {connector.id === 'injected' ? 'Browser Extension' : 'Available'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Wallet Information */}
      {showDetails && (
        <div className="space-y-4">
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-800 mb-3">Supported Wallets:</h4>
            <div className="space-y-4">
              {walletInfo.map((wallet) => (
                <div key={wallet.name} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{wallet.icon}</span>
                      <div>
                        <h5 className="font-medium text-gray-800 flex items-center space-x-2">
                          <span>{wallet.name}</span>
                          {wallet.recommended && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                              Recommended
                            </span>
                          )}
                        </h5>
                        <p className="text-sm text-gray-600">{wallet.description}</p>
                      </div>
                    </div>
                    <a
                      href={wallet.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm"
                    >
                      <span>Download</span>
                      <ExternalLink size={12} />
                    </a>
                  </div>
                  <div className="mt-2">
                    <p className="text-xs text-gray-500 mb-1">Features:</p>
                    <div className="flex flex-wrap gap-1">
                      {wallet.features.map((feature) => (
                        <span
                          key={feature}
                          className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Network Information */}
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-800 mb-3">Network Configuration:</h4>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-700">Network Name:</p>
                  <p className="text-gray-600">Avalanche Fuji C-Chain</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Chain ID:</p>
                  <p className="text-gray-600">43113</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">RPC URL:</p>
                  <p className="text-gray-600 break-all">https://api.avax-test.network/ext/bc/C/rpc</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Currency:</p>
                  <p className="text-gray-600">AVAX</p>
                </div>
              </div>
            </div>
          </div>

          {/* Help Section */}
          <div className="border-t pt-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Info size={16} className="text-blue-600" />
                <span className="font-medium text-blue-800">Need Help?</span>
              </div>
              <div className="text-sm text-blue-700 space-y-1">
                <p>• Make sure you're on Avalanche Fuji testnet</p>
                <p>• Get test AVAX from the <a href="https://faucet.avax.network/" target="_blank" className="underline">Avalanche Faucet</a></p>
                <p>• Core Wallet is recommended for the best Avalanche experience</p>
                <p>• Refresh the page if wallets don't appear</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
