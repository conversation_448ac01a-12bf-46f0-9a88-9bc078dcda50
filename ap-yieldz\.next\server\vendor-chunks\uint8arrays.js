"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uint8arrays";
exports.ids = ["vendor-chunks/uint8arrays"];
exports.modules = {

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/alloc.js":
/*!***************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/alloc.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alloc: () => (/* binding */ alloc),\n/* harmony export */   allocUnsafe: () => (/* binding */ allocUnsafe)\n/* harmony export */ });\nfunction alloc(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.alloc != null) {\n    return globalThis.Buffer.alloc(size);\n  }\n  return new Uint8Array(size);\n}\nfunction allocUnsafe(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null) {\n    return globalThis.Buffer.allocUnsafe(size);\n  }\n  return new Uint8Array(size);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy9hbGxvYy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFx1aW50OGFycmF5c1xcZXNtXFxzcmNcXGFsbG9jLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBhbGxvYyhzaXplID0gMCkge1xuICBpZiAoZ2xvYmFsVGhpcy5CdWZmZXIgIT0gbnVsbCAmJiBnbG9iYWxUaGlzLkJ1ZmZlci5hbGxvYyAhPSBudWxsKSB7XG4gICAgcmV0dXJuIGdsb2JhbFRoaXMuQnVmZmVyLmFsbG9jKHNpemUpO1xuICB9XG4gIHJldHVybiBuZXcgVWludDhBcnJheShzaXplKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBhbGxvY1Vuc2FmZShzaXplID0gMCkge1xuICBpZiAoZ2xvYmFsVGhpcy5CdWZmZXIgIT0gbnVsbCAmJiBnbG9iYWxUaGlzLkJ1ZmZlci5hbGxvY1Vuc2FmZSAhPSBudWxsKSB7XG4gICAgcmV0dXJuIGdsb2JhbFRoaXMuQnVmZmVyLmFsbG9jVW5zYWZlKHNpemUpO1xuICB9XG4gIHJldHVybiBuZXcgVWludDhBcnJheShzaXplKTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/alloc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/compare.js":
/*!*****************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/compare.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compare: () => (/* binding */ compare)\n/* harmony export */ });\nfunction compare(a, b) {\n  for (let i = 0; i < a.byteLength; i++) {\n    if (a[i] < b[i]) {\n      return -1;\n    }\n    if (a[i] > b[i]) {\n      return 1;\n    }\n  }\n  if (a.byteLength > b.byteLength) {\n    return 1;\n  }\n  if (a.byteLength < b.byteLength) {\n    return -1;\n  }\n  return 0;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy9jb21wYXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQLGtCQUFrQixrQkFBa0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXHVpbnQ4YXJyYXlzXFxlc21cXHNyY1xcY29tcGFyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gY29tcGFyZShhLCBiKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgYS5ieXRlTGVuZ3RoOyBpKyspIHtcbiAgICBpZiAoYVtpXSA8IGJbaV0pIHtcbiAgICAgIHJldHVybiAtMTtcbiAgICB9XG4gICAgaWYgKGFbaV0gPiBiW2ldKSB7XG4gICAgICByZXR1cm4gMTtcbiAgICB9XG4gIH1cbiAgaWYgKGEuYnl0ZUxlbmd0aCA+IGIuYnl0ZUxlbmd0aCkge1xuICAgIHJldHVybiAxO1xuICB9XG4gIGlmIChhLmJ5dGVMZW5ndGggPCBiLmJ5dGVMZW5ndGgpIHtcbiAgICByZXR1cm4gLTE7XG4gIH1cbiAgcmV0dXJuIDA7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/compare.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/concat.js":
/*!****************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/concat.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat)\n/* harmony export */ });\n/* harmony import */ var _alloc_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./alloc.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/alloc.js\");\n\nfunction concat(arrays, length) {\n  if (!length) {\n    length = arrays.reduce((acc, curr) => acc + curr.length, 0);\n  }\n  const output = (0,_alloc_js__WEBPACK_IMPORTED_MODULE_0__.allocUnsafe)(length);\n  let offset = 0;\n  for (const arr of arrays) {\n    output.set(arr, offset);\n    offset += arr.length;\n  }\n  return output;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy9jb25jYXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFDbEM7QUFDUDtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsc0RBQVc7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcdWludDhhcnJheXNcXGVzbVxcc3JjXFxjb25jYXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWxsb2NVbnNhZmUgfSBmcm9tICcuL2FsbG9jLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBjb25jYXQoYXJyYXlzLCBsZW5ndGgpIHtcbiAgaWYgKCFsZW5ndGgpIHtcbiAgICBsZW5ndGggPSBhcnJheXMucmVkdWNlKChhY2MsIGN1cnIpID0+IGFjYyArIGN1cnIubGVuZ3RoLCAwKTtcbiAgfVxuICBjb25zdCBvdXRwdXQgPSBhbGxvY1Vuc2FmZShsZW5ndGgpO1xuICBsZXQgb2Zmc2V0ID0gMDtcbiAgZm9yIChjb25zdCBhcnIgb2YgYXJyYXlzKSB7XG4gICAgb3V0cHV0LnNldChhcnIsIG9mZnNldCk7XG4gICAgb2Zmc2V0ICs9IGFyci5sZW5ndGg7XG4gIH1cbiAgcmV0dXJuIG91dHB1dDtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/concat.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/equals.js":
/*!****************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/equals.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   equals: () => (/* binding */ equals)\n/* harmony export */ });\nfunction equals(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (a.byteLength !== b.byteLength) {\n    return false;\n  }\n  for (let i = 0; i < a.byteLength; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n  return true;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy9lcXVhbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGtCQUFrQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcdWludDhhcnJheXNcXGVzbVxcc3JjXFxlcXVhbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGVxdWFscyhhLCBiKSB7XG4gIGlmIChhID09PSBiKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgaWYgKGEuYnl0ZUxlbmd0aCAhPT0gYi5ieXRlTGVuZ3RoKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgYS5ieXRlTGVuZ3RoOyBpKyspIHtcbiAgICBpZiAoYVtpXSAhPT0gYltpXSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/equals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/from-string.js":
/*!*********************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/from-string.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromString: () => (/* binding */ fromString)\n/* harmony export */ });\n/* harmony import */ var _util_bases_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/bases.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/util/bases.js\");\n\nfunction fromString(string, encoding = 'utf8') {\n  const base = _util_bases_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"][encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(string, 'utf8');\n  }\n  return base.decoder.decode(`${ base.prefix }${ string }`);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy9mcm9tLXN0cmluZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQztBQUM3QjtBQUNQLGVBQWUsc0RBQUs7QUFDcEI7QUFDQSw4Q0FBOEMsVUFBVTtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxhQUFhLEdBQUcsUUFBUTtBQUN6RCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFx1aW50OGFycmF5c1xcZXNtXFxzcmNcXGZyb20tc3RyaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBiYXNlcyBmcm9tICcuL3V0aWwvYmFzZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGZyb21TdHJpbmcoc3RyaW5nLCBlbmNvZGluZyA9ICd1dGY4Jykge1xuICBjb25zdCBiYXNlID0gYmFzZXNbZW5jb2RpbmddO1xuICBpZiAoIWJhc2UpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIGVuY29kaW5nIFwiJHsgZW5jb2RpbmcgfVwiYCk7XG4gIH1cbiAgaWYgKChlbmNvZGluZyA9PT0gJ3V0ZjgnIHx8IGVuY29kaW5nID09PSAndXRmLTgnKSAmJiBnbG9iYWxUaGlzLkJ1ZmZlciAhPSBudWxsICYmIGdsb2JhbFRoaXMuQnVmZmVyLmZyb20gIT0gbnVsbCkge1xuICAgIHJldHVybiBnbG9iYWxUaGlzLkJ1ZmZlci5mcm9tKHN0cmluZywgJ3V0ZjgnKTtcbiAgfVxuICByZXR1cm4gYmFzZS5kZWNvZGVyLmRlY29kZShgJHsgYmFzZS5wcmVmaXggfSR7IHN0cmluZyB9YCk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/from-string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/index.js":
/*!***************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compare: () => (/* reexport safe */ _compare_js__WEBPACK_IMPORTED_MODULE_0__.compare),\n/* harmony export */   concat: () => (/* reexport safe */ _concat_js__WEBPACK_IMPORTED_MODULE_1__.concat),\n/* harmony export */   equals: () => (/* reexport safe */ _equals_js__WEBPACK_IMPORTED_MODULE_2__.equals),\n/* harmony export */   fromString: () => (/* reexport safe */ _from_string_js__WEBPACK_IMPORTED_MODULE_3__.fromString),\n/* harmony export */   toString: () => (/* reexport safe */ _to_string_js__WEBPACK_IMPORTED_MODULE_4__.toString),\n/* harmony export */   xor: () => (/* reexport safe */ _xor_js__WEBPACK_IMPORTED_MODULE_5__.xor)\n/* harmony export */ });\n/* harmony import */ var _compare_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./compare.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/compare.js\");\n/* harmony import */ var _concat_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./concat.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/concat.js\");\n/* harmony import */ var _equals_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./equals.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/equals.js\");\n/* harmony import */ var _from_string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./from-string.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/from-string.js\");\n/* harmony import */ var _to_string_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./to-string.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/to-string.js\");\n/* harmony import */ var _xor_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./xor.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/xor.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBdUM7QUFDRjtBQUNBO0FBQ1M7QUFDSjtBQUNYIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXHVpbnQ4YXJyYXlzXFxlc21cXHNyY1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29tcGFyZSB9IGZyb20gJy4vY29tcGFyZS5qcyc7XG5pbXBvcnQgeyBjb25jYXQgfSBmcm9tICcuL2NvbmNhdC5qcyc7XG5pbXBvcnQgeyBlcXVhbHMgfSBmcm9tICcuL2VxdWFscy5qcyc7XG5pbXBvcnQgeyBmcm9tU3RyaW5nIH0gZnJvbSAnLi9mcm9tLXN0cmluZy5qcyc7XG5pbXBvcnQgeyB0b1N0cmluZyB9IGZyb20gJy4vdG8tc3RyaW5nLmpzJztcbmltcG9ydCB7IHhvciB9IGZyb20gJy4veG9yLmpzJztcbmV4cG9ydCB7XG4gIGNvbXBhcmUsXG4gIGNvbmNhdCxcbiAgZXF1YWxzLFxuICBmcm9tU3RyaW5nLFxuICB0b1N0cmluZyxcbiAgeG9yXG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/to-string.js":
/*!*******************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/to-string.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toString: () => (/* binding */ toString)\n/* harmony export */ });\n/* harmony import */ var _util_bases_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/bases.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/util/bases.js\");\n\nfunction toString(array, encoding = 'utf8') {\n  const base = _util_bases_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"][encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(array.buffer, array.byteOffset, array.byteLength).toString('utf8');\n  }\n  return base.encoder.encode(array).substring(1);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy90by1zdHJpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFDN0I7QUFDUCxlQUFlLHNEQUFLO0FBQ3BCO0FBQ0EsOENBQThDLFVBQVU7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXHVpbnQ4YXJyYXlzXFxlc21cXHNyY1xcdG8tc3RyaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBiYXNlcyBmcm9tICcuL3V0aWwvYmFzZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHRvU3RyaW5nKGFycmF5LCBlbmNvZGluZyA9ICd1dGY4Jykge1xuICBjb25zdCBiYXNlID0gYmFzZXNbZW5jb2RpbmddO1xuICBpZiAoIWJhc2UpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIGVuY29kaW5nIFwiJHsgZW5jb2RpbmcgfVwiYCk7XG4gIH1cbiAgaWYgKChlbmNvZGluZyA9PT0gJ3V0ZjgnIHx8IGVuY29kaW5nID09PSAndXRmLTgnKSAmJiBnbG9iYWxUaGlzLkJ1ZmZlciAhPSBudWxsICYmIGdsb2JhbFRoaXMuQnVmZmVyLmZyb20gIT0gbnVsbCkge1xuICAgIHJldHVybiBnbG9iYWxUaGlzLkJ1ZmZlci5mcm9tKGFycmF5LmJ1ZmZlciwgYXJyYXkuYnl0ZU9mZnNldCwgYXJyYXkuYnl0ZUxlbmd0aCkudG9TdHJpbmcoJ3V0ZjgnKTtcbiAgfVxuICByZXR1cm4gYmFzZS5lbmNvZGVyLmVuY29kZShhcnJheSkuc3Vic3RyaW5nKDEpO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/to-string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/util/bases.js":
/*!********************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/util/bases.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var multiformats_basics__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! multiformats/basics */ \"(ssr)/./node_modules/multiformats/esm/src/basics.js\");\n/* harmony import */ var _alloc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../alloc.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/alloc.js\");\n\n\nfunction createCodec(name, prefix, encode, decode) {\n  return {\n    name,\n    prefix,\n    encoder: {\n      name,\n      prefix,\n      encode\n    },\n    decoder: { decode }\n  };\n}\nconst string = createCodec('utf8', 'u', buf => {\n  const decoder = new TextDecoder('utf8');\n  return 'u' + decoder.decode(buf);\n}, str => {\n  const encoder = new TextEncoder();\n  return encoder.encode(str.substring(1));\n});\nconst ascii = createCodec('ascii', 'a', buf => {\n  let string = 'a';\n  for (let i = 0; i < buf.length; i++) {\n    string += String.fromCharCode(buf[i]);\n  }\n  return string;\n}, str => {\n  str = str.substring(1);\n  const buf = (0,_alloc_js__WEBPACK_IMPORTED_MODULE_1__.allocUnsafe)(str.length);\n  for (let i = 0; i < str.length; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n});\nconst BASES = {\n  utf8: string,\n  'utf-8': string,\n  hex: multiformats_basics__WEBPACK_IMPORTED_MODULE_0__.bases.base16,\n  latin1: ascii,\n  ascii: ascii,\n  binary: ascii,\n  ...multiformats_basics__WEBPACK_IMPORTED_MODULE_0__.bases\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BASES);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/util/bases.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uint8arrays/esm/src/xor.js":
/*!*************************************************!*\
  !*** ./node_modules/uint8arrays/esm/src/xor.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xor: () => (/* binding */ xor)\n/* harmony export */ });\n/* harmony import */ var _alloc_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./alloc.js */ \"(ssr)/./node_modules/uint8arrays/esm/src/alloc.js\");\n\nfunction xor(a, b) {\n  if (a.length !== b.length) {\n    throw new Error('Inputs should have the same length');\n  }\n  const result = (0,_alloc_js__WEBPACK_IMPORTED_MODULE_0__.allocUnsafe)(a.length);\n  for (let i = 0; i < a.length; i++) {\n    result[i] = a[i] ^ b[i];\n  }\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdWludDhhcnJheXMvZXNtL3NyYy94b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFDbEM7QUFDUDtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsc0RBQVc7QUFDNUIsa0JBQWtCLGNBQWM7QUFDaEM7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcdWludDhhcnJheXNcXGVzbVxcc3JjXFx4b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWxsb2NVbnNhZmUgfSBmcm9tICcuL2FsbG9jLmpzJztcbmV4cG9ydCBmdW5jdGlvbiB4b3IoYSwgYikge1xuICBpZiAoYS5sZW5ndGggIT09IGIubGVuZ3RoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdJbnB1dHMgc2hvdWxkIGhhdmUgdGhlIHNhbWUgbGVuZ3RoJyk7XG4gIH1cbiAgY29uc3QgcmVzdWx0ID0gYWxsb2NVbnNhZmUoYS5sZW5ndGgpO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGEubGVuZ3RoOyBpKyspIHtcbiAgICByZXN1bHRbaV0gPSBhW2ldIF4gYltpXTtcbiAgfVxuICByZXR1cm4gcmVzdWx0O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uint8arrays/esm/src/xor.js\n");

/***/ })

};
;