"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lit-labs";
exports.ids = ["vendor-chunks/@lit-labs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@lit-labs/ssr-dom-shim/index.js":
/*!******************************************************!*\
  !*** ./node_modules/@lit-labs/ssr-dom-shim/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomElementRegistry: () => (/* binding */ CustomElementRegistryShimWithRealType),\n/* harmony export */   CustomEvent: () => (/* reexport safe */ _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.CustomEvent),\n/* harmony export */   Element: () => (/* binding */ ElementShimWithRealType),\n/* harmony export */   ElementInternals: () => (/* reexport safe */ _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.ElementInternals),\n/* harmony export */   Event: () => (/* reexport safe */ _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.Event),\n/* harmony export */   EventTarget: () => (/* reexport safe */ _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.EventTarget),\n/* harmony export */   HTMLElement: () => (/* binding */ HTMLElementShimWithRealType),\n/* harmony export */   HYDRATE_INTERNALS_ATTR_PREFIX: () => (/* reexport safe */ _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.HYDRATE_INTERNALS_ATTR_PREFIX),\n/* harmony export */   ariaMixinAttributes: () => (/* reexport safe */ _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.ariaMixinAttributes),\n/* harmony export */   customElements: () => (/* binding */ customElements)\n/* harmony export */ });\n/* harmony import */ var _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/element-internals.js */ \"(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js\");\n/* harmony import */ var _lib_events_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/events.js */ \"(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/events.js\");\n/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n\n\n\n// In an empty Node.js vm, we need to patch the global context.\n// TODO: Remove these globalThis assignments when we remove support\n// for vm modules (--experimental-vm-modules).\nglobalThis.Event ??= _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.EventShim;\nglobalThis.CustomEvent ??= _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.CustomEventShim;\nconst attributes = new WeakMap();\nconst attributesForElement = (element) => {\n    let attrs = attributes.get(element);\n    if (attrs === undefined) {\n        attributes.set(element, (attrs = new Map()));\n    }\n    return attrs;\n};\n// The typings around the exports below are a little funky:\n//\n// 1. We want the `name` of the shim classes to match the real ones at runtime,\n//    hence e.g. `class Element`.\n// 2. We can't shadow the global types with a simple class declaration, because\n//    then we can't reference the global types for casting, hence e.g.\n//    `const ElementShim = class Element`.\n// 3. We want to export the classes typed as the real ones, hence e.g.\n//    `const ElementShimWithRealType = ElementShim as object as typeof Element;`.\n// 4. We want the exported names to match the real ones, hence e.g.\n//    `export {ElementShimWithRealType as Element}`.\nconst ElementShim = class Element extends _lib_events_js__WEBPACK_IMPORTED_MODULE_1__.EventTargetShim {\n    constructor() {\n        super(...arguments);\n        this.__shadowRootMode = null;\n        this.__shadowRoot = null;\n        this.__internals = null;\n    }\n    get attributes() {\n        return Array.from(attributesForElement(this)).map(([name, value]) => ({\n            name,\n            value,\n        }));\n    }\n    get shadowRoot() {\n        if (this.__shadowRootMode === 'closed') {\n            return null;\n        }\n        return this.__shadowRoot;\n    }\n    get localName() {\n        return this.constructor.__localName;\n    }\n    get tagName() {\n        return this.localName?.toUpperCase();\n    }\n    setAttribute(name, value) {\n        // Emulate browser behavior that silently casts all values to string. E.g.\n        // `42` becomes `\"42\"` and `{}` becomes `\"[object Object]\"\"`.\n        attributesForElement(this).set(name, String(value));\n    }\n    removeAttribute(name) {\n        attributesForElement(this).delete(name);\n    }\n    toggleAttribute(name, force) {\n        // Steps reference https://dom.spec.whatwg.org/#dom-element-toggleattribute\n        if (this.hasAttribute(name)) {\n            // Step 5\n            if (force === undefined || !force) {\n                this.removeAttribute(name);\n                return false;\n            }\n        }\n        else {\n            // Step 4\n            if (force === undefined || force) {\n                // Step 4.1\n                this.setAttribute(name, '');\n                return true;\n            }\n            else {\n                // Step 4.2\n                return false;\n            }\n        }\n        // Step 6\n        return true;\n    }\n    hasAttribute(name) {\n        return attributesForElement(this).has(name);\n    }\n    attachShadow(init) {\n        const shadowRoot = { host: this };\n        this.__shadowRootMode = init.mode;\n        if (init && init.mode === 'open') {\n            this.__shadowRoot = shadowRoot;\n        }\n        return shadowRoot;\n    }\n    attachInternals() {\n        if (this.__internals !== null) {\n            throw new Error(`Failed to execute 'attachInternals' on 'HTMLElement': ` +\n                `ElementInternals for the specified element was already attached.`);\n        }\n        const internals = new _lib_element_internals_js__WEBPACK_IMPORTED_MODULE_0__.ElementInternalsShim(this);\n        this.__internals = internals;\n        return internals;\n    }\n    getAttribute(name) {\n        const value = attributesForElement(this).get(name);\n        return value ?? null;\n    }\n};\nconst ElementShimWithRealType = ElementShim;\n\nconst HTMLElementShim = class HTMLElement extends ElementShim {\n};\nconst HTMLElementShimWithRealType = HTMLElementShim;\n\n// For convenience, we provide a global instance of a HTMLElement as an event\n// target. This facilitates registering global event handlers\n// (e.g. for @lit/context ContextProvider).\n// We use this in in the SSR render function.\n// Note, this is a bespoke element and not simply `document` or `window` since\n// user code relies on these being undefined in the server environment.\nglobalThis.litServerRoot ??= Object.defineProperty(new HTMLElementShimWithRealType(), 'localName', {\n    // Patch localName (and tagName) to return a unique name.\n    get() {\n        return 'lit-server-root';\n    },\n});\nconst CustomElementRegistryShim = class CustomElementRegistry {\n    constructor() {\n        this.__definitions = new Map();\n    }\n    define(name, ctor) {\n        if (this.__definitions.has(name)) {\n            if (true) {\n                console.warn(`'CustomElementRegistry' already has \"${name}\" defined. ` +\n                    `This may have been caused by live reload or hot module ` +\n                    `replacement in which case it can be safely ignored.\\n` +\n                    `Make sure to test your application with a production build as ` +\n                    `repeat registrations will throw in production.`);\n            }\n            else {}\n        }\n        // Provide tagName and localName for the component.\n        ctor.__localName = name;\n        this.__definitions.set(name, {\n            ctor,\n            // Note it's important we read `observedAttributes` in case it is a getter\n            // with side-effects, as is the case in Lit, where it triggers class\n            // finalization.\n            //\n            // TODO(aomarks) To be spec compliant, we should also capture the\n            // registration-time lifecycle methods like `connectedCallback`. For them\n            // to be actually accessible to e.g. the Lit SSR element renderer, though,\n            // we'd need to introduce a new API for accessing them (since `get` only\n            // returns the constructor).\n            observedAttributes: ctor.observedAttributes ?? [],\n        });\n    }\n    get(name) {\n        const definition = this.__definitions.get(name);\n        return definition?.ctor;\n    }\n};\nconst CustomElementRegistryShimWithRealType = CustomElementRegistryShim;\n\nconst customElements = new CustomElementRegistryShimWithRealType();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit-labs/ssr-dom-shim/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ElementInternals: () => (/* binding */ ElementInternalsShimWithRealType),\n/* harmony export */   ElementInternalsShim: () => (/* binding */ ElementInternalsShim),\n/* harmony export */   HYDRATE_INTERNALS_ATTR_PREFIX: () => (/* binding */ HYDRATE_INTERNALS_ATTR_PREFIX),\n/* harmony export */   ariaMixinAttributes: () => (/* binding */ ariaMixinAttributes)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Map of ARIAMixin properties to attributes\n */\nconst ariaMixinAttributes = {\n    ariaAtomic: 'aria-atomic',\n    ariaAutoComplete: 'aria-autocomplete',\n    ariaBrailleLabel: 'aria-braillelabel',\n    ariaBrailleRoleDescription: 'aria-brailleroledescription',\n    ariaBusy: 'aria-busy',\n    ariaChecked: 'aria-checked',\n    ariaColCount: 'aria-colcount',\n    ariaColIndex: 'aria-colindex',\n    ariaColSpan: 'aria-colspan',\n    ariaCurrent: 'aria-current',\n    ariaDescription: 'aria-description',\n    ariaDisabled: 'aria-disabled',\n    ariaExpanded: 'aria-expanded',\n    ariaHasPopup: 'aria-haspopup',\n    ariaHidden: 'aria-hidden',\n    ariaInvalid: 'aria-invalid',\n    ariaKeyShortcuts: 'aria-keyshortcuts',\n    ariaLabel: 'aria-label',\n    ariaLevel: 'aria-level',\n    ariaLive: 'aria-live',\n    ariaModal: 'aria-modal',\n    ariaMultiLine: 'aria-multiline',\n    ariaMultiSelectable: 'aria-multiselectable',\n    ariaOrientation: 'aria-orientation',\n    ariaPlaceholder: 'aria-placeholder',\n    ariaPosInSet: 'aria-posinset',\n    ariaPressed: 'aria-pressed',\n    ariaReadOnly: 'aria-readonly',\n    ariaRequired: 'aria-required',\n    ariaRoleDescription: 'aria-roledescription',\n    ariaRowCount: 'aria-rowcount',\n    ariaRowIndex: 'aria-rowindex',\n    ariaRowSpan: 'aria-rowspan',\n    ariaSelected: 'aria-selected',\n    ariaSetSize: 'aria-setsize',\n    ariaSort: 'aria-sort',\n    ariaValueMax: 'aria-valuemax',\n    ariaValueMin: 'aria-valuemin',\n    ariaValueNow: 'aria-valuenow',\n    ariaValueText: 'aria-valuetext',\n    role: 'role',\n};\n// Shim the global element internals object\n// Methods should be fine as noops and properties can generally\n// be while on the server.\nconst ElementInternalsShim = class ElementInternals {\n    get shadowRoot() {\n        // Grab the shadow root instance from the Element shim\n        // to ensure that the shadow root is always available\n        // to the internals instance even if the mode is 'closed'\n        return this.__host\n            .__shadowRoot;\n    }\n    constructor(_host) {\n        this.ariaAtomic = '';\n        this.ariaAutoComplete = '';\n        this.ariaBrailleLabel = '';\n        this.ariaBrailleRoleDescription = '';\n        this.ariaBusy = '';\n        this.ariaChecked = '';\n        this.ariaColCount = '';\n        this.ariaColIndex = '';\n        this.ariaColSpan = '';\n        this.ariaCurrent = '';\n        this.ariaDescription = '';\n        this.ariaDisabled = '';\n        this.ariaExpanded = '';\n        this.ariaHasPopup = '';\n        this.ariaHidden = '';\n        this.ariaInvalid = '';\n        this.ariaKeyShortcuts = '';\n        this.ariaLabel = '';\n        this.ariaLevel = '';\n        this.ariaLive = '';\n        this.ariaModal = '';\n        this.ariaMultiLine = '';\n        this.ariaMultiSelectable = '';\n        this.ariaOrientation = '';\n        this.ariaPlaceholder = '';\n        this.ariaPosInSet = '';\n        this.ariaPressed = '';\n        this.ariaReadOnly = '';\n        this.ariaRequired = '';\n        this.ariaRoleDescription = '';\n        this.ariaRowCount = '';\n        this.ariaRowIndex = '';\n        this.ariaRowSpan = '';\n        this.ariaSelected = '';\n        this.ariaSetSize = '';\n        this.ariaSort = '';\n        this.ariaValueMax = '';\n        this.ariaValueMin = '';\n        this.ariaValueNow = '';\n        this.ariaValueText = '';\n        this.role = '';\n        this.form = null;\n        this.labels = [];\n        this.states = new Set();\n        this.validationMessage = '';\n        this.validity = {};\n        this.willValidate = true;\n        this.__host = _host;\n    }\n    checkValidity() {\n        // TODO(augustjk) Consider actually implementing logic.\n        // See https://github.com/lit/lit/issues/3740\n        console.warn('`ElementInternals.checkValidity()` was called on the server.' +\n            'This method always returns true.');\n        return true;\n    }\n    reportValidity() {\n        return true;\n    }\n    setFormValue() { }\n    setValidity() { }\n};\nconst ElementInternalsShimWithRealType = ElementInternalsShim;\n\nconst HYDRATE_INTERNALS_ATTR_PREFIX = 'hydrate-internals-';\n//# sourceMappingURL=element-internals.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/element-internals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/events.js":
/*!***********************************************************!*\
  !*** ./node_modules/@lit-labs/ssr-dom-shim/lib/events.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomEvent: () => (/* binding */ CustomEventShimWithRealType),\n/* harmony export */   CustomEventShim: () => (/* binding */ CustomEventShimWithRealType),\n/* harmony export */   Event: () => (/* binding */ EventShimWithRealType),\n/* harmony export */   EventShim: () => (/* binding */ EventShimWithRealType),\n/* harmony export */   EventTarget: () => (/* binding */ EventTargetShimWithRealType),\n/* harmony export */   EventTargetShim: () => (/* binding */ EventTargetShimWithRealType)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _Event_cancelable, _Event_bubbles, _Event_composed, _Event_defaultPrevented, _Event_timestamp, _Event_propagationStopped, _Event_type, _Event_target, _Event_isBeingDispatched, _a, _CustomEvent_detail, _b;\nconst isCaptureEventListener = (options) => (typeof options === 'boolean' ? options : options?.capture ?? false);\n// Event phases\nconst NONE = 0;\nconst CAPTURING_PHASE = 1;\nconst AT_TARGET = 2;\nconst BUBBLING_PHASE = 3;\n// Shim the global EventTarget object\nconst EventTargetShim = class EventTarget {\n    constructor() {\n        this.__eventListeners = new Map();\n        this.__captureEventListeners = new Map();\n    }\n    addEventListener(type, callback, options) {\n        if (callback === undefined || callback === null) {\n            return;\n        }\n        const eventListenersMap = isCaptureEventListener(options)\n            ? this.__captureEventListeners\n            : this.__eventListeners;\n        let eventListeners = eventListenersMap.get(type);\n        if (eventListeners === undefined) {\n            eventListeners = new Map();\n            eventListenersMap.set(type, eventListeners);\n        }\n        else if (eventListeners.has(callback)) {\n            return;\n        }\n        const normalizedOptions = typeof options === 'object' && options ? options : {};\n        normalizedOptions.signal?.addEventListener('abort', () => this.removeEventListener(type, callback, options));\n        eventListeners.set(callback, normalizedOptions ?? {});\n    }\n    removeEventListener(type, callback, options) {\n        if (callback === undefined || callback === null) {\n            return;\n        }\n        const eventListenersMap = isCaptureEventListener(options)\n            ? this.__captureEventListeners\n            : this.__eventListeners;\n        const eventListeners = eventListenersMap.get(type);\n        if (eventListeners !== undefined) {\n            eventListeners.delete(callback);\n            if (!eventListeners.size) {\n                eventListenersMap.delete(type);\n            }\n        }\n    }\n    dispatchEvent(event) {\n        const composedPath = [this];\n        let parent = this.__eventTargetParent;\n        if (event.composed) {\n            while (parent) {\n                composedPath.push(parent);\n                parent = parent.__eventTargetParent;\n            }\n        }\n        else {\n            // If the event is not composed and the event was dispatched inside\n            // shadow DOM, we need to stop before the host of the shadow DOM.\n            while (parent && parent !== this.__host) {\n                composedPath.push(parent);\n                parent = parent.__eventTargetParent;\n            }\n        }\n        // We need to patch various properties that would either be empty or wrong\n        // in this scenario.\n        let stopPropagation = false;\n        let stopImmediatePropagation = false;\n        let eventPhase = NONE;\n        let target = null;\n        let tmpTarget = null;\n        let currentTarget = null;\n        const originalStopPropagation = event.stopPropagation;\n        const originalStopImmediatePropagation = event.stopImmediatePropagation;\n        Object.defineProperties(event, {\n            target: {\n                get() {\n                    return target ?? tmpTarget;\n                },\n                ...enumerableProperty,\n            },\n            srcElement: {\n                get() {\n                    return event.target;\n                },\n                ...enumerableProperty,\n            },\n            currentTarget: {\n                get() {\n                    return currentTarget;\n                },\n                ...enumerableProperty,\n            },\n            eventPhase: {\n                get() {\n                    return eventPhase;\n                },\n                ...enumerableProperty,\n            },\n            composedPath: {\n                value: () => composedPath,\n                ...enumerableProperty,\n            },\n            stopPropagation: {\n                value: () => {\n                    stopPropagation = true;\n                    originalStopPropagation.call(event);\n                },\n                ...enumerableProperty,\n            },\n            stopImmediatePropagation: {\n                value: () => {\n                    stopImmediatePropagation = true;\n                    originalStopImmediatePropagation.call(event);\n                },\n                ...enumerableProperty,\n            },\n        });\n        // An event handler can either be a function, an object with a handleEvent\n        // method or null. This function takes care to call the event handler\n        // correctly.\n        const invokeEventListener = (listener, options, eventListenerMap) => {\n            if (typeof listener === 'function') {\n                listener(event);\n            }\n            else if (typeof listener?.handleEvent === 'function') {\n                listener.handleEvent(event);\n            }\n            if (options.once) {\n                eventListenerMap.delete(listener);\n            }\n        };\n        // When an event is finished being dispatched, which can be after the event\n        // tree has been traversed or stopPropagation/stopImmediatePropagation has\n        // been called. Once that is the case, the currentTarget and eventPhase\n        // need to be reset and a value, representing whether the event has not\n        // been prevented, needs to be returned.\n        const finishDispatch = () => {\n            currentTarget = null;\n            eventPhase = NONE;\n            return !event.defaultPrevented;\n        };\n        // An event starts with the capture order, where it starts from the top.\n        // This is done even if bubbles is set to false, which is the default.\n        const captureEventPath = composedPath.slice().reverse();\n        // If the event target, which dispatches the event, is either in the light DOM\n        // or the event is not composed, the target is always itself. If that is not\n        // the case, the target needs to be retargeted: https://dom.spec.whatwg.org/#retarget\n        target = !this.__host || !event.composed ? this : null;\n        const retarget = (eventTargets) => {\n            // eslint-disable-next-line @typescript-eslint/no-this-alias\n            tmpTarget = this;\n            while (tmpTarget.__host && eventTargets.includes(tmpTarget.__host)) {\n                tmpTarget = tmpTarget.__host;\n            }\n        };\n        for (const eventTarget of captureEventPath) {\n            if (!target && (!tmpTarget || tmpTarget === eventTarget.__host)) {\n                retarget(captureEventPath.slice(captureEventPath.indexOf(eventTarget)));\n            }\n            currentTarget = eventTarget;\n            eventPhase = eventTarget === event.target ? AT_TARGET : CAPTURING_PHASE;\n            const captureEventListeners = eventTarget.__captureEventListeners.get(event.type);\n            if (captureEventListeners) {\n                for (const [listener, options] of captureEventListeners) {\n                    invokeEventListener(listener, options, captureEventListeners);\n                    if (stopImmediatePropagation) {\n                        // Event.stopImmediatePropagation() stops any following invocation\n                        // of an event handler even on the same event target.\n                        return finishDispatch();\n                    }\n                }\n            }\n            if (stopPropagation) {\n                // Event.stopPropagation() stops any following invocation\n                // of an event handler for any following event targets.\n                return finishDispatch();\n            }\n        }\n        const bubbleEventPath = event.bubbles ? composedPath : [this];\n        tmpTarget = null;\n        for (const eventTarget of bubbleEventPath) {\n            if (!target &&\n                (!tmpTarget || eventTarget === tmpTarget.__host)) {\n                retarget(bubbleEventPath.slice(0, bubbleEventPath.indexOf(eventTarget) + 1));\n            }\n            currentTarget = eventTarget;\n            eventPhase = eventTarget === event.target ? AT_TARGET : BUBBLING_PHASE;\n            const captureEventListeners = eventTarget.__eventListeners.get(event.type);\n            if (captureEventListeners) {\n                for (const [listener, options] of captureEventListeners) {\n                    invokeEventListener(listener, options, captureEventListeners);\n                    if (stopImmediatePropagation) {\n                        // Event.stopImmediatePropagation() stops any following invocation\n                        // of an event handler even on the same event target.\n                        return finishDispatch();\n                    }\n                }\n            }\n            if (stopPropagation) {\n                // Event.stopPropagation() stops any following invocation\n                // of an event handler for any following event targets.\n                return finishDispatch();\n            }\n        }\n        return finishDispatch();\n    }\n};\nconst EventTargetShimWithRealType = EventTargetShim;\n\nconst enumerableProperty = { __proto__: null };\nenumerableProperty.enumerable = true;\nObject.freeze(enumerableProperty);\n// TODO: Remove this when we remove support for vm modules (--experimental-vm-modules).\nconst EventShim = (_a = class Event {\n        constructor(type, options = {}) {\n            _Event_cancelable.set(this, false);\n            _Event_bubbles.set(this, false);\n            _Event_composed.set(this, false);\n            _Event_defaultPrevented.set(this, false);\n            _Event_timestamp.set(this, Date.now());\n            _Event_propagationStopped.set(this, false);\n            _Event_type.set(this, void 0);\n            _Event_target.set(this, void 0);\n            _Event_isBeingDispatched.set(this, void 0);\n            this.NONE = NONE;\n            this.CAPTURING_PHASE = CAPTURING_PHASE;\n            this.AT_TARGET = AT_TARGET;\n            this.BUBBLING_PHASE = BUBBLING_PHASE;\n            if (arguments.length === 0)\n                throw new Error(`The type argument must be specified`);\n            if (typeof options !== 'object' || !options) {\n                throw new Error(`The \"options\" argument must be an object`);\n            }\n            const { bubbles, cancelable, composed } = options;\n            __classPrivateFieldSet(this, _Event_cancelable, !!cancelable, \"f\");\n            __classPrivateFieldSet(this, _Event_bubbles, !!bubbles, \"f\");\n            __classPrivateFieldSet(this, _Event_composed, !!composed, \"f\");\n            __classPrivateFieldSet(this, _Event_type, `${type}`, \"f\");\n            __classPrivateFieldSet(this, _Event_target, null, \"f\");\n            __classPrivateFieldSet(this, _Event_isBeingDispatched, false, \"f\");\n        }\n        initEvent(_type, _bubbles, _cancelable) {\n            throw new Error('Method not implemented.');\n        }\n        stopImmediatePropagation() {\n            this.stopPropagation();\n        }\n        preventDefault() {\n            __classPrivateFieldSet(this, _Event_defaultPrevented, true, \"f\");\n        }\n        get target() {\n            return __classPrivateFieldGet(this, _Event_target, \"f\");\n        }\n        get currentTarget() {\n            return __classPrivateFieldGet(this, _Event_target, \"f\");\n        }\n        get srcElement() {\n            return __classPrivateFieldGet(this, _Event_target, \"f\");\n        }\n        get type() {\n            return __classPrivateFieldGet(this, _Event_type, \"f\");\n        }\n        get cancelable() {\n            return __classPrivateFieldGet(this, _Event_cancelable, \"f\");\n        }\n        get defaultPrevented() {\n            return __classPrivateFieldGet(this, _Event_cancelable, \"f\") && __classPrivateFieldGet(this, _Event_defaultPrevented, \"f\");\n        }\n        get timeStamp() {\n            return __classPrivateFieldGet(this, _Event_timestamp, \"f\");\n        }\n        composedPath() {\n            return __classPrivateFieldGet(this, _Event_isBeingDispatched, \"f\") ? [__classPrivateFieldGet(this, _Event_target, \"f\")] : [];\n        }\n        get returnValue() {\n            return !__classPrivateFieldGet(this, _Event_cancelable, \"f\") || !__classPrivateFieldGet(this, _Event_defaultPrevented, \"f\");\n        }\n        get bubbles() {\n            return __classPrivateFieldGet(this, _Event_bubbles, \"f\");\n        }\n        get composed() {\n            return __classPrivateFieldGet(this, _Event_composed, \"f\");\n        }\n        get eventPhase() {\n            return __classPrivateFieldGet(this, _Event_isBeingDispatched, \"f\") ? _a.AT_TARGET : _a.NONE;\n        }\n        get cancelBubble() {\n            return __classPrivateFieldGet(this, _Event_propagationStopped, \"f\");\n        }\n        set cancelBubble(value) {\n            if (value) {\n                __classPrivateFieldSet(this, _Event_propagationStopped, true, \"f\");\n            }\n        }\n        stopPropagation() {\n            __classPrivateFieldSet(this, _Event_propagationStopped, true, \"f\");\n        }\n        get isTrusted() {\n            return false;\n        }\n    },\n    _Event_cancelable = new WeakMap(),\n    _Event_bubbles = new WeakMap(),\n    _Event_composed = new WeakMap(),\n    _Event_defaultPrevented = new WeakMap(),\n    _Event_timestamp = new WeakMap(),\n    _Event_propagationStopped = new WeakMap(),\n    _Event_type = new WeakMap(),\n    _Event_target = new WeakMap(),\n    _Event_isBeingDispatched = new WeakMap(),\n    _a.NONE = NONE,\n    _a.CAPTURING_PHASE = CAPTURING_PHASE,\n    _a.AT_TARGET = AT_TARGET,\n    _a.BUBBLING_PHASE = BUBBLING_PHASE,\n    _a);\nObject.defineProperties(EventShim.prototype, {\n    initEvent: enumerableProperty,\n    stopImmediatePropagation: enumerableProperty,\n    preventDefault: enumerableProperty,\n    target: enumerableProperty,\n    currentTarget: enumerableProperty,\n    srcElement: enumerableProperty,\n    type: enumerableProperty,\n    cancelable: enumerableProperty,\n    defaultPrevented: enumerableProperty,\n    timeStamp: enumerableProperty,\n    composedPath: enumerableProperty,\n    returnValue: enumerableProperty,\n    bubbles: enumerableProperty,\n    composed: enumerableProperty,\n    eventPhase: enumerableProperty,\n    cancelBubble: enumerableProperty,\n    stopPropagation: enumerableProperty,\n    isTrusted: enumerableProperty,\n});\n// TODO: Remove this when we remove support for vm modules (--experimental-vm-modules).\nconst CustomEventShim = (_b = class CustomEvent extends EventShim {\n        constructor(type, options = {}) {\n            super(type, options);\n            _CustomEvent_detail.set(this, void 0);\n            __classPrivateFieldSet(this, _CustomEvent_detail, options?.detail ?? null, \"f\");\n        }\n        initCustomEvent(_type, _bubbles, _cancelable, _detail) {\n            throw new Error('Method not implemented.');\n        }\n        get detail() {\n            return __classPrivateFieldGet(this, _CustomEvent_detail, \"f\");\n        }\n    },\n    _CustomEvent_detail = new WeakMap(),\n    _b);\nObject.defineProperties(CustomEventShim.prototype, {\n    detail: enumerableProperty,\n});\nconst EventShimWithRealType = EventShim;\nconst CustomEventShimWithRealType = CustomEventShim;\n\n//# sourceMappingURL=events.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@lit-labs/ssr-dom-shim/lib/events.js\n");

/***/ })

};
;