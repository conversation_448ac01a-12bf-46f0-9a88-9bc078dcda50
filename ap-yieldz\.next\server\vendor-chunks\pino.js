"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino";
exports.ids = ["vendor-chunks/pino"];
exports.modules = {

/***/ "(ssr)/./node_modules/pino/lib/caller.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/caller.js ***!
  \*****************************************/
/***/ ((module) => {

eval("\n\nfunction noOpPrepareStackTrace (_, stack) {\n  return stack\n}\n\nmodule.exports = function getCallers () {\n  const originalPrepare = Error.prepareStackTrace\n  Error.prepareStackTrace = noOpPrepareStackTrace\n  const stack = new Error().stack\n  Error.prepareStackTrace = originalPrepare\n\n  if (!Array.isArray(stack)) {\n    return undefined\n  }\n\n  const entries = stack.slice(2)\n\n  const fileNames = []\n\n  for (const entry of entries) {\n    if (!entry) {\n      continue\n    }\n\n    fileNames.push(entry.getFileName())\n  }\n\n  return fileNames\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvY2FsbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xccGlub1xcbGliXFxjYWxsZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmZ1bmN0aW9uIG5vT3BQcmVwYXJlU3RhY2tUcmFjZSAoXywgc3RhY2spIHtcbiAgcmV0dXJuIHN0YWNrXG59XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gZ2V0Q2FsbGVycyAoKSB7XG4gIGNvbnN0IG9yaWdpbmFsUHJlcGFyZSA9IEVycm9yLnByZXBhcmVTdGFja1RyYWNlXG4gIEVycm9yLnByZXBhcmVTdGFja1RyYWNlID0gbm9PcFByZXBhcmVTdGFja1RyYWNlXG4gIGNvbnN0IHN0YWNrID0gbmV3IEVycm9yKCkuc3RhY2tcbiAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBvcmlnaW5hbFByZXBhcmVcblxuICBpZiAoIUFycmF5LmlzQXJyYXkoc3RhY2spKSB7XG4gICAgcmV0dXJuIHVuZGVmaW5lZFxuICB9XG5cbiAgY29uc3QgZW50cmllcyA9IHN0YWNrLnNsaWNlKDIpXG5cbiAgY29uc3QgZmlsZU5hbWVzID0gW11cblxuICBmb3IgKGNvbnN0IGVudHJ5IG9mIGVudHJpZXMpIHtcbiAgICBpZiAoIWVudHJ5KSB7XG4gICAgICBjb250aW51ZVxuICAgIH1cblxuICAgIGZpbGVOYW1lcy5wdXNoKGVudHJ5LmdldEZpbGVOYW1lKCkpXG4gIH1cblxuICByZXR1cm4gZmlsZU5hbWVzXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/caller.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/deprecations.js":
/*!***********************************************!*\
  !*** ./node_modules/pino/lib/deprecations.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst warning = __webpack_require__(/*! process-warning */ \"(ssr)/./node_modules/process-warning/index.js\")()\nmodule.exports = warning\n\nconst warnName = 'PinoWarning'\n\nwarning.create(warnName, 'PINODEP008', 'prettyPrint is deprecated, look at https://github.com/pinojs/pino-pretty for alternatives.')\n\nwarning.create(warnName, 'PINODEP009', 'The use of pino.final is discouraged in Node.js v14+ and not required. It will be removed in the next major version')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvZGVwcmVjYXRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLHNFQUFpQjtBQUN6Qzs7QUFFQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxwaW5vXFxsaWJcXGRlcHJlY2F0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3Qgd2FybmluZyA9IHJlcXVpcmUoJ3Byb2Nlc3Mtd2FybmluZycpKClcbm1vZHVsZS5leHBvcnRzID0gd2FybmluZ1xuXG5jb25zdCB3YXJuTmFtZSA9ICdQaW5vV2FybmluZydcblxud2FybmluZy5jcmVhdGUod2Fybk5hbWUsICdQSU5PREVQMDA4JywgJ3ByZXR0eVByaW50IGlzIGRlcHJlY2F0ZWQsIGxvb2sgYXQgaHR0cHM6Ly9naXRodWIuY29tL3Bpbm9qcy9waW5vLXByZXR0eSBmb3IgYWx0ZXJuYXRpdmVzLicpXG5cbndhcm5pbmcuY3JlYXRlKHdhcm5OYW1lLCAnUElOT0RFUDAwOScsICdUaGUgdXNlIG9mIHBpbm8uZmluYWwgaXMgZGlzY291cmFnZWQgaW4gTm9kZS5qcyB2MTQrIGFuZCBub3QgcmVxdWlyZWQuIEl0IHdpbGwgYmUgcmVtb3ZlZCBpbiB0aGUgbmV4dCBtYWpvciB2ZXJzaW9uJylcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/deprecations.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/levels.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/levels.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/* eslint no-prototype-builtins: 0 */\nconst {\n  lsCacheSym,\n  levelValSym,\n  useOnlyCustomLevelsSym,\n  streamSym,\n  formattersSym,\n  hooksSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { noop, genLog } = __webpack_require__(/*! ./tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\n\nconst levels = {\n  trace: 10,\n  debug: 20,\n  info: 30,\n  warn: 40,\n  error: 50,\n  fatal: 60\n}\nconst levelMethods = {\n  fatal: (hook) => {\n    const logFatal = genLog(levels.fatal, hook)\n    return function (...args) {\n      const stream = this[streamSym]\n      logFatal.call(this, ...args)\n      if (typeof stream.flushSync === 'function') {\n        try {\n          stream.flushSync()\n        } catch (e) {\n          // https://github.com/pinojs/pino/pull/740#discussion_r346788313\n        }\n      }\n    }\n  },\n  error: (hook) => genLog(levels.error, hook),\n  warn: (hook) => genLog(levels.warn, hook),\n  info: (hook) => genLog(levels.info, hook),\n  debug: (hook) => genLog(levels.debug, hook),\n  trace: (hook) => genLog(levels.trace, hook)\n}\n\nconst nums = Object.keys(levels).reduce((o, k) => {\n  o[levels[k]] = k\n  return o\n}, {})\n\nconst initialLsCache = Object.keys(nums).reduce((o, k) => {\n  o[k] = '{\"level\":' + Number(k)\n  return o\n}, {})\n\nfunction genLsCache (instance) {\n  const formatter = instance[formattersSym].level\n  const { labels } = instance.levels\n  const cache = {}\n  for (const label in labels) {\n    const level = formatter(labels[label], Number(label))\n    cache[label] = JSON.stringify(level).slice(0, -1)\n  }\n  instance[lsCacheSym] = cache\n  return instance\n}\n\nfunction isStandardLevel (level, useOnlyCustomLevels) {\n  if (useOnlyCustomLevels) {\n    return false\n  }\n\n  switch (level) {\n    case 'fatal':\n    case 'error':\n    case 'warn':\n    case 'info':\n    case 'debug':\n    case 'trace':\n      return true\n    default:\n      return false\n  }\n}\n\nfunction setLevel (level) {\n  const { labels, values } = this.levels\n  if (typeof level === 'number') {\n    if (labels[level] === undefined) throw Error('unknown level value' + level)\n    level = labels[level]\n  }\n  if (values[level] === undefined) throw Error('unknown level ' + level)\n  const preLevelVal = this[levelValSym]\n  const levelVal = this[levelValSym] = values[level]\n  const useOnlyCustomLevelsVal = this[useOnlyCustomLevelsSym]\n  const hook = this[hooksSym].logMethod\n\n  for (const key in values) {\n    if (levelVal > values[key]) {\n      this[key] = noop\n      continue\n    }\n    this[key] = isStandardLevel(key, useOnlyCustomLevelsVal) ? levelMethods[key](hook) : genLog(values[key], hook)\n  }\n\n  this.emit(\n    'level-change',\n    level,\n    levelVal,\n    labels[preLevelVal],\n    preLevelVal\n  )\n}\n\nfunction getLevel (level) {\n  const { levels, levelVal } = this\n  // protection against potential loss of Pino scope from serializers (edge case with circular refs - https://github.com/pinojs/pino/issues/833)\n  return (levels && levels.labels) ? levels.labels[levelVal] : ''\n}\n\nfunction isLevelEnabled (logLevel) {\n  const { values } = this.levels\n  const logLevelVal = values[logLevel]\n  return logLevelVal !== undefined && (logLevelVal >= this[levelValSym])\n}\n\nfunction mappings (customLevels = null, useOnlyCustomLevels = false) {\n  const customNums = customLevels\n    /* eslint-disable */\n    ? Object.keys(customLevels).reduce((o, k) => {\n        o[customLevels[k]] = k\n        return o\n      }, {})\n    : null\n    /* eslint-enable */\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { Infinity: { value: 'silent' } }),\n    useOnlyCustomLevels ? null : nums,\n    customNums\n  )\n  const values = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : levels,\n    customLevels\n  )\n  return { labels, values }\n}\n\nfunction assertDefaultLevelFound (defaultLevel, customLevels, useOnlyCustomLevels) {\n  if (typeof defaultLevel === 'number') {\n    const values = [].concat(\n      Object.keys(customLevels || {}).map(key => customLevels[key]),\n      useOnlyCustomLevels ? [] : Object.keys(nums).map(level => +level),\n      Infinity\n    )\n    if (!values.includes(defaultLevel)) {\n      throw Error(`default level:${defaultLevel} must be included in custom levels`)\n    }\n    return\n  }\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : levels,\n    customLevels\n  )\n  if (!(defaultLevel in labels)) {\n    throw Error(`default level:${defaultLevel} must be included in custom levels`)\n  }\n}\n\nfunction assertNoLevelCollisions (levels, customLevels) {\n  const { labels, values } = levels\n  for (const k in customLevels) {\n    if (k in values) {\n      throw Error('levels cannot be overridden')\n    }\n    if (customLevels[k] in labels) {\n      throw Error('pre-existing level values cannot be used for new levels')\n    }\n  }\n}\n\nmodule.exports = {\n  initialLsCache,\n  genLsCache,\n  levelMethods,\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  levels,\n  assertNoLevelCollisions,\n  assertDefaultLevelFound\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/levels.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/meta.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/meta.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { version } = __webpack_require__(/*! ../package.json */ \"(ssr)/./node_modules/pino/package.json\")\n\nmodule.exports = { version }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvbWV0YS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixRQUFRLFVBQVUsRUFBRSxtQkFBTyxDQUFDLCtEQUFpQjs7QUFFN0MsbUJBQW1CIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXHBpbm9cXGxpYlxcbWV0YS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgeyB2ZXJzaW9uIH0gPSByZXF1aXJlKCcuLi9wYWNrYWdlLmpzb24nKVxuXG5tb2R1bGUuZXhwb3J0cyA9IHsgdmVyc2lvbiB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/meta.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/multistream.js":
/*!**********************************************!*\
  !*** ./node_modules/pino/lib/multistream.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst metadata = Symbol.for('pino.metadata')\nconst { levels } = __webpack_require__(/*! ./levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\n\nconst defaultLevels = Object.create(levels)\ndefaultLevels.silent = Infinity\n\nconst DEFAULT_INFO_LEVEL = levels.info\n\nfunction multistream (streamsArray, opts) {\n  let counter = 0\n  streamsArray = streamsArray || []\n  opts = opts || { dedupe: false }\n\n  let levels = defaultLevels\n  if (opts.levels && typeof opts.levels === 'object') {\n    levels = opts.levels\n  }\n\n  const res = {\n    write,\n    add,\n    flushSync,\n    end,\n    minLevel: 0,\n    streams: [],\n    clone,\n    [metadata]: true\n  }\n\n  if (Array.isArray(streamsArray)) {\n    streamsArray.forEach(add, res)\n  } else {\n    add.call(res, streamsArray)\n  }\n\n  // clean this object up\n  // or it will stay allocated forever\n  // as it is closed on the following closures\n  streamsArray = null\n\n  return res\n\n  // we can exit early because the streams are ordered by level\n  function write (data) {\n    let dest\n    const level = this.lastLevel\n    const { streams } = this\n    let stream\n    for (let i = 0; i < streams.length; i++) {\n      dest = streams[i]\n      if (dest.level <= level) {\n        stream = dest.stream\n        if (stream[metadata]) {\n          const { lastTime, lastMsg, lastObj, lastLogger } = this\n          stream.lastLevel = level\n          stream.lastTime = lastTime\n          stream.lastMsg = lastMsg\n          stream.lastObj = lastObj\n          stream.lastLogger = lastLogger\n        }\n        if (!opts.dedupe || dest.level === level) {\n          stream.write(data)\n        }\n      } else {\n        break\n      }\n    }\n  }\n\n  function flushSync () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n    }\n  }\n\n  function add (dest) {\n    if (!dest) {\n      return res\n    }\n\n    // Check that dest implements either StreamEntry or DestinationStream\n    const isStream = typeof dest.write === 'function' || dest.stream\n    const stream_ = dest.write ? dest : dest.stream\n    // This is necessary to provide a meaningful error message, otherwise it throws somewhere inside write()\n    if (!isStream) {\n      throw Error('stream object needs to implement either StreamEntry or DestinationStream interface')\n    }\n\n    const { streams } = this\n\n    let level\n    if (typeof dest.levelVal === 'number') {\n      level = dest.levelVal\n    } else if (typeof dest.level === 'string') {\n      level = levels[dest.level]\n    } else if (typeof dest.level === 'number') {\n      level = dest.level\n    } else {\n      level = DEFAULT_INFO_LEVEL\n    }\n\n    const dest_ = {\n      stream: stream_,\n      level,\n      levelVal: undefined,\n      id: counter++\n    }\n\n    streams.unshift(dest_)\n    streams.sort(compareByLevel)\n\n    this.minLevel = streams[0].level\n\n    return res\n  }\n\n  function end () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n      stream.end()\n    }\n  }\n\n  function clone (level) {\n    const streams = new Array(this.streams.length)\n\n    for (let i = 0; i < streams.length; i++) {\n      streams[i] = {\n        level: level,\n        stream: this.streams[i].stream\n      }\n    }\n\n    return {\n      write,\n      add,\n      minLevel: level,\n      streams,\n      clone,\n      flushSync,\n      [metadata]: true\n    }\n  }\n}\n\nfunction compareByLevel (a, b) {\n  return a.level - b.level\n}\n\nmodule.exports = multistream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/multistream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/proto.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/proto.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst {\n  lsCacheSym,\n  levelValSym,\n  setLevelSym,\n  getLevelSym,\n  chindingsSym,\n  parsedChindingsSym,\n  mixinSym,\n  asJsonSym,\n  writeSym,\n  mixinMergeStrategySym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  serializersSym,\n  formattersSym,\n  useOnlyCustomLevelsSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  stringifySym,\n  formatOptsSym,\n  stringifiersSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst {\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  initialLsCache,\n  genLsCache,\n  assertNoLevelCollisions\n} = __webpack_require__(/*! ./levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\nconst {\n  asChindings,\n  asJson,\n  buildFormatters,\n  stringify\n} = __webpack_require__(/*! ./tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\nconst {\n  version\n} = __webpack_require__(/*! ./meta */ \"(ssr)/./node_modules/pino/lib/meta.js\")\nconst redaction = __webpack_require__(/*! ./redaction */ \"(ssr)/./node_modules/pino/lib/redaction.js\")\n\n// note: use of class is satirical\n// https://github.com/pinojs/pino/pull/433#pullrequestreview-127703127\nconst constructor = class Pino {}\nconst prototype = {\n  constructor,\n  child,\n  bindings,\n  setBindings,\n  flush,\n  isLevelEnabled,\n  version,\n  get level () { return this[getLevelSym]() },\n  set level (lvl) { this[setLevelSym](lvl) },\n  get levelVal () { return this[levelValSym] },\n  set levelVal (n) { throw Error('levelVal is read-only') },\n  [lsCacheSym]: initialLsCache,\n  [writeSym]: write,\n  [asJsonSym]: asJson,\n  [getLevelSym]: getLevel,\n  [setLevelSym]: setLevel\n}\n\nObject.setPrototypeOf(prototype, EventEmitter.prototype)\n\n// exporting and consuming the prototype object using factory pattern fixes scoping issues with getters when serializing\nmodule.exports = function () {\n  return Object.create(prototype)\n}\n\nconst resetChildingsFormatter = bindings => bindings\nfunction child (bindings, options) {\n  if (!bindings) {\n    throw Error('missing bindings for child Pino')\n  }\n  options = options || {} // default options to empty object\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const instance = Object.create(this)\n\n  if (options.hasOwnProperty('serializers') === true) {\n    instance[serializersSym] = Object.create(null)\n\n    for (const k in serializers) {\n      instance[serializersSym][k] = serializers[k]\n    }\n    const parentSymbols = Object.getOwnPropertySymbols(serializers)\n    /* eslint no-var: off */\n    for (var i = 0; i < parentSymbols.length; i++) {\n      const ks = parentSymbols[i]\n      instance[serializersSym][ks] = serializers[ks]\n    }\n\n    for (const bk in options.serializers) {\n      instance[serializersSym][bk] = options.serializers[bk]\n    }\n    const bindingsSymbols = Object.getOwnPropertySymbols(options.serializers)\n    for (var bi = 0; bi < bindingsSymbols.length; bi++) {\n      const bks = bindingsSymbols[bi]\n      instance[serializersSym][bks] = options.serializers[bks]\n    }\n  } else instance[serializersSym] = serializers\n  if (options.hasOwnProperty('formatters')) {\n    const { level, bindings: chindings, log } = options.formatters\n    instance[formattersSym] = buildFormatters(\n      level || formatters.level,\n      chindings || resetChildingsFormatter,\n      log || formatters.log\n    )\n  } else {\n    instance[formattersSym] = buildFormatters(\n      formatters.level,\n      resetChildingsFormatter,\n      formatters.log\n    )\n  }\n  if (options.hasOwnProperty('customLevels') === true) {\n    assertNoLevelCollisions(this.levels, options.customLevels)\n    instance.levels = mappings(options.customLevels, instance[useOnlyCustomLevelsSym])\n    genLsCache(instance)\n  }\n\n  // redact must place before asChindings and only replace if exist\n  if ((typeof options.redact === 'object' && options.redact !== null) || Array.isArray(options.redact)) {\n    instance.redact = options.redact // replace redact directly\n    const stringifiers = redaction(instance.redact, stringify)\n    const formatOpts = { stringify: stringifiers[redactFmtSym] }\n    instance[stringifySym] = stringify\n    instance[stringifiersSym] = stringifiers\n    instance[formatOptsSym] = formatOpts\n  }\n\n  instance[chindingsSym] = asChindings(instance, bindings)\n  const childLevel = options.level || this.level\n  instance[setLevelSym](childLevel)\n\n  return instance\n}\n\nfunction bindings () {\n  const chindings = this[chindingsSym]\n  const chindingsJson = `{${chindings.substr(1)}}` // at least contains ,\"pid\":7068,\"hostname\":\"myMac\"\n  const bindingsFromJson = JSON.parse(chindingsJson)\n  delete bindingsFromJson.pid\n  delete bindingsFromJson.hostname\n  return bindingsFromJson\n}\n\nfunction setBindings (newBindings) {\n  const chindings = asChindings(this, newBindings)\n  this[chindingsSym] = chindings\n  delete this[parsedChindingsSym]\n}\n\n/**\n * Default strategy for creating `mergeObject` from arguments and the result from `mixin()`.\n * Fields from `mergeObject` have higher priority in this strategy.\n *\n * @param {Object} mergeObject The object a user has supplied to the logging function.\n * @param {Object} mixinObject The result of the `mixin` method.\n * @return {Object}\n */\nfunction defaultMixinMergeStrategy (mergeObject, mixinObject) {\n  return Object.assign(mixinObject, mergeObject)\n}\n\nfunction write (_obj, msg, num) {\n  const t = this[timeSym]()\n  const mixin = this[mixinSym]\n  const mixinMergeStrategy = this[mixinMergeStrategySym] || defaultMixinMergeStrategy\n  let obj\n\n  if (_obj === undefined || _obj === null) {\n    obj = {}\n  } else if (_obj instanceof Error) {\n    obj = { err: _obj }\n    if (msg === undefined) {\n      msg = _obj.message\n    }\n  } else {\n    obj = _obj\n    if (msg === undefined && _obj.err) {\n      msg = _obj.err.message\n    }\n  }\n\n  if (mixin) {\n    obj = mixinMergeStrategy(obj, mixin(obj, num))\n  }\n\n  const s = this[asJsonSym](obj, msg, num, t)\n\n  const stream = this[streamSym]\n  if (stream[needsMetadataGsym] === true) {\n    stream.lastLevel = num\n    stream.lastObj = obj\n    stream.lastMsg = msg\n    stream.lastTime = t.slice(this[timeSliceIndexSym])\n    stream.lastLogger = this // for child loggers\n  }\n  stream.write(s)\n}\n\nfunction noop () {}\n\nfunction flush () {\n  const stream = this[streamSym]\n  if ('flush' in stream) stream.flush(noop)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/proto.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/redaction.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/redaction.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst fastRedact = __webpack_require__(/*! fast-redact */ \"(ssr)/./node_modules/fast-redact/index.js\")\nconst { redactFmtSym, wildcardFirstSym } = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { rx, validator } = fastRedact\n\nconst validate = validator({\n  ERR_PATHS_MUST_BE_STRINGS: () => 'pino – redacted paths must be strings',\n  ERR_INVALID_PATH: (s) => `pino – redact paths array contains an invalid path (${s})`\n})\n\nconst CENSOR = '[Redacted]'\nconst strict = false // TODO should this be configurable?\n\nfunction redaction (opts, serialize) {\n  const { paths, censor } = handle(opts)\n\n  const shape = paths.reduce((o, str) => {\n    rx.lastIndex = 0\n    const first = rx.exec(str)\n    const next = rx.exec(str)\n\n    // ns is the top-level path segment, brackets + quoting removed.\n    let ns = first[1] !== undefined\n      ? first[1].replace(/^(?:\"|'|`)(.*)(?:\"|'|`)$/, '$1')\n      : first[0]\n\n    if (ns === '*') {\n      ns = wildcardFirstSym\n    }\n\n    // top level key:\n    if (next === null) {\n      o[ns] = null\n      return o\n    }\n\n    // path with at least two segments:\n    // if ns is already redacted at the top level, ignore lower level redactions\n    if (o[ns] === null) {\n      return o\n    }\n\n    const { index } = next\n    const nextPath = `${str.substr(index, str.length - 1)}`\n\n    o[ns] = o[ns] || []\n\n    // shape is a mix of paths beginning with literal values and wildcard\n    // paths [ \"a.b.c\", \"*.b.z\" ] should reduce to a shape of\n    // { \"a\": [ \"b.c\", \"b.z\" ], *: [ \"b.z\" ] }\n    // note: \"b.z\" is in both \"a\" and * arrays because \"a\" matches the wildcard.\n    // (* entry has wildcardFirstSym as key)\n    if (ns !== wildcardFirstSym && o[ns].length === 0) {\n      // first time ns's get all '*' redactions so far\n      o[ns].push(...(o[wildcardFirstSym] || []))\n    }\n\n    if (ns === wildcardFirstSym) {\n      // new * path gets added to all previously registered literal ns's.\n      Object.keys(o).forEach(function (k) {\n        if (o[k]) {\n          o[k].push(nextPath)\n        }\n      })\n    }\n\n    o[ns].push(nextPath)\n    return o\n  }, {})\n\n  // the redactor assigned to the format symbol key\n  // provides top level redaction for instances where\n  // an object is interpolated into the msg string\n  const result = {\n    [redactFmtSym]: fastRedact({ paths, censor, serialize, strict })\n  }\n\n  const topCensor = (...args) => {\n    return typeof censor === 'function' ? serialize(censor(...args)) : serialize(censor)\n  }\n\n  return [...Object.keys(shape), ...Object.getOwnPropertySymbols(shape)].reduce((o, k) => {\n    // top level key:\n    if (shape[k] === null) {\n      o[k] = (value) => topCensor(value, [k])\n    } else {\n      const wrappedCensor = typeof censor === 'function'\n        ? (value, path) => {\n            return censor(value, [k, ...path])\n          }\n        : censor\n      o[k] = fastRedact({\n        paths: shape[k],\n        censor: wrappedCensor,\n        serialize,\n        strict\n      })\n    }\n    return o\n  }, result)\n}\n\nfunction handle (opts) {\n  if (Array.isArray(opts)) {\n    opts = { paths: opts, censor: CENSOR }\n    validate(opts)\n    return opts\n  }\n  let { paths, censor = CENSOR, remove } = opts\n  if (Array.isArray(paths) === false) { throw Error('pino – redact must contain an array of strings') }\n  if (remove === true) censor = undefined\n  validate({ paths, censor })\n\n  return { paths, censor }\n}\n\nmodule.exports = redaction\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvcmVkYWN0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLG1CQUFtQixtQkFBTyxDQUFDLDhEQUFhO0FBQ3hDLFFBQVEsaUNBQWlDLEVBQUUsbUJBQU8sQ0FBQywyREFBVztBQUM5RCxRQUFRLGdCQUFnQjs7QUFFeEI7QUFDQTtBQUNBLGtGQUFrRixFQUFFO0FBQ3BGLENBQUM7O0FBRUQ7QUFDQTs7QUFFQTtBQUNBLFVBQVUsZ0JBQWdCOztBQUUxQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFlBQVksUUFBUTtBQUNwQix3QkFBd0Isa0NBQWtDOztBQUUxRDs7QUFFQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLEdBQUcsSUFBSTs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxrQ0FBa0M7QUFDbkU7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlDQUFpQztBQUN6Qyx3Q0FBd0M7QUFDeEM7QUFDQSxhQUFhLGVBQWU7O0FBRTVCLFdBQVc7QUFDWDs7QUFFQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxwaW5vXFxsaWJcXHJlZGFjdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgZmFzdFJlZGFjdCA9IHJlcXVpcmUoJ2Zhc3QtcmVkYWN0JylcbmNvbnN0IHsgcmVkYWN0Rm10U3ltLCB3aWxkY2FyZEZpcnN0U3ltIH0gPSByZXF1aXJlKCcuL3N5bWJvbHMnKVxuY29uc3QgeyByeCwgdmFsaWRhdG9yIH0gPSBmYXN0UmVkYWN0XG5cbmNvbnN0IHZhbGlkYXRlID0gdmFsaWRhdG9yKHtcbiAgRVJSX1BBVEhTX01VU1RfQkVfU1RSSU5HUzogKCkgPT4gJ3Bpbm8g4oCTIHJlZGFjdGVkIHBhdGhzIG11c3QgYmUgc3RyaW5ncycsXG4gIEVSUl9JTlZBTElEX1BBVEg6IChzKSA9PiBgcGlubyDigJMgcmVkYWN0IHBhdGhzIGFycmF5IGNvbnRhaW5zIGFuIGludmFsaWQgcGF0aCAoJHtzfSlgXG59KVxuXG5jb25zdCBDRU5TT1IgPSAnW1JlZGFjdGVkXSdcbmNvbnN0IHN0cmljdCA9IGZhbHNlIC8vIFRPRE8gc2hvdWxkIHRoaXMgYmUgY29uZmlndXJhYmxlP1xuXG5mdW5jdGlvbiByZWRhY3Rpb24gKG9wdHMsIHNlcmlhbGl6ZSkge1xuICBjb25zdCB7IHBhdGhzLCBjZW5zb3IgfSA9IGhhbmRsZShvcHRzKVxuXG4gIGNvbnN0IHNoYXBlID0gcGF0aHMucmVkdWNlKChvLCBzdHIpID0+IHtcbiAgICByeC5sYXN0SW5kZXggPSAwXG4gICAgY29uc3QgZmlyc3QgPSByeC5leGVjKHN0cilcbiAgICBjb25zdCBuZXh0ID0gcnguZXhlYyhzdHIpXG5cbiAgICAvLyBucyBpcyB0aGUgdG9wLWxldmVsIHBhdGggc2VnbWVudCwgYnJhY2tldHMgKyBxdW90aW5nIHJlbW92ZWQuXG4gICAgbGV0IG5zID0gZmlyc3RbMV0gIT09IHVuZGVmaW5lZFxuICAgICAgPyBmaXJzdFsxXS5yZXBsYWNlKC9eKD86XCJ8J3xgKSguKikoPzpcInwnfGApJC8sICckMScpXG4gICAgICA6IGZpcnN0WzBdXG5cbiAgICBpZiAobnMgPT09ICcqJykge1xuICAgICAgbnMgPSB3aWxkY2FyZEZpcnN0U3ltXG4gICAgfVxuXG4gICAgLy8gdG9wIGxldmVsIGtleTpcbiAgICBpZiAobmV4dCA9PT0gbnVsbCkge1xuICAgICAgb1tuc10gPSBudWxsXG4gICAgICByZXR1cm4gb1xuICAgIH1cblxuICAgIC8vIHBhdGggd2l0aCBhdCBsZWFzdCB0d28gc2VnbWVudHM6XG4gICAgLy8gaWYgbnMgaXMgYWxyZWFkeSByZWRhY3RlZCBhdCB0aGUgdG9wIGxldmVsLCBpZ25vcmUgbG93ZXIgbGV2ZWwgcmVkYWN0aW9uc1xuICAgIGlmIChvW25zXSA9PT0gbnVsbCkge1xuICAgICAgcmV0dXJuIG9cbiAgICB9XG5cbiAgICBjb25zdCB7IGluZGV4IH0gPSBuZXh0XG4gICAgY29uc3QgbmV4dFBhdGggPSBgJHtzdHIuc3Vic3RyKGluZGV4LCBzdHIubGVuZ3RoIC0gMSl9YFxuXG4gICAgb1tuc10gPSBvW25zXSB8fCBbXVxuXG4gICAgLy8gc2hhcGUgaXMgYSBtaXggb2YgcGF0aHMgYmVnaW5uaW5nIHdpdGggbGl0ZXJhbCB2YWx1ZXMgYW5kIHdpbGRjYXJkXG4gICAgLy8gcGF0aHMgWyBcImEuYi5jXCIsIFwiKi5iLnpcIiBdIHNob3VsZCByZWR1Y2UgdG8gYSBzaGFwZSBvZlxuICAgIC8vIHsgXCJhXCI6IFsgXCJiLmNcIiwgXCJiLnpcIiBdLCAqOiBbIFwiYi56XCIgXSB9XG4gICAgLy8gbm90ZTogXCJiLnpcIiBpcyBpbiBib3RoIFwiYVwiIGFuZCAqIGFycmF5cyBiZWNhdXNlIFwiYVwiIG1hdGNoZXMgdGhlIHdpbGRjYXJkLlxuICAgIC8vICgqIGVudHJ5IGhhcyB3aWxkY2FyZEZpcnN0U3ltIGFzIGtleSlcbiAgICBpZiAobnMgIT09IHdpbGRjYXJkRmlyc3RTeW0gJiYgb1tuc10ubGVuZ3RoID09PSAwKSB7XG4gICAgICAvLyBmaXJzdCB0aW1lIG5zJ3MgZ2V0IGFsbCAnKicgcmVkYWN0aW9ucyBzbyBmYXJcbiAgICAgIG9bbnNdLnB1c2goLi4uKG9bd2lsZGNhcmRGaXJzdFN5bV0gfHwgW10pKVxuICAgIH1cblxuICAgIGlmIChucyA9PT0gd2lsZGNhcmRGaXJzdFN5bSkge1xuICAgICAgLy8gbmV3ICogcGF0aCBnZXRzIGFkZGVkIHRvIGFsbCBwcmV2aW91c2x5IHJlZ2lzdGVyZWQgbGl0ZXJhbCBucydzLlxuICAgICAgT2JqZWN0LmtleXMobykuZm9yRWFjaChmdW5jdGlvbiAoaykge1xuICAgICAgICBpZiAob1trXSkge1xuICAgICAgICAgIG9ba10ucHVzaChuZXh0UGF0aClcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICB9XG5cbiAgICBvW25zXS5wdXNoKG5leHRQYXRoKVxuICAgIHJldHVybiBvXG4gIH0sIHt9KVxuXG4gIC8vIHRoZSByZWRhY3RvciBhc3NpZ25lZCB0byB0aGUgZm9ybWF0IHN5bWJvbCBrZXlcbiAgLy8gcHJvdmlkZXMgdG9wIGxldmVsIHJlZGFjdGlvbiBmb3IgaW5zdGFuY2VzIHdoZXJlXG4gIC8vIGFuIG9iamVjdCBpcyBpbnRlcnBvbGF0ZWQgaW50byB0aGUgbXNnIHN0cmluZ1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgW3JlZGFjdEZtdFN5bV06IGZhc3RSZWRhY3QoeyBwYXRocywgY2Vuc29yLCBzZXJpYWxpemUsIHN0cmljdCB9KVxuICB9XG5cbiAgY29uc3QgdG9wQ2Vuc29yID0gKC4uLmFyZ3MpID0+IHtcbiAgICByZXR1cm4gdHlwZW9mIGNlbnNvciA9PT0gJ2Z1bmN0aW9uJyA/IHNlcmlhbGl6ZShjZW5zb3IoLi4uYXJncykpIDogc2VyaWFsaXplKGNlbnNvcilcbiAgfVxuXG4gIHJldHVybiBbLi4uT2JqZWN0LmtleXMoc2hhcGUpLCAuLi5PYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHNoYXBlKV0ucmVkdWNlKChvLCBrKSA9PiB7XG4gICAgLy8gdG9wIGxldmVsIGtleTpcbiAgICBpZiAoc2hhcGVba10gPT09IG51bGwpIHtcbiAgICAgIG9ba10gPSAodmFsdWUpID0+IHRvcENlbnNvcih2YWx1ZSwgW2tdKVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCB3cmFwcGVkQ2Vuc29yID0gdHlwZW9mIGNlbnNvciA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgICA/ICh2YWx1ZSwgcGF0aCkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGNlbnNvcih2YWx1ZSwgW2ssIC4uLnBhdGhdKVxuICAgICAgICAgIH1cbiAgICAgICAgOiBjZW5zb3JcbiAgICAgIG9ba10gPSBmYXN0UmVkYWN0KHtcbiAgICAgICAgcGF0aHM6IHNoYXBlW2tdLFxuICAgICAgICBjZW5zb3I6IHdyYXBwZWRDZW5zb3IsXG4gICAgICAgIHNlcmlhbGl6ZSxcbiAgICAgICAgc3RyaWN0XG4gICAgICB9KVxuICAgIH1cbiAgICByZXR1cm4gb1xuICB9LCByZXN1bHQpXG59XG5cbmZ1bmN0aW9uIGhhbmRsZSAob3B0cykge1xuICBpZiAoQXJyYXkuaXNBcnJheShvcHRzKSkge1xuICAgIG9wdHMgPSB7IHBhdGhzOiBvcHRzLCBjZW5zb3I6IENFTlNPUiB9XG4gICAgdmFsaWRhdGUob3B0cylcbiAgICByZXR1cm4gb3B0c1xuICB9XG4gIGxldCB7IHBhdGhzLCBjZW5zb3IgPSBDRU5TT1IsIHJlbW92ZSB9ID0gb3B0c1xuICBpZiAoQXJyYXkuaXNBcnJheShwYXRocykgPT09IGZhbHNlKSB7IHRocm93IEVycm9yKCdwaW5vIOKAkyByZWRhY3QgbXVzdCBjb250YWluIGFuIGFycmF5IG9mIHN0cmluZ3MnKSB9XG4gIGlmIChyZW1vdmUgPT09IHRydWUpIGNlbnNvciA9IHVuZGVmaW5lZFxuICB2YWxpZGF0ZSh7IHBhdGhzLCBjZW5zb3IgfSlcblxuICByZXR1cm4geyBwYXRocywgY2Vuc29yIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSByZWRhY3Rpb25cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/redaction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/symbols.js":
/*!******************************************!*\
  !*** ./node_modules/pino/lib/symbols.js ***!
  \******************************************/
/***/ ((module) => {

eval("\n\nconst setLevelSym = Symbol('pino.setLevel')\nconst getLevelSym = Symbol('pino.getLevel')\nconst levelValSym = Symbol('pino.levelVal')\nconst useLevelLabelsSym = Symbol('pino.useLevelLabels')\nconst useOnlyCustomLevelsSym = Symbol('pino.useOnlyCustomLevels')\nconst mixinSym = Symbol('pino.mixin')\n\nconst lsCacheSym = Symbol('pino.lsCache')\nconst chindingsSym = Symbol('pino.chindings')\nconst parsedChindingsSym = Symbol('pino.parsedChindings')\n\nconst asJsonSym = Symbol('pino.asJson')\nconst writeSym = Symbol('pino.write')\nconst redactFmtSym = Symbol('pino.redactFmt')\n\nconst timeSym = Symbol('pino.time')\nconst timeSliceIndexSym = Symbol('pino.timeSliceIndex')\nconst streamSym = Symbol('pino.stream')\nconst stringifySym = Symbol('pino.stringify')\nconst stringifySafeSym = Symbol('pino.stringifySafe')\nconst stringifiersSym = Symbol('pino.stringifiers')\nconst endSym = Symbol('pino.end')\nconst formatOptsSym = Symbol('pino.formatOpts')\nconst messageKeySym = Symbol('pino.messageKey')\nconst nestedKeySym = Symbol('pino.nestedKey')\nconst nestedKeyStrSym = Symbol('pino.nestedKeyStr')\nconst mixinMergeStrategySym = Symbol('pino.mixinMergeStrategy')\n\nconst wildcardFirstSym = Symbol('pino.wildcardFirst')\n\n// public symbols, no need to use the same pino\n// version for these\nconst serializersSym = Symbol.for('pino.serializers')\nconst formattersSym = Symbol.for('pino.formatters')\nconst hooksSym = Symbol.for('pino.hooks')\nconst needsMetadataGsym = Symbol.for('pino.metadata')\n\nmodule.exports = {\n  setLevelSym,\n  getLevelSym,\n  levelValSym,\n  useLevelLabelsSym,\n  mixinSym,\n  lsCacheSym,\n  chindingsSym,\n  parsedChindingsSym,\n  asJsonSym,\n  writeSym,\n  serializersSym,\n  redactFmtSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  nestedKeySym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/symbols.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/time.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/time.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nconst nullTime = () => ''\n\nconst epochTime = () => `,\"time\":${Date.now()}`\n\nconst unixTime = () => `,\"time\":${Math.round(Date.now() / 1000.0)}`\n\nconst isoTime = () => `,\"time\":\"${new Date(Date.now()).toISOString()}\"` // using Date.now() for testability\n\nmodule.exports = { nullTime, epochTime, unixTime, isoTime }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvdGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxtQ0FBbUMsV0FBVzs7QUFFOUMsa0NBQWtDLGdDQUFnQzs7QUFFbEUsa0NBQWtDLG1DQUFtQzs7QUFFckUsbUJBQW1CIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXHBpbm9cXGxpYlxcdGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgbnVsbFRpbWUgPSAoKSA9PiAnJ1xuXG5jb25zdCBlcG9jaFRpbWUgPSAoKSA9PiBgLFwidGltZVwiOiR7RGF0ZS5ub3coKX1gXG5cbmNvbnN0IHVuaXhUaW1lID0gKCkgPT4gYCxcInRpbWVcIjoke01hdGgucm91bmQoRGF0ZS5ub3coKSAvIDEwMDAuMCl9YFxuXG5jb25zdCBpc29UaW1lID0gKCkgPT4gYCxcInRpbWVcIjpcIiR7bmV3IERhdGUoRGF0ZS5ub3coKSkudG9JU09TdHJpbmcoKX1cImAgLy8gdXNpbmcgRGF0ZS5ub3coKSBmb3IgdGVzdGFiaWxpdHlcblxubW9kdWxlLmV4cG9ydHMgPSB7IG51bGxUaW1lLCBlcG9jaFRpbWUsIHVuaXhUaW1lLCBpc29UaW1lIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/tools.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/tools.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst format = __webpack_require__(/*! quick-format-unescaped */ \"(ssr)/./node_modules/quick-format-unescaped/index.js\")\nconst { mapHttpRequest, mapHttpResponse } = __webpack_require__(/*! pino-std-serializers */ \"(ssr)/./node_modules/pino-std-serializers/index.js\")\nconst SonicBoom = __webpack_require__(/*! sonic-boom */ \"(ssr)/./node_modules/sonic-boom/index.js\")\nconst warning = __webpack_require__(/*! ./deprecations */ \"(ssr)/./node_modules/pino/lib/deprecations.js\")\nconst {\n  lsCacheSym,\n  chindingsSym,\n  parsedChindingsSym,\n  writeSym,\n  serializersSym,\n  formatOptsSym,\n  endSym,\n  stringifiersSym,\n  stringifySym,\n  stringifySafeSym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  streamSym,\n  nestedKeySym,\n  formattersSym,\n  messageKeySym,\n  nestedKeyStrSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { isMainThread } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst transport = __webpack_require__(/*! ./transport */ \"(ssr)/./node_modules/pino/lib/transport.js\")\n\nfunction noop () {}\n\nfunction genLog (level, hook) {\n  if (!hook) return LOG\n\n  return function hookWrappedLog (...args) {\n    hook.call(this, args, LOG, level)\n  }\n\n  function LOG (o, ...n) {\n    if (typeof o === 'object') {\n      let msg = o\n      if (o !== null) {\n        if (o.method && o.headers && o.socket) {\n          o = mapHttpRequest(o)\n        } else if (typeof o.setHeader === 'function') {\n          o = mapHttpResponse(o)\n        }\n      }\n      let formatParams\n      if (msg === null && n.length === 0) {\n        formatParams = [null]\n      } else {\n        msg = n.shift()\n        formatParams = n\n      }\n      this[writeSym](o, format(msg, formatParams, this[formatOptsSym]), level)\n    } else {\n      this[writeSym](null, format(o, n, this[formatOptsSym]), level)\n    }\n  }\n}\n\n// magically escape strings for json\n// relying on their charCodeAt\n// everything below 32 needs JSON.stringify()\n// 34 and 92 happens all the time, so we\n// have a fast case for them\nfunction asString (str) {\n  let result = ''\n  let last = 0\n  let found = false\n  let point = 255\n  const l = str.length\n  if (l > 100) {\n    return JSON.stringify(str)\n  }\n  for (var i = 0; i < l && point >= 32; i++) {\n    point = str.charCodeAt(i)\n    if (point === 34 || point === 92) {\n      result += str.slice(last, i) + '\\\\'\n      last = i\n      found = true\n    }\n  }\n  if (!found) {\n    result = str\n  } else {\n    result += str.slice(last)\n  }\n  return point < 32 ? JSON.stringify(str) : '\"' + result + '\"'\n}\n\nfunction asJson (obj, msg, num, time) {\n  const stringify = this[stringifySym]\n  const stringifySafe = this[stringifySafeSym]\n  const stringifiers = this[stringifiersSym]\n  const end = this[endSym]\n  const chindings = this[chindingsSym]\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const messageKey = this[messageKeySym]\n  let data = this[lsCacheSym][num] + time\n\n  // we need the child bindings added to the output first so instance logged\n  // objects can take precedence when JSON.parse-ing the resulting log line\n  data = data + chindings\n\n  let value\n  if (formatters.log) {\n    obj = formatters.log(obj)\n  }\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  let propStr = ''\n  for (const key in obj) {\n    value = obj[key]\n    if (Object.prototype.hasOwnProperty.call(obj, key) && value !== undefined) {\n      value = serializers[key] ? serializers[key](value) : value\n\n      const stringifier = stringifiers[key] || wildcardStringifier\n\n      switch (typeof value) {\n        case 'undefined':\n        case 'function':\n          continue\n        case 'number':\n          /* eslint no-fallthrough: \"off\" */\n          if (Number.isFinite(value) === false) {\n            value = null\n          }\n        // this case explicitly falls through to the next one\n        case 'boolean':\n          if (stringifier) value = stringifier(value)\n          break\n        case 'string':\n          value = (stringifier || asString)(value)\n          break\n        default:\n          value = (stringifier || stringify)(value, stringifySafe)\n      }\n      if (value === undefined) continue\n      propStr += ',\"' + key + '\":' + value\n    }\n  }\n\n  let msgStr = ''\n  if (msg !== undefined) {\n    value = serializers[messageKey] ? serializers[messageKey](msg) : msg\n    const stringifier = stringifiers[messageKey] || wildcardStringifier\n\n    switch (typeof value) {\n      case 'function':\n        break\n      case 'number':\n        /* eslint no-fallthrough: \"off\" */\n        if (Number.isFinite(value) === false) {\n          value = null\n        }\n      // this case explicitly falls through to the next one\n      case 'boolean':\n        if (stringifier) value = stringifier(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      case 'string':\n        value = (stringifier || asString)(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      default:\n        value = (stringifier || stringify)(value, stringifySafe)\n        msgStr = ',\"' + messageKey + '\":' + value\n    }\n  }\n\n  if (this[nestedKeySym] && propStr) {\n    // place all the obj properties under the specified key\n    // the nested key is already formatted from the constructor\n    return data + this[nestedKeyStrSym] + propStr.slice(1) + '}' + msgStr + end\n  } else {\n    return data + propStr + msgStr + end\n  }\n}\n\nfunction asChindings (instance, bindings) {\n  let value\n  let data = instance[chindingsSym]\n  const stringify = instance[stringifySym]\n  const stringifySafe = instance[stringifySafeSym]\n  const stringifiers = instance[stringifiersSym]\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  const serializers = instance[serializersSym]\n  const formatter = instance[formattersSym].bindings\n  bindings = formatter(bindings)\n\n  for (const key in bindings) {\n    value = bindings[key]\n    const valid = key !== 'level' &&\n      key !== 'serializers' &&\n      key !== 'formatters' &&\n      key !== 'customLevels' &&\n      bindings.hasOwnProperty(key) &&\n      value !== undefined\n    if (valid === true) {\n      value = serializers[key] ? serializers[key](value) : value\n      value = (stringifiers[key] || wildcardStringifier || stringify)(value, stringifySafe)\n      if (value === undefined) continue\n      data += ',\"' + key + '\":' + value\n    }\n  }\n  return data\n}\n\nfunction getPrettyStream (opts, prettifier, dest, instance) {\n  if (prettifier && typeof prettifier === 'function') {\n    prettifier = prettifier.bind(instance)\n    return prettifierMetaWrapper(prettifier(opts), dest, opts)\n  }\n  try {\n    const prettyFactory = (__webpack_require__(/*! pino-pretty */ \"pino-pretty\").prettyFactory)\n    prettyFactory.asMetaWrapper = prettifierMetaWrapper\n    return prettifierMetaWrapper(prettyFactory(opts), dest, opts)\n  } catch (e) {\n    if (e.message.startsWith(\"Cannot find module 'pino-pretty'\")) {\n      throw Error('Missing `pino-pretty` module: `pino-pretty` must be installed separately')\n    };\n    throw e\n  }\n}\n\nfunction prettifierMetaWrapper (pretty, dest, opts) {\n  opts = Object.assign({ suppressFlushSyncWarning: false }, opts)\n  let warned = false\n  return {\n    [needsMetadataGsym]: true,\n    lastLevel: 0,\n    lastMsg: null,\n    lastObj: null,\n    lastLogger: null,\n    flushSync () {\n      if (opts.suppressFlushSyncWarning || warned) {\n        return\n      }\n      warned = true\n      setMetadataProps(dest, this)\n      dest.write(pretty(Object.assign({\n        level: 40, // warn\n        msg: 'pino.final with prettyPrint does not support flushing',\n        time: Date.now()\n      }, this.chindings())))\n    },\n    chindings () {\n      const lastLogger = this.lastLogger\n      let chindings = null\n\n      // protection against flushSync being called before logging\n      // anything\n      if (!lastLogger) {\n        return null\n      }\n\n      if (lastLogger.hasOwnProperty(parsedChindingsSym)) {\n        chindings = lastLogger[parsedChindingsSym]\n      } else {\n        chindings = JSON.parse('{' + lastLogger[chindingsSym].substr(1) + '}')\n        lastLogger[parsedChindingsSym] = chindings\n      }\n\n      return chindings\n    },\n    write (chunk) {\n      const lastLogger = this.lastLogger\n      const chindings = this.chindings()\n\n      let time = this.lastTime\n\n      /* istanbul ignore next */\n      if (typeof time === 'number') {\n        // do nothing!\n      } else if (time.match(/^\\d+/)) {\n        time = parseInt(time)\n      } else {\n        time = time.slice(1, -1)\n      }\n\n      const lastObj = this.lastObj\n      const lastMsg = this.lastMsg\n      const errorProps = null\n\n      const formatters = lastLogger[formattersSym]\n      const formattedObj = formatters.log ? formatters.log(lastObj) : lastObj\n\n      const messageKey = lastLogger[messageKeySym]\n      if (lastMsg && formattedObj && !Object.prototype.hasOwnProperty.call(formattedObj, messageKey)) {\n        formattedObj[messageKey] = lastMsg\n      }\n\n      const obj = Object.assign({\n        level: this.lastLevel,\n        time\n      }, formattedObj, errorProps)\n\n      const serializers = lastLogger[serializersSym]\n      const keys = Object.keys(serializers)\n\n      for (var i = 0; i < keys.length; i++) {\n        const key = keys[i]\n        if (obj[key] !== undefined) {\n          obj[key] = serializers[key](obj[key])\n        }\n      }\n\n      for (const key in chindings) {\n        if (!obj.hasOwnProperty(key)) {\n          obj[key] = chindings[key]\n        }\n      }\n\n      const stringifiers = lastLogger[stringifiersSym]\n      const redact = stringifiers[redactFmtSym]\n\n      const formatted = pretty(typeof redact === 'function' ? redact(obj) : obj)\n      if (formatted === undefined) return\n\n      setMetadataProps(dest, this)\n      dest.write(formatted)\n    }\n  }\n}\n\nfunction hasBeenTampered (stream) {\n  return stream.write !== stream.constructor.prototype.write\n}\n\nfunction buildSafeSonicBoom (opts) {\n  const stream = new SonicBoom(opts)\n  stream.on('error', filterBrokenPipe)\n  // if we are sync: false, we must flush on exit\n  if (!opts.sync && isMainThread) {\n    setupOnExit(stream)\n  }\n  return stream\n\n  function filterBrokenPipe (err) {\n    // TODO verify on Windows\n    if (err.code === 'EPIPE') {\n      // If we get EPIPE, we should stop logging here\n      // however we have no control to the consumer of\n      // SonicBoom, so we just overwrite the write method\n      stream.write = noop\n      stream.end = noop\n      stream.flushSync = noop\n      stream.destroy = noop\n      return\n    }\n    stream.removeListener('error', filterBrokenPipe)\n    stream.emit('error', err)\n  }\n}\n\nfunction setupOnExit (stream) {\n  /* istanbul ignore next */\n  if (global.WeakRef && global.WeakMap && global.FinalizationRegistry) {\n    // This is leak free, it does not leave event handlers\n    const onExit = __webpack_require__(/*! on-exit-leak-free */ \"(ssr)/./node_modules/on-exit-leak-free/index.js\")\n\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  }\n}\n\nfunction autoEnd (stream, eventName) {\n  // This check is needed only on some platforms\n  /* istanbul ignore next */\n  if (stream.destroyed) {\n    return\n  }\n\n  if (eventName === 'beforeExit') {\n    // We still have an event loop, let's use it\n    stream.flush()\n    stream.on('drain', function () {\n      stream.end()\n    })\n  } else {\n    // We do not have an event loop, so flush synchronously\n    stream.flushSync()\n  }\n}\n\nfunction createArgsNormalizer (defaultOptions) {\n  return function normalizeArgs (instance, caller, opts = {}, stream) {\n    // support stream as a string\n    if (typeof opts === 'string') {\n      stream = buildSafeSonicBoom({ dest: opts, sync: true })\n      opts = {}\n    } else if (typeof stream === 'string') {\n      if (opts && opts.transport) {\n        throw Error('only one of option.transport or stream can be specified')\n      }\n      stream = buildSafeSonicBoom({ dest: stream, sync: true })\n    } else if (opts instanceof SonicBoom || opts.writable || opts._writableState) {\n      stream = opts\n      opts = {}\n    } else if (opts.transport) {\n      if (opts.transport instanceof SonicBoom || opts.transport.writable || opts.transport._writableState) {\n        throw Error('option.transport do not allow stream, please pass to option directly. e.g. pino(transport)')\n      }\n      if (opts.transport.targets && opts.transport.targets.length && opts.formatters && typeof opts.formatters.level === 'function') {\n        throw Error('option.transport.targets do not allow custom level formatters')\n      }\n\n      let customLevels\n      if (opts.customLevels) {\n        customLevels = opts.useOnlyCustomLevels ? opts.customLevels : Object.assign({}, opts.levels, opts.customLevels)\n      }\n      stream = transport({ caller, ...opts.transport, levels: customLevels })\n    }\n    opts = Object.assign({}, defaultOptions, opts)\n    opts.serializers = Object.assign({}, defaultOptions.serializers, opts.serializers)\n    opts.formatters = Object.assign({}, defaultOptions.formatters, opts.formatters)\n\n    if ('onTerminated' in opts) {\n      throw Error('The onTerminated option has been removed, use pino.final instead')\n    }\n    if ('changeLevelName' in opts) {\n      process.emitWarning(\n        'The changeLevelName option is deprecated and will be removed in v7. Use levelKey instead.',\n        { code: 'changeLevelName_deprecation' }\n      )\n      opts.levelKey = opts.changeLevelName\n      delete opts.changeLevelName\n    }\n    const { enabled, prettyPrint, prettifier, messageKey } = opts\n    if (enabled === false) opts.level = 'silent'\n    stream = stream || process.stdout\n    if (stream === process.stdout && stream.fd >= 0 && !hasBeenTampered(stream)) {\n      stream = buildSafeSonicBoom({ fd: stream.fd, sync: true })\n    }\n    if (prettyPrint) {\n      warning.emit('PINODEP008')\n      const prettyOpts = Object.assign({ messageKey }, prettyPrint)\n      stream = getPrettyStream(prettyOpts, prettifier, stream, instance)\n    }\n    return { opts, stream }\n  }\n}\n\nfunction final (logger, handler) {\n  const major = Number(process.versions.node.split('.')[0])\n  if (major >= 14) warning.emit('PINODEP009')\n\n  if (typeof logger === 'undefined' || typeof logger.child !== 'function') {\n    throw Error('expected a pino logger instance')\n  }\n  const hasHandler = (typeof handler !== 'undefined')\n  if (hasHandler && typeof handler !== 'function') {\n    throw Error('if supplied, the handler parameter should be a function')\n  }\n  const stream = logger[streamSym]\n  if (typeof stream.flushSync !== 'function') {\n    throw Error('final requires a stream that has a flushSync method, such as pino.destination')\n  }\n\n  const finalLogger = new Proxy(logger, {\n    get: (logger, key) => {\n      if (key in logger.levels.values) {\n        return (...args) => {\n          logger[key](...args)\n          stream.flushSync()\n        }\n      }\n      return logger[key]\n    }\n  })\n\n  if (!hasHandler) {\n    try {\n      stream.flushSync()\n    } catch {\n      // it's too late to wait for the stream to be ready\n      // because this is a final tick scenario.\n      // in practice there shouldn't be a situation where it isn't\n      // however, swallow the error just in case (and for easier testing)\n    }\n    return finalLogger\n  }\n\n  return (err = null, ...args) => {\n    try {\n      stream.flushSync()\n    } catch (e) {\n      // it's too late to wait for the stream to be ready\n      // because this is a final tick scenario.\n      // in practice there shouldn't be a situation where it isn't\n      // however, swallow the error just in case (and for easier testing)\n    }\n    return handler(err, finalLogger, ...args)\n  }\n}\n\nfunction stringify (obj, stringifySafeFn) {\n  try {\n    return JSON.stringify(obj)\n  } catch (_) {\n    try {\n      const stringify = stringifySafeFn || this[stringifySafeSym]\n      return stringify(obj)\n    } catch (_) {\n      return '\"[unable to serialize, circular reference is too complex to analyze]\"'\n    }\n  }\n}\n\nfunction buildFormatters (level, bindings, log) {\n  return {\n    level,\n    bindings,\n    log\n  }\n}\n\nfunction setMetadataProps (dest, that) {\n  if (dest[needsMetadataGsym] === true) {\n    dest.lastLevel = that.lastLevel\n    dest.lastMsg = that.lastMsg\n    dest.lastObj = that.lastObj\n    dest.lastTime = that.lastTime\n    dest.lastLogger = that.lastLogger\n  }\n}\n\n/**\n * Convert a string integer file descriptor to a proper native integer\n * file descriptor.\n *\n * @param {string} destination The file descriptor string to attempt to convert.\n *\n * @returns {Number}\n */\nfunction normalizeDestFileDescriptor (destination) {\n  const fd = Number(destination)\n  if (typeof destination === 'string' && Number.isFinite(fd)) {\n    return fd\n  }\n  return destination\n}\n\nmodule.exports = {\n  noop,\n  buildSafeSonicBoom,\n  getPrettyStream,\n  asChindings,\n  asJson,\n  genLog,\n  createArgsNormalizer,\n  final,\n  stringify,\n  buildFormatters,\n  normalizeDestFileDescriptor\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/tools.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/transport.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/transport.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { createRequire } = __webpack_require__(/*! module */ \"module\")\nconst getCallers = __webpack_require__(/*! ./caller */ \"(ssr)/./node_modules/pino/lib/caller.js\")\nconst { join, isAbsolute } = __webpack_require__(/*! path */ \"path\")\nconst sleep = __webpack_require__(/*! atomic-sleep */ \"(ssr)/./node_modules/atomic-sleep/index.js\")\n\nlet onExit\n\nif (global.WeakRef && global.WeakMap && global.FinalizationRegistry) {\n  // This require MUST be top level otherwise the transport would\n  // not work from within Jest as it hijacks require.\n  onExit = __webpack_require__(/*! on-exit-leak-free */ \"(ssr)/./node_modules/on-exit-leak-free/index.js\")\n}\n\nconst ThreadStream = __webpack_require__(/*! thread-stream */ \"(ssr)/./node_modules/thread-stream/index.js\")\n\nfunction setupOnExit (stream) {\n  /* istanbul ignore next */\n  if (onExit) {\n    // This is leak free, it does not leave event handlers\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  } else {\n    const fn = autoEnd.bind(null, stream)\n    process.once('beforeExit', fn)\n    process.once('exit', fn)\n\n    stream.on('close', function () {\n      process.removeListener('beforeExit', fn)\n      process.removeListener('exit', fn)\n    })\n  }\n}\n\nfunction buildStream (filename, workerData, workerOpts) {\n  const stream = new ThreadStream({\n    filename,\n    workerData,\n    workerOpts\n  })\n\n  stream.on('ready', onReady)\n  stream.on('close', function () {\n    process.removeListener('exit', onExit)\n  })\n\n  process.on('exit', onExit)\n\n  function onReady () {\n    process.removeListener('exit', onExit)\n    stream.unref()\n\n    if (workerOpts.autoEnd !== false) {\n      setupOnExit(stream)\n    }\n  }\n\n  function onExit () {\n    if (stream.closed) {\n      return\n    }\n    stream.flushSync()\n    // Apparently there is a very sporadic race condition\n    // that in certain OS would prevent the messages to be flushed\n    // because the thread might not have been created still.\n    // Unfortunately we need to sleep(100) in this case.\n    sleep(100)\n    stream.end()\n  }\n\n  return stream\n}\n\nfunction autoEnd (stream) {\n  stream.ref()\n  stream.flushSync()\n  stream.end()\n  stream.once('close', function () {\n    stream.unref()\n  })\n}\n\nfunction transport (fullOptions) {\n  const { pipeline, targets, levels, options = {}, worker = {}, caller = getCallers() } = fullOptions\n\n  // Backwards compatibility\n  const callers = typeof caller === 'string' ? [caller] : caller\n\n  // This will be eventually modified by bundlers\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n\n  let target = fullOptions.target\n\n  if (target && targets) {\n    throw new Error('only one of target or targets can be specified')\n  }\n\n  if (targets) {\n    target = bundlerOverrides['pino-worker'] || join(__dirname, 'worker.js')\n    options.targets = targets.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n  } else if (pipeline) {\n    target = bundlerOverrides['pino-pipeline-worker'] || join(__dirname, 'worker-pipeline.js')\n    options.targets = pipeline.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n  }\n\n  if (levels) {\n    options.levels = levels\n  }\n\n  return buildStream(fixTarget(target), options, worker)\n\n  function fixTarget (origin) {\n    origin = bundlerOverrides[origin] || origin\n\n    if (isAbsolute(origin) || origin.indexOf('file://') === 0) {\n      return origin\n    }\n\n    if (origin === 'pino/file') {\n      return join(__dirname, '..', 'file.js')\n    }\n\n    let fixTarget\n\n    for (const filePath of callers) {\n      try {\n        fixTarget = createRequire(filePath).resolve(origin)\n        break\n      } catch (err) {\n        // Silent catch\n        continue\n      }\n    }\n\n    if (!fixTarget) {\n      throw new Error(`unable to determine transport target for \"${origin}\"`)\n    }\n\n    return fixTarget\n  }\n}\n\nmodule.exports = transport\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/package.json":
/*!****************************************!*\
  !*** ./node_modules/pino/package.json ***!
  \****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"pino","version":"7.11.0","description":"super fast, all natural json logger","main":"pino.js","type":"commonjs","types":"pino.d.ts","browser":"./browser.js","files":["pino.js","file.js","pino.d.ts","bin.js","browser.js","pretty.js","usage.txt","test","docs","example.js","lib"],"scripts":{"docs":"docsify serve","browser-test":"airtap --local 8080 test/browser*test.js","lint":"eslint .","test":"npm run lint && npm run transpile && tap --ts && jest test/jest && npm run test-types","test-ci":"npm run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly && npm run test-types","test-ci-pnpm":"pnpm run lint && npm run transpile && tap --ts --no-coverage --no-check-coverage && pnpm run test-types","test-ci-yarn-pnp":"yarn run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly","test-types":"tsc && tsd && ts-node test/types/pino.ts","transpile":"node ./test/fixtures/ts/transpile.cjs","cov-ui":"tap --ts --coverage-report=html","bench":"node benchmarks/utils/runbench all","bench-basic":"node benchmarks/utils/runbench basic","bench-object":"node benchmarks/utils/runbench object","bench-deep-object":"node benchmarks/utils/runbench deep-object","bench-multi-arg":"node benchmarks/utils/runbench multi-arg","bench-longs-tring":"node benchmarks/utils/runbench long-string","bench-child":"node benchmarks/utils/runbench child","bench-child-child":"node benchmarks/utils/runbench child-child","bench-child-creation":"node benchmarks/utils/runbench child-creation","bench-formatters":"node benchmarks/utils/runbench formatters","update-bench-doc":"node benchmarks/utils/generate-benchmark-doc > docs/benchmarks.md"},"bin":{"pino":"./bin.js"},"precommit":"test","repository":{"type":"git","url":"git+https://github.com/pinojs/pino.git"},"keywords":["fast","logger","stream","json"],"author":"Matteo Collina <<EMAIL>>","contributors":["David Mark Clements <<EMAIL>>","James Sumners <<EMAIL>>","Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)"],"license":"MIT","bugs":{"url":"https://github.com/pinojs/pino/issues"},"homepage":"http://getpino.io","devDependencies":{"@types/flush-write-stream":"^1.0.0","@types/node":"^17.0.0","@types/tap":"^15.0.6","airtap":"4.0.4","benchmark":"^2.1.4","bole":"^4.0.0","bunyan":"^1.8.14","docsify-cli":"^4.4.1","eslint":"^7.17.0","eslint-config-standard":"^16.0.3","eslint-plugin-import":"^2.22.1","eslint-plugin-node":"^11.1.0","eslint-plugin-promise":"^5.1.0","execa":"^5.0.0","fastbench":"^1.0.1","flush-write-stream":"^2.0.0","import-fresh":"^3.2.1","jest":"^27.3.1","log":"^6.0.0","loglevel":"^1.6.7","pino-pretty":"^v7.6.0","pre-commit":"^1.2.2","proxyquire":"^2.1.3","pump":"^3.0.0","rimraf":"^3.0.2","semver":"^7.0.0","split2":"^4.0.0","steed":"^1.1.3","strip-ansi":"^6.0.0","tap":"^16.0.0","tape":"^5.0.0","through2":"^4.0.0","ts-node":"^10.7.0","tsd":"^0.20.0","typescript":"^4.4.4","winston":"^3.3.3"},"dependencies":{"atomic-sleep":"^1.0.0","fast-redact":"^3.0.0","on-exit-leak-free":"^0.2.0","pino-abstract-transport":"v0.5.0","pino-std-serializers":"^4.0.0","process-warning":"^1.0.0","quick-format-unescaped":"^4.0.3","real-require":"^0.1.0","safe-stable-stringify":"^2.1.0","sonic-boom":"^2.2.1","thread-stream":"^0.15.1"},"tsd":{"directory":"test/types"}}');

/***/ }),

/***/ "(ssr)/./node_modules/pino/pino.js":
/*!***********************************!*\
  !*** ./node_modules/pino/pino.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/* eslint no-prototype-builtins: 0 */\nconst os = __webpack_require__(/*! os */ \"os\")\nconst stdSerializers = __webpack_require__(/*! pino-std-serializers */ \"(ssr)/./node_modules/pino-std-serializers/index.js\")\nconst caller = __webpack_require__(/*! ./lib/caller */ \"(ssr)/./node_modules/pino/lib/caller.js\")\nconst redaction = __webpack_require__(/*! ./lib/redaction */ \"(ssr)/./node_modules/pino/lib/redaction.js\")\nconst time = __webpack_require__(/*! ./lib/time */ \"(ssr)/./node_modules/pino/lib/time.js\")\nconst proto = __webpack_require__(/*! ./lib/proto */ \"(ssr)/./node_modules/pino/lib/proto.js\")\nconst symbols = __webpack_require__(/*! ./lib/symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { configure } = __webpack_require__(/*! safe-stable-stringify */ \"(ssr)/./node_modules/safe-stable-stringify/index.js\")\nconst { assertDefaultLevelFound, mappings, genLsCache, levels } = __webpack_require__(/*! ./lib/levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\nconst {\n  createArgsNormalizer,\n  asChindings,\n  final,\n  buildSafeSonicBoom,\n  buildFormatters,\n  stringify,\n  normalizeDestFileDescriptor,\n  noop\n} = __webpack_require__(/*! ./lib/tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\nconst { version } = __webpack_require__(/*! ./lib/meta */ \"(ssr)/./node_modules/pino/lib/meta.js\")\nconst {\n  chindingsSym,\n  redactFmtSym,\n  serializersSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  setLevelSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  nestedKeySym,\n  mixinSym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym\n} = symbols\nconst { epochTime, nullTime } = time\nconst { pid } = process\nconst hostname = os.hostname()\nconst defaultErrorSerializer = stdSerializers.err\nconst defaultOptions = {\n  level: 'info',\n  levels,\n  messageKey: 'msg',\n  nestedKey: null,\n  enabled: true,\n  prettyPrint: false,\n  base: { pid, hostname },\n  serializers: Object.assign(Object.create(null), {\n    err: defaultErrorSerializer\n  }),\n  formatters: Object.assign(Object.create(null), {\n    bindings (bindings) {\n      return bindings\n    },\n    level (label, number) {\n      return { level: number }\n    }\n  }),\n  hooks: {\n    logMethod: undefined\n  },\n  timestamp: epochTime,\n  name: undefined,\n  redact: null,\n  customLevels: null,\n  useOnlyCustomLevels: false,\n  depthLimit: 5,\n  edgeLimit: 100\n}\n\nconst normalize = createArgsNormalizer(defaultOptions)\n\nconst serializers = Object.assign(Object.create(null), stdSerializers)\n\nfunction pino (...args) {\n  const instance = {}\n  const { opts, stream } = normalize(instance, caller(), ...args)\n  const {\n    redact,\n    crlf,\n    serializers,\n    timestamp,\n    messageKey,\n    nestedKey,\n    base,\n    name,\n    level,\n    customLevels,\n    mixin,\n    mixinMergeStrategy,\n    useOnlyCustomLevels,\n    formatters,\n    hooks,\n    depthLimit,\n    edgeLimit\n  } = opts\n\n  const stringifySafe = configure({\n    maximumDepth: depthLimit,\n    maximumBreadth: edgeLimit\n  })\n\n  const allFormatters = buildFormatters(\n    formatters.level,\n    formatters.bindings,\n    formatters.log\n  )\n\n  const stringifiers = redact ? redaction(redact, stringify) : {}\n  const stringifyFn = stringify.bind({\n    [stringifySafeSym]: stringifySafe\n  })\n  const formatOpts = redact\n    ? { stringify: stringifiers[redactFmtSym] }\n    : { stringify: stringifyFn }\n  const end = '}' + (crlf ? '\\r\\n' : '\\n')\n  const coreChindings = asChindings.bind(null, {\n    [chindingsSym]: '',\n    [serializersSym]: serializers,\n    [stringifiersSym]: stringifiers,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [formattersSym]: allFormatters\n  })\n\n  let chindings = ''\n  if (base !== null) {\n    if (name === undefined) {\n      chindings = coreChindings(base)\n    } else {\n      chindings = coreChindings(Object.assign({}, base, { name }))\n    }\n  }\n\n  const time = (timestamp instanceof Function)\n    ? timestamp\n    : (timestamp ? epochTime : nullTime)\n  const timeSliceIndex = time().indexOf(':') + 1\n\n  if (useOnlyCustomLevels && !customLevels) throw Error('customLevels is required if useOnlyCustomLevels is set true')\n  if (mixin && typeof mixin !== 'function') throw Error(`Unknown mixin type \"${typeof mixin}\" - expected \"function\"`)\n\n  assertDefaultLevelFound(level, customLevels, useOnlyCustomLevels)\n  const levels = mappings(customLevels, useOnlyCustomLevels)\n\n  Object.assign(instance, {\n    levels,\n    [useOnlyCustomLevelsSym]: useOnlyCustomLevels,\n    [streamSym]: stream,\n    [timeSym]: time,\n    [timeSliceIndexSym]: timeSliceIndex,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [stringifiersSym]: stringifiers,\n    [endSym]: end,\n    [formatOptsSym]: formatOpts,\n    [messageKeySym]: messageKey,\n    [nestedKeySym]: nestedKey,\n    // protect against injection\n    [nestedKeyStrSym]: nestedKey ? `,${JSON.stringify(nestedKey)}:{` : '',\n    [serializersSym]: serializers,\n    [mixinSym]: mixin,\n    [mixinMergeStrategySym]: mixinMergeStrategy,\n    [chindingsSym]: chindings,\n    [formattersSym]: allFormatters,\n    [hooksSym]: hooks,\n    silent: noop\n  })\n\n  Object.setPrototypeOf(instance, proto())\n\n  genLsCache(instance)\n\n  instance[setLevelSym](level)\n\n  return instance\n}\n\nmodule.exports = pino\n\nmodule.exports.destination = (dest = process.stdout.fd) => {\n  if (typeof dest === 'object') {\n    dest.dest = normalizeDestFileDescriptor(dest.dest || process.stdout.fd)\n    return buildSafeSonicBoom(dest)\n  } else {\n    return buildSafeSonicBoom({ dest: normalizeDestFileDescriptor(dest), minLength: 0, sync: true })\n  }\n}\n\nmodule.exports.transport = __webpack_require__(/*! ./lib/transport */ \"(ssr)/./node_modules/pino/lib/transport.js\")\nmodule.exports.multistream = __webpack_require__(/*! ./lib/multistream */ \"(ssr)/./node_modules/pino/lib/multistream.js\")\n\nmodule.exports.final = final\nmodule.exports.levels = mappings()\nmodule.exports.stdSerializers = serializers\nmodule.exports.stdTimeFunctions = Object.assign({}, time)\nmodule.exports.symbols = symbols\nmodule.exports.version = version\n\n// Enables default and name export with TypeScript and Babel\nmodule.exports[\"default\"] = pino\nmodule.exports.pino = pino\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/pino.js\n");

/***/ })

};
;