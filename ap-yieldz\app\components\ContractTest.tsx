'use client';

import { useReadContract } from 'wagmi';
import { SIMPLE_LENDING_AGGREGATOR_ABI } from '../blockchain/abi/SimpleLendingAggregator';
import { LENDING_APY_AGGREGATOR_ADDRESS } from '../blockchain/config/wagmi';

export function ContractTest() {
  // Test reading owner from the contract
  const { data: owner, error: ownerError, isLoading: ownerLoading } = useReadContract({
    address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
    abi: SIMPLE_LENDING_AGGREGATOR_ABI,
    functionName: 'owner',
  });

  // Test reading supported assets
  const { data: assets, error: assetsError, isLoading: assetsLoading } = useReadContract({
    address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
    abi: SIMPLE_LENDING_AGGREGATOR_ABI,
    functionName: 'getSupportedAssets',
  });

  return (
    <div className="bg-gray-100 p-4 rounded-lg space-y-4">
      <h3 className="font-bold text-lg">Contract Connection Test</h3>
      
      <div>
        <p><strong>Contract Address:</strong> {LENDING_APY_AGGREGATOR_ADDRESS}</p>
      </div>
      
      <div>
        <p><strong>Contract Owner:</strong></p>
        {ownerLoading ? (
          <p className="text-blue-600">Loading...</p>
        ) : ownerError ? (
          <p className="text-red-600">Error: {ownerError.message}</p>
        ) : owner ? (
          <p className="text-green-600">{String(owner)}</p>
        ) : (
          <p className="text-gray-600">No owner found</p>
        )}
      </div>
      
      <div>
        <p><strong>Supported Assets:</strong></p>
        {assetsLoading ? (
          <p className="text-blue-600">Loading...</p>
        ) : assetsError ? (
          <p className="text-red-600">Error: {assetsError.message}</p>
        ) : assets && Array.isArray(assets) && assets.length > 0 ? (
          <ul className="text-green-600">
            {(assets as string[]).map((asset, index) => (
              <li key={index}>{asset}</li>
            ))}
          </ul>
        ) : (
          <p className="text-yellow-600">No supported assets found. You need to add assets using addSupportedAsset()</p>
        )}
      </div>
      
      {(ownerError || assetsError) && (
        <div className="bg-red-50 p-3 rounded border border-red-200">
          <p className="text-red-800 font-medium">Contract Connection Issues:</p>
          <ul className="text-red-700 text-sm mt-2 space-y-1">
            <li>• Make sure the contract address is correct</li>
            <li>• Ensure you're connected to Avalanche Fuji testnet</li>
            <li>• Verify the contract is deployed and accessible</li>
          </ul>
        </div>
      )}
    </div>
  );
}
