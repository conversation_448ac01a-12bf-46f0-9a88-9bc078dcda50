"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_github_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   githubSvg: () => (/* binding */ githubSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst githubSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#1B1F23\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M8 19.89a12 12 0 1 1 15.8 11.38c-.6.12-.8-.26-.8-.57v-3.3c0-1.12-.4-1.85-.82-2.22 2.67-.3 5.48-1.31 5.48-5.92 0-1.31-.47-2.38-1.24-3.22.13-.3.54-1.52-.12-3.18 0 0-1-.32-3.3 1.23a11.54 11.54 0 0 0-6 0c-2.3-1.55-3.3-1.23-3.3-1.23a4.32 4.32 0 0 0-.12 3.18 4.64 4.64 0 0 0-1.24 3.22c0 4.6 2.8 5.63 5.47 5.93-.34.3-.65.83-.76 1.6-.69.31-2.42.84-3.5-1 0 0-.63-1.15-1.83-1.23 0 0-1.18-.02-.09.73 0 0 .8.37 1.34 1.76 0 0 .7 2.14 4.03 1.41v2.24c0 .31-.2.68-.8.57A12 12 0 0 1 8 19.9Z\"\n        />\n      </g>\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=github.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js\n"));

/***/ })

}]);