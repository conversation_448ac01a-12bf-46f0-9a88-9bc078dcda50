"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts":
/*!********************************************!*\
  !*** ./app/blockchain/hooks/useAPYData.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAPYData: () => (/* binding */ useAPYData),\n/* harmony export */   useHistoricalAPY: () => (/* binding */ useHistoricalAPY)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/aaveAPI */ \"(app-pages-browser)/./app/services/aaveAPI.ts\");\n\n\n// For Aave-only integration, we'll use mock data for now\n// Real Aave API integration can be added later\n// const AAVE_API_URL = 'https://aave-api-v2.aave.com/data/liquidity/v2';\n// Mapping of asset symbols to their respective IDs on Aave V3\nconst AAVE_RESERVE_IDS = {\n    'USDC': '******************************************',\n    'USDT': '******************************************',\n    'WETH': '******************************************',\n    'WBTC': '******************************************'\n};\n// Aave-only APY data for Fuji testnet (mock data for demonstration)\nconst AAVE_ONLY_APY_DATA = [\n    {\n        asset: 'usdc',\n        symbol: 'USDC',\n        aaveSupplyAPY: 4.25,\n        aaveBorrowAPY: 5.15,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    },\n    {\n        asset: 'wavax',\n        symbol: 'WAVAX',\n        aaveSupplyAPY: 2.85,\n        aaveBorrowAPY: 4.25,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    },\n    {\n        asset: 'usdt',\n        symbol: 'USDT',\n        aaveSupplyAPY: 4.15,\n        aaveBorrowAPY: 5.25,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    }\n];\nfunction useAPYData() {\n    const [apyData, setApyData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAPYData.useEffect\": ()=>{\n            const fetchAPYData = {\n                \"useAPYData.useEffect.fetchAPYData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        console.log('Fetching live Aave APY data...');\n                        // Fetch live rates from Aave API\n                        const liveRates = await (0,_services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__.fetchLiveAaveRates)();\n                        console.log('Raw live rates from API:', liveRates);\n                        // Check if we got valid rates\n                        const hasValidRates = Object.values(liveRates).some({\n                            \"useAPYData.useEffect.fetchAPYData.hasValidRates\": (token)=>token.supplyAPY > 0 || token.borrowAPY > 0\n                        }[\"useAPYData.useEffect.fetchAPYData.hasValidRates\"]);\n                        if (!hasValidRates) {\n                            console.log('API returned zero rates, using fallback data');\n                            throw new Error('API returned invalid rates (all zeros)');\n                        }\n                        // Convert to our APYData format\n                        const liveAPYData = [\n                            {\n                                asset: 'usdc',\n                                symbol: 'USDC',\n                                aaveSupplyAPY: liveRates.USDC.supplyAPY,\n                                aaveBorrowAPY: liveRates.USDC.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            },\n                            {\n                                asset: 'wavax',\n                                symbol: 'WAVAX',\n                                aaveSupplyAPY: liveRates.WAVAX.supplyAPY,\n                                aaveBorrowAPY: liveRates.WAVAX.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            },\n                            {\n                                asset: 'usdt',\n                                symbol: 'USDT',\n                                aaveSupplyAPY: liveRates.USDT.supplyAPY,\n                                aaveBorrowAPY: liveRates.USDT.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            }\n                        ];\n                        setApyData(liveAPYData);\n                        setError(null);\n                        console.log('Live APY data loaded successfully:', liveAPYData);\n                    } catch (err) {\n                        console.error('Error loading live APY data:', err);\n                        console.log('Falling back to mock data...');\n                        // Fallback to mock data if API fails\n                        setApyData(AAVE_ONLY_APY_DATA);\n                        setError('Using fallback APY data - live rates unavailable');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useAPYData.useEffect.fetchAPYData\"];\n            fetchAPYData();\n            // Refresh data every 60 seconds for live rates\n            const interval = setInterval(fetchAPYData, 60000);\n            return ({\n                \"useAPYData.useEffect\": ()=>clearInterval(interval)\n            })[\"useAPYData.useEffect\"];\n        }\n    }[\"useAPYData.useEffect\"], []);\n    const getAPYForAsset = (asset)=>{\n        const result = apyData.find((data)=>data.asset === asset.toLowerCase() || data.symbol === asset.toUpperCase());\n        console.log(\"Getting APY for \".concat(asset, \":\"), result);\n        return result;\n    };\n    return {\n        apyData,\n        loading,\n        error,\n        getAPYForAsset,\n        refresh: async ()=>{\n            setLoading(true);\n            try {\n                console.log('Manually refreshing APY data...');\n                // TEMPORARY: Force fallback data for testing (same as main effect)\n                const FORCE_FALLBACK = true;\n                if (FORCE_FALLBACK) {\n                    console.log('TEMPORARY: Manual refresh using fallback data for testing');\n                    setApyData(AAVE_ONLY_APY_DATA);\n                    setError('Temporarily using fallback data for testing');\n                    return;\n                }\n                const liveRates = await (0,_services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__.fetchLiveAaveRates)();\n                console.log('Manual refresh - Raw live rates:', liveRates);\n                // Check if we got valid rates\n                const hasValidRates = Object.values(liveRates).some((token)=>token.supplyAPY > 0 || token.borrowAPY > 0);\n                if (!hasValidRates) {\n                    console.log('Manual refresh - API returned zero rates, using fallback data');\n                    throw new Error('API returned invalid rates (all zeros)');\n                }\n                const liveAPYData = [\n                    {\n                        asset: 'usdc',\n                        symbol: 'USDC',\n                        aaveSupplyAPY: liveRates.USDC.supplyAPY,\n                        aaveBorrowAPY: liveRates.USDC.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    },\n                    {\n                        asset: 'wavax',\n                        symbol: 'WAVAX',\n                        aaveSupplyAPY: liveRates.WAVAX.supplyAPY,\n                        aaveBorrowAPY: liveRates.WAVAX.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    },\n                    {\n                        asset: 'usdt',\n                        symbol: 'USDT',\n                        aaveSupplyAPY: liveRates.USDT.supplyAPY,\n                        aaveBorrowAPY: liveRates.USDT.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    }\n                ];\n                setApyData(liveAPYData);\n                setError(null);\n            } catch (error) {\n                console.error('Manual refresh failed:', error);\n                setApyData(AAVE_ONLY_APY_DATA);\n                setError('Refresh failed - using fallback data');\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n}\n// Historical APY data for charts\nfunction useHistoricalAPY(asset, protocol) {\n    let days = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useHistoricalAPY.useEffect\": ()=>{\n            const generateMockHistoricalData = {\n                \"useHistoricalAPY.useEffect.generateMockHistoricalData\": ()=>{\n                    // Get base APY from Aave-only data\n                    const assetData = AAVE_ONLY_APY_DATA.find({\n                        \"useHistoricalAPY.useEffect.generateMockHistoricalData.assetData\": (d)=>d.asset === asset.toLowerCase() || d.symbol === asset.toUpperCase()\n                    }[\"useHistoricalAPY.useEffect.generateMockHistoricalData.assetData\"]);\n                    if (!assetData) {\n                        setData([]);\n                        setLoading(false);\n                        return;\n                    }\n                    const baseSupplyAPY = protocol === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY;\n                    const baseBorrowAPY = protocol === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY;\n                    // Generate realistic historical data with small variations\n                    const historicalData = Array.from({\n                        length: days\n                    }).map({\n                        \"useHistoricalAPY.useEffect.generateMockHistoricalData.historicalData\": (_, i)=>{\n                            const date = new Date();\n                            date.setDate(date.getDate() - (days - i));\n                            // Add small random variations to make it look realistic\n                            const supplyVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation\n                            const borrowVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation\n                            return {\n                                date: date.toISOString().split('T')[0],\n                                supplyAPY: Math.max(0, baseSupplyAPY + supplyVariation),\n                                borrowAPY: Math.max(0, baseBorrowAPY + borrowVariation)\n                            };\n                        }\n                    }[\"useHistoricalAPY.useEffect.generateMockHistoricalData.historicalData\"]);\n                    setData(historicalData);\n                    setLoading(false);\n                }\n            }[\"useHistoricalAPY.useEffect.generateMockHistoricalData\"];\n            if (asset) {\n                generateMockHistoricalData();\n            }\n        }\n    }[\"useHistoricalAPY.useEffect\"], [\n        asset,\n        protocol,\n        days\n    ]);\n    return {\n        data,\n        loading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\n"));

/***/ })

});