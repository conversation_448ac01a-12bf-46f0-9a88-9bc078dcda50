/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@rainbow-me/rainbowkit/dist/index.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/* vanilla-extract-css-ns:src/css/reset.css.ts.vanilla.css?source=Lmlla2JjYzAgewogIGJvcmRlcjogMDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGZvbnQtc2l6ZTogMTAwJTsKICBsaW5lLWhlaWdodDogbm9ybWFsOwogIG1hcmdpbjogMDsKICBwYWRkaW5nOiAwOwogIHRleHQtYWxpZ246IGxlZnQ7CiAgdmVydGljYWwtYWxpZ246IGJhc2VsaW5lOwogIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQ7Cn0KLmlla2JjYzEgewogIGxpc3Qtc3R5bGU6IG5vbmU7Cn0KLmlla2JjYzIgewogIHF1b3Rlczogbm9uZTsKfQouaWVrYmNjMjpiZWZvcmUsIC5pZWtiY2MyOmFmdGVyIHsKICBjb250ZW50OiAnJzsKfQouaWVrYmNjMyB7CiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsKICBib3JkZXItc3BhY2luZzogMDsKfQouaWVrYmNjNCB7CiAgYXBwZWFyYW5jZTogbm9uZTsKfQouaWVrYmNjNSB7CiAgb3V0bGluZTogbm9uZTsKfQouaWVrYmNjNTo6cGxhY2Vob2xkZXIgewogIG9wYWNpdHk6IDE7Cn0KLmlla2JjYzYgewogIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50OwogIGNvbG9yOiBpbmhlcml0Owp9Ci5pZWtiY2M3OmRpc2FibGVkIHsKICBvcGFjaXR5OiAxOwp9Ci5pZWtiY2M3OjotbXMtZXhwYW5kIHsKICBkaXNwbGF5OiBub25lOwp9Ci5pZWtiY2M4OjotbXMtY2xlYXIgewogIGRpc3BsYXk6IG5vbmU7Cn0KLmlla2JjYzg6Oi13ZWJraXQtc2VhcmNoLWNhbmNlbC1idXR0b24gewogIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTsKfQouaWVrYmNjOSB7CiAgYmFja2dyb3VuZDogbm9uZTsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdGV4dC1hbGlnbjogbGVmdDsKfQouaWVrYmNjYSB7CiAgY29sb3I6IGluaGVyaXQ7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwp9 */
[data-rk] .iekbcc0 {
  border: 0;
  box-sizing: border-box;
  font-size: 100%;
  line-height: normal;
  margin: 0;
  padding: 0;
  text-align: left;
  vertical-align: baseline;
  -webkit-tap-highlight-color: transparent;
}
[data-rk] .iekbcc1 {
  list-style: none;
}
[data-rk] .iekbcc2 {
  quotes: none;
}
[data-rk] .iekbcc2:before,
[data-rk] .iekbcc2:after {
  content: "";
}
[data-rk] .iekbcc3 {
  border-collapse: collapse;
  border-spacing: 0;
}
[data-rk] .iekbcc4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
[data-rk] .iekbcc5 {
  outline: none;
}
[data-rk] .iekbcc5::-moz-placeholder {
  opacity: 1;
}
[data-rk] .iekbcc5::placeholder {
  opacity: 1;
}
[data-rk] .iekbcc6 {
  background-color: transparent;
  color: inherit;
}
[data-rk] .iekbcc7:disabled {
  opacity: 1;
}
[data-rk] .iekbcc7::-ms-expand {
  display: none;
}
[data-rk] .iekbcc8::-ms-clear {
  display: none;
}
[data-rk] .iekbcc8::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
[data-rk] .iekbcc9 {
  background: none;
  cursor: pointer;
  text-align: left;
}
[data-rk] .iekbcca {
  color: inherit;
  text-decoration: none;
}

/* vanilla-extract-css-ns:src/css/sprinkles.css.ts.vanilla.css?source=#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 */
[data-rk] .ju367v0 {
  align-items: flex-start;
}
[data-rk] .ju367v2 {
  align-items: flex-end;
}
[data-rk] .ju367v4 {
  align-items: center;
}
[data-rk] .ju367v6 {
  display: none;
}
[data-rk] .ju367v8 {
  display: block;
}
[data-rk] .ju367va {
  display: flex;
}
[data-rk] .ju367vc {
  display: inline;
}
[data-rk] .ju367ve {
  align-self: flex-start;
}
[data-rk] .ju367vf {
  align-self: flex-end;
}
[data-rk] .ju367vg {
  align-self: center;
}
[data-rk] .ju367vh {
  background-size: cover;
}
[data-rk] .ju367vi {
  border-radius: 1px;
}
[data-rk] .ju367vj {
  border-radius: 6px;
}
[data-rk] .ju367vk {
  border-radius: 10px;
}
[data-rk] .ju367vl {
  border-radius: 13px;
}
[data-rk] .ju367vm {
  border-radius: var(--rk-radii-actionButton);
}
[data-rk] .ju367vn {
  border-radius: var(--rk-radii-connectButton);
}
[data-rk] .ju367vo {
  border-radius: var(--rk-radii-menuButton);
}
[data-rk] .ju367vp {
  border-radius: var(--rk-radii-modal);
}
[data-rk] .ju367vq {
  border-radius: var(--rk-radii-modalMobile);
}
[data-rk] .ju367vr {
  border-radius: 25%;
}
[data-rk] .ju367vs {
  border-radius: 9999px;
}
[data-rk] .ju367vt {
  border-style: solid;
}
[data-rk] .ju367vu {
  border-width: 0px;
}
[data-rk] .ju367vv {
  border-width: 1px;
}
[data-rk] .ju367vw {
  border-width: 2px;
}
[data-rk] .ju367vx {
  border-width: 4px;
}
[data-rk] .ju367vy {
  cursor: pointer;
}
[data-rk] .ju367vz {
  cursor: none;
}
[data-rk] .ju367v10 {
  pointer-events: none;
}
[data-rk] .ju367v11 {
  pointer-events: all;
}
[data-rk] .ju367v12 {
  min-height: 8px;
}
[data-rk] .ju367v13 {
  min-height: 44px;
}
[data-rk] .ju367v14 {
  flex-direction: row;
}
[data-rk] .ju367v15 {
  flex-direction: column;
}
[data-rk] .ju367v16 {
  font-family: var(--rk-fonts-body);
}
[data-rk] .ju367v17 {
  font-size: 12px;
  line-height: 18px;
}
[data-rk] .ju367v18 {
  font-size: 13px;
  line-height: 18px;
}
[data-rk] .ju367v19 {
  font-size: 14px;
  line-height: 18px;
}
[data-rk] .ju367v1a {
  font-size: 16px;
  line-height: 20px;
}
[data-rk] .ju367v1b {
  font-size: 18px;
  line-height: 24px;
}
[data-rk] .ju367v1c {
  font-size: 20px;
  line-height: 24px;
}
[data-rk] .ju367v1d {
  font-size: 23px;
  line-height: 29px;
}
[data-rk] .ju367v1e {
  font-weight: 400;
}
[data-rk] .ju367v1f {
  font-weight: 500;
}
[data-rk] .ju367v1g {
  font-weight: 600;
}
[data-rk] .ju367v1h {
  font-weight: 700;
}
[data-rk] .ju367v1i {
  font-weight: 800;
}
[data-rk] .ju367v1j {
  gap: 0;
}
[data-rk] .ju367v1k {
  gap: 1px;
}
[data-rk] .ju367v1l {
  gap: 2px;
}
[data-rk] .ju367v1m {
  gap: 3px;
}
[data-rk] .ju367v1n {
  gap: 4px;
}
[data-rk] .ju367v1o {
  gap: 5px;
}
[data-rk] .ju367v1p {
  gap: 6px;
}
[data-rk] .ju367v1q {
  gap: 8px;
}
[data-rk] .ju367v1r {
  gap: 10px;
}
[data-rk] .ju367v1s {
  gap: 12px;
}
[data-rk] .ju367v1t {
  gap: 14px;
}
[data-rk] .ju367v1u {
  gap: 16px;
}
[data-rk] .ju367v1v {
  gap: 18px;
}
[data-rk] .ju367v1w {
  gap: 20px;
}
[data-rk] .ju367v1x {
  gap: 24px;
}
[data-rk] .ju367v1y {
  gap: 28px;
}
[data-rk] .ju367v1z {
  gap: 32px;
}
[data-rk] .ju367v20 {
  gap: 36px;
}
[data-rk] .ju367v21 {
  gap: 44px;
}
[data-rk] .ju367v22 {
  gap: 64px;
}
[data-rk] .ju367v23 {
  gap: -1px;
}
[data-rk] .ju367v24 {
  height: 1px;
}
[data-rk] .ju367v25 {
  height: 2px;
}
[data-rk] .ju367v26 {
  height: 4px;
}
[data-rk] .ju367v27 {
  height: 8px;
}
[data-rk] .ju367v28 {
  height: 12px;
}
[data-rk] .ju367v29 {
  height: 20px;
}
[data-rk] .ju367v2a {
  height: 24px;
}
[data-rk] .ju367v2b {
  height: 28px;
}
[data-rk] .ju367v2c {
  height: 30px;
}
[data-rk] .ju367v2d {
  height: 32px;
}
[data-rk] .ju367v2e {
  height: 34px;
}
[data-rk] .ju367v2f {
  height: 36px;
}
[data-rk] .ju367v2g {
  height: 40px;
}
[data-rk] .ju367v2h {
  height: 44px;
}
[data-rk] .ju367v2i {
  height: 48px;
}
[data-rk] .ju367v2j {
  height: 54px;
}
[data-rk] .ju367v2k {
  height: 60px;
}
[data-rk] .ju367v2l {
  height: 200px;
}
[data-rk] .ju367v2m {
  height: 100%;
}
[data-rk] .ju367v2n {
  height: -moz-max-content;
  height: max-content;
}
[data-rk] .ju367v2o {
  justify-content: flex-start;
}
[data-rk] .ju367v2p {
  justify-content: flex-end;
}
[data-rk] .ju367v2q {
  justify-content: center;
}
[data-rk] .ju367v2r {
  justify-content: space-between;
}
[data-rk] .ju367v2s {
  justify-content: space-around;
}
[data-rk] .ju367v2t {
  text-align: left;
}
[data-rk] .ju367v2u {
  text-align: center;
}
[data-rk] .ju367v2v {
  text-align: inherit;
}
[data-rk] .ju367v2w {
  margin-bottom: 0;
}
[data-rk] .ju367v2x {
  margin-bottom: 1px;
}
[data-rk] .ju367v2y {
  margin-bottom: 2px;
}
[data-rk] .ju367v2z {
  margin-bottom: 3px;
}
[data-rk] .ju367v30 {
  margin-bottom: 4px;
}
[data-rk] .ju367v31 {
  margin-bottom: 5px;
}
[data-rk] .ju367v32 {
  margin-bottom: 6px;
}
[data-rk] .ju367v33 {
  margin-bottom: 8px;
}
[data-rk] .ju367v34 {
  margin-bottom: 10px;
}
[data-rk] .ju367v35 {
  margin-bottom: 12px;
}
[data-rk] .ju367v36 {
  margin-bottom: 14px;
}
[data-rk] .ju367v37 {
  margin-bottom: 16px;
}
[data-rk] .ju367v38 {
  margin-bottom: 18px;
}
[data-rk] .ju367v39 {
  margin-bottom: 20px;
}
[data-rk] .ju367v3a {
  margin-bottom: 24px;
}
[data-rk] .ju367v3b {
  margin-bottom: 28px;
}
[data-rk] .ju367v3c {
  margin-bottom: 32px;
}
[data-rk] .ju367v3d {
  margin-bottom: 36px;
}
[data-rk] .ju367v3e {
  margin-bottom: 44px;
}
[data-rk] .ju367v3f {
  margin-bottom: 64px;
}
[data-rk] .ju367v3g {
  margin-bottom: -1px;
}
[data-rk] .ju367v3h {
  margin-left: 0;
}
[data-rk] .ju367v3i {
  margin-left: 1px;
}
[data-rk] .ju367v3j {
  margin-left: 2px;
}
[data-rk] .ju367v3k {
  margin-left: 3px;
}
[data-rk] .ju367v3l {
  margin-left: 4px;
}
[data-rk] .ju367v3m {
  margin-left: 5px;
}
[data-rk] .ju367v3n {
  margin-left: 6px;
}
[data-rk] .ju367v3o {
  margin-left: 8px;
}
[data-rk] .ju367v3p {
  margin-left: 10px;
}
[data-rk] .ju367v3q {
  margin-left: 12px;
}
[data-rk] .ju367v3r {
  margin-left: 14px;
}
[data-rk] .ju367v3s {
  margin-left: 16px;
}
[data-rk] .ju367v3t {
  margin-left: 18px;
}
[data-rk] .ju367v3u {
  margin-left: 20px;
}
[data-rk] .ju367v3v {
  margin-left: 24px;
}
[data-rk] .ju367v3w {
  margin-left: 28px;
}
[data-rk] .ju367v3x {
  margin-left: 32px;
}
[data-rk] .ju367v3y {
  margin-left: 36px;
}
[data-rk] .ju367v3z {
  margin-left: 44px;
}
[data-rk] .ju367v40 {
  margin-left: 64px;
}
[data-rk] .ju367v41 {
  margin-left: -1px;
}
[data-rk] .ju367v42 {
  margin-right: 0;
}
[data-rk] .ju367v43 {
  margin-right: 1px;
}
[data-rk] .ju367v44 {
  margin-right: 2px;
}
[data-rk] .ju367v45 {
  margin-right: 3px;
}
[data-rk] .ju367v46 {
  margin-right: 4px;
}
[data-rk] .ju367v47 {
  margin-right: 5px;
}
[data-rk] .ju367v48 {
  margin-right: 6px;
}
[data-rk] .ju367v49 {
  margin-right: 8px;
}
[data-rk] .ju367v4a {
  margin-right: 10px;
}
[data-rk] .ju367v4b {
  margin-right: 12px;
}
[data-rk] .ju367v4c {
  margin-right: 14px;
}
[data-rk] .ju367v4d {
  margin-right: 16px;
}
[data-rk] .ju367v4e {
  margin-right: 18px;
}
[data-rk] .ju367v4f {
  margin-right: 20px;
}
[data-rk] .ju367v4g {
  margin-right: 24px;
}
[data-rk] .ju367v4h {
  margin-right: 28px;
}
[data-rk] .ju367v4i {
  margin-right: 32px;
}
[data-rk] .ju367v4j {
  margin-right: 36px;
}
[data-rk] .ju367v4k {
  margin-right: 44px;
}
[data-rk] .ju367v4l {
  margin-right: 64px;
}
[data-rk] .ju367v4m {
  margin-right: -1px;
}
[data-rk] .ju367v4n {
  margin-top: 0;
}
[data-rk] .ju367v4o {
  margin-top: 1px;
}
[data-rk] .ju367v4p {
  margin-top: 2px;
}
[data-rk] .ju367v4q {
  margin-top: 3px;
}
[data-rk] .ju367v4r {
  margin-top: 4px;
}
[data-rk] .ju367v4s {
  margin-top: 5px;
}
[data-rk] .ju367v4t {
  margin-top: 6px;
}
[data-rk] .ju367v4u {
  margin-top: 8px;
}
[data-rk] .ju367v4v {
  margin-top: 10px;
}
[data-rk] .ju367v4w {
  margin-top: 12px;
}
[data-rk] .ju367v4x {
  margin-top: 14px;
}
[data-rk] .ju367v4y {
  margin-top: 16px;
}
[data-rk] .ju367v4z {
  margin-top: 18px;
}
[data-rk] .ju367v50 {
  margin-top: 20px;
}
[data-rk] .ju367v51 {
  margin-top: 24px;
}
[data-rk] .ju367v52 {
  margin-top: 28px;
}
[data-rk] .ju367v53 {
  margin-top: 32px;
}
[data-rk] .ju367v54 {
  margin-top: 36px;
}
[data-rk] .ju367v55 {
  margin-top: 44px;
}
[data-rk] .ju367v56 {
  margin-top: 64px;
}
[data-rk] .ju367v57 {
  margin-top: -1px;
}
[data-rk] .ju367v58 {
  max-width: 1px;
}
[data-rk] .ju367v59 {
  max-width: 2px;
}
[data-rk] .ju367v5a {
  max-width: 4px;
}
[data-rk] .ju367v5b {
  max-width: 8px;
}
[data-rk] .ju367v5c {
  max-width: 12px;
}
[data-rk] .ju367v5d {
  max-width: 20px;
}
[data-rk] .ju367v5e {
  max-width: 24px;
}
[data-rk] .ju367v5f {
  max-width: 28px;
}
[data-rk] .ju367v5g {
  max-width: 30px;
}
[data-rk] .ju367v5h {
  max-width: 32px;
}
[data-rk] .ju367v5i {
  max-width: 34px;
}
[data-rk] .ju367v5j {
  max-width: 36px;
}
[data-rk] .ju367v5k {
  max-width: 40px;
}
[data-rk] .ju367v5l {
  max-width: 44px;
}
[data-rk] .ju367v5m {
  max-width: 48px;
}
[data-rk] .ju367v5n {
  max-width: 54px;
}
[data-rk] .ju367v5o {
  max-width: 60px;
}
[data-rk] .ju367v5p {
  max-width: 200px;
}
[data-rk] .ju367v5q {
  max-width: 100%;
}
[data-rk] .ju367v5r {
  max-width: -moz-max-content;
  max-width: max-content;
}
[data-rk] .ju367v5s {
  min-width: 1px;
}
[data-rk] .ju367v5t {
  min-width: 2px;
}
[data-rk] .ju367v5u {
  min-width: 4px;
}
[data-rk] .ju367v5v {
  min-width: 8px;
}
[data-rk] .ju367v5w {
  min-width: 12px;
}
[data-rk] .ju367v5x {
  min-width: 20px;
}
[data-rk] .ju367v5y {
  min-width: 24px;
}
[data-rk] .ju367v5z {
  min-width: 28px;
}
[data-rk] .ju367v60 {
  min-width: 30px;
}
[data-rk] .ju367v61 {
  min-width: 32px;
}
[data-rk] .ju367v62 {
  min-width: 34px;
}
[data-rk] .ju367v63 {
  min-width: 36px;
}
[data-rk] .ju367v64 {
  min-width: 40px;
}
[data-rk] .ju367v65 {
  min-width: 44px;
}
[data-rk] .ju367v66 {
  min-width: 48px;
}
[data-rk] .ju367v67 {
  min-width: 54px;
}
[data-rk] .ju367v68 {
  min-width: 60px;
}
[data-rk] .ju367v69 {
  min-width: 200px;
}
[data-rk] .ju367v6a {
  min-width: 100%;
}
[data-rk] .ju367v6b {
  min-width: -moz-max-content;
  min-width: max-content;
}
[data-rk] .ju367v6c {
  overflow: hidden;
}
[data-rk] .ju367v6d {
  padding-bottom: 0;
}
[data-rk] .ju367v6e {
  padding-bottom: 1px;
}
[data-rk] .ju367v6f {
  padding-bottom: 2px;
}
[data-rk] .ju367v6g {
  padding-bottom: 3px;
}
[data-rk] .ju367v6h {
  padding-bottom: 4px;
}
[data-rk] .ju367v6i {
  padding-bottom: 5px;
}
[data-rk] .ju367v6j {
  padding-bottom: 6px;
}
[data-rk] .ju367v6k {
  padding-bottom: 8px;
}
[data-rk] .ju367v6l {
  padding-bottom: 10px;
}
[data-rk] .ju367v6m {
  padding-bottom: 12px;
}
[data-rk] .ju367v6n {
  padding-bottom: 14px;
}
[data-rk] .ju367v6o {
  padding-bottom: 16px;
}
[data-rk] .ju367v6p {
  padding-bottom: 18px;
}
[data-rk] .ju367v6q {
  padding-bottom: 20px;
}
[data-rk] .ju367v6r {
  padding-bottom: 24px;
}
[data-rk] .ju367v6s {
  padding-bottom: 28px;
}
[data-rk] .ju367v6t {
  padding-bottom: 32px;
}
[data-rk] .ju367v6u {
  padding-bottom: 36px;
}
[data-rk] .ju367v6v {
  padding-bottom: 44px;
}
[data-rk] .ju367v6w {
  padding-bottom: 64px;
}
[data-rk] .ju367v6x {
  padding-bottom: -1px;
}
[data-rk] .ju367v6y {
  padding-left: 0;
}
[data-rk] .ju367v6z {
  padding-left: 1px;
}
[data-rk] .ju367v70 {
  padding-left: 2px;
}
[data-rk] .ju367v71 {
  padding-left: 3px;
}
[data-rk] .ju367v72 {
  padding-left: 4px;
}
[data-rk] .ju367v73 {
  padding-left: 5px;
}
[data-rk] .ju367v74 {
  padding-left: 6px;
}
[data-rk] .ju367v75 {
  padding-left: 8px;
}
[data-rk] .ju367v76 {
  padding-left: 10px;
}
[data-rk] .ju367v77 {
  padding-left: 12px;
}
[data-rk] .ju367v78 {
  padding-left: 14px;
}
[data-rk] .ju367v79 {
  padding-left: 16px;
}
[data-rk] .ju367v7a {
  padding-left: 18px;
}
[data-rk] .ju367v7b {
  padding-left: 20px;
}
[data-rk] .ju367v7c {
  padding-left: 24px;
}
[data-rk] .ju367v7d {
  padding-left: 28px;
}
[data-rk] .ju367v7e {
  padding-left: 32px;
}
[data-rk] .ju367v7f {
  padding-left: 36px;
}
[data-rk] .ju367v7g {
  padding-left: 44px;
}
[data-rk] .ju367v7h {
  padding-left: 64px;
}
[data-rk] .ju367v7i {
  padding-left: -1px;
}
[data-rk] .ju367v7j {
  padding-right: 0;
}
[data-rk] .ju367v7k {
  padding-right: 1px;
}
[data-rk] .ju367v7l {
  padding-right: 2px;
}
[data-rk] .ju367v7m {
  padding-right: 3px;
}
[data-rk] .ju367v7n {
  padding-right: 4px;
}
[data-rk] .ju367v7o {
  padding-right: 5px;
}
[data-rk] .ju367v7p {
  padding-right: 6px;
}
[data-rk] .ju367v7q {
  padding-right: 8px;
}
[data-rk] .ju367v7r {
  padding-right: 10px;
}
[data-rk] .ju367v7s {
  padding-right: 12px;
}
[data-rk] .ju367v7t {
  padding-right: 14px;
}
[data-rk] .ju367v7u {
  padding-right: 16px;
}
[data-rk] .ju367v7v {
  padding-right: 18px;
}
[data-rk] .ju367v7w {
  padding-right: 20px;
}
[data-rk] .ju367v7x {
  padding-right: 24px;
}
[data-rk] .ju367v7y {
  padding-right: 28px;
}
[data-rk] .ju367v7z {
  padding-right: 32px;
}
[data-rk] .ju367v80 {
  padding-right: 36px;
}
[data-rk] .ju367v81 {
  padding-right: 44px;
}
[data-rk] .ju367v82 {
  padding-right: 64px;
}
[data-rk] .ju367v83 {
  padding-right: -1px;
}
[data-rk] .ju367v84 {
  padding-top: 0;
}
[data-rk] .ju367v85 {
  padding-top: 1px;
}
[data-rk] .ju367v86 {
  padding-top: 2px;
}
[data-rk] .ju367v87 {
  padding-top: 3px;
}
[data-rk] .ju367v88 {
  padding-top: 4px;
}
[data-rk] .ju367v89 {
  padding-top: 5px;
}
[data-rk] .ju367v8a {
  padding-top: 6px;
}
[data-rk] .ju367v8b {
  padding-top: 8px;
}
[data-rk] .ju367v8c {
  padding-top: 10px;
}
[data-rk] .ju367v8d {
  padding-top: 12px;
}
[data-rk] .ju367v8e {
  padding-top: 14px;
}
[data-rk] .ju367v8f {
  padding-top: 16px;
}
[data-rk] .ju367v8g {
  padding-top: 18px;
}
[data-rk] .ju367v8h {
  padding-top: 20px;
}
[data-rk] .ju367v8i {
  padding-top: 24px;
}
[data-rk] .ju367v8j {
  padding-top: 28px;
}
[data-rk] .ju367v8k {
  padding-top: 32px;
}
[data-rk] .ju367v8l {
  padding-top: 36px;
}
[data-rk] .ju367v8m {
  padding-top: 44px;
}
[data-rk] .ju367v8n {
  padding-top: 64px;
}
[data-rk] .ju367v8o {
  padding-top: -1px;
}
[data-rk] .ju367v8p {
  position: absolute;
}
[data-rk] .ju367v8q {
  position: fixed;
}
[data-rk] .ju367v8r {
  position: relative;
}
[data-rk] .ju367v8s {
  -webkit-user-select: none;
}
[data-rk] .ju367v8t {
  right: 0;
}
[data-rk] .ju367v8u {
  transition: 0.125s ease;
}
[data-rk] .ju367v8v {
  transition: transform 0.125s ease;
}
[data-rk] .ju367v8w {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
[data-rk] .ju367v8x {
  width: 1px;
}
[data-rk] .ju367v8y {
  width: 2px;
}
[data-rk] .ju367v8z {
  width: 4px;
}
[data-rk] .ju367v90 {
  width: 8px;
}
[data-rk] .ju367v91 {
  width: 12px;
}
[data-rk] .ju367v92 {
  width: 20px;
}
[data-rk] .ju367v93 {
  width: 24px;
}
[data-rk] .ju367v94 {
  width: 28px;
}
[data-rk] .ju367v95 {
  width: 30px;
}
[data-rk] .ju367v96 {
  width: 32px;
}
[data-rk] .ju367v97 {
  width: 34px;
}
[data-rk] .ju367v98 {
  width: 36px;
}
[data-rk] .ju367v99 {
  width: 40px;
}
[data-rk] .ju367v9a {
  width: 44px;
}
[data-rk] .ju367v9b {
  width: 48px;
}
[data-rk] .ju367v9c {
  width: 54px;
}
[data-rk] .ju367v9d {
  width: 60px;
}
[data-rk] .ju367v9e {
  width: 200px;
}
[data-rk] .ju367v9f {
  width: 100%;
}
[data-rk] .ju367v9g {
  width: -moz-max-content;
  width: max-content;
}
[data-rk] .ju367v9h {
  -webkit-backdrop-filter: var(--rk-blurs-modalOverlay);
  backdrop-filter: var(--rk-blurs-modalOverlay);
}
[data-rk] .ju367v9i {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9j:hover {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9k:active {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9l {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9m:hover {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9n:active {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9o {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9p:hover {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9q:active {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9r {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9s:hover {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9t:active {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9u {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9v:hover {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9w:active {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9x {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367v9y:hover {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367v9z:active {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367va0 {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va1:hover {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va2:active {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va3 {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va4:hover {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va5:active {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va6 {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va7:hover {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va8:active {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va9 {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vaa:hover {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vab:active {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vac {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vad:hover {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vae:active {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vaf {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vag:hover {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vah:active {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vai {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vaj:hover {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vak:active {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367val {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vam:hover {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367van:active {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vao {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vap:hover {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vaq:active {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367var {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vas:hover {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vat:active {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vau {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vav:hover {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vaw:active {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vax {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vay:hover {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vaz:active {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vb0 {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb1:hover {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb2:active {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb3 {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb4:hover {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb5:active {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb6 {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb7:hover {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb8:active {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb9 {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vba:hover {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vbb:active {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vbc {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbd:hover {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbe:active {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbf {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbg:hover {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbh:active {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbi {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbj:hover {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbk:active {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbl {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbm:hover {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbn:active {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbo {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbp:hover {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbq:active {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbr {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbs:hover {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbt:active {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbu {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbv:hover {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbw:active {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbx {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vby:hover {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vbz:active {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vc0 {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc1:hover {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc2:active {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc3 {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc4:hover {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc5:active {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc6 {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc7:hover {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc8:active {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc9 {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vca:hover {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vcb:active {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vcc {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vcd:hover {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vce:active {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vcf {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vcg:hover {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vch:active {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vci {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vcj:hover {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vck:active {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vcl {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vcm:hover {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vcn:active {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vco {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcp:hover {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcq:active {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcr {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vcs:hover {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vct:active {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vcu {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcv:hover {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcw:active {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcx {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vcy:hover {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vcz:active {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vd0 {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd1:hover {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd2:active {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd3 {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd4:hover {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd5:active {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd6 {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd7:hover {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd8:active {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd9 {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vda:hover {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vdb:active {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vdc {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vdd:hover {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vde:active {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vdf {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdg:hover {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdh:active {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdi {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdj:hover {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdk:active {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdl {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdm:hover {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdn:active {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdo {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdp:hover {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdq:active {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdr {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vds:hover {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vdt:active {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vdu {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdv:hover {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdw:active {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdx {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vdy:hover {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vdz:active {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367ve0 {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve1:hover {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve2:active {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve3 {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve4:hover {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve5:active {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve6 {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve7:hover {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve8:active {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve9 {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vea:hover {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367veb:active {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vec {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367ved:hover {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vee:active {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vef {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367veg:hover {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367veh:active {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367vei {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vej:hover {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vek:active {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vel {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367vem:hover {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367ven:active {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367veo {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367vep:hover {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367veq:active {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367ver {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367ves:hover {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367vet:active {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367veu {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vev:hover {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vew:active {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vex {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vey:hover {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vez:active {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vf0 {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf1:hover {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf2:active {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf3 {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf4:hover {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf5:active {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf6 {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf7:hover {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf8:active {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf9 {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfa:hover {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfb:active {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfc {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vfd:hover {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vfe:active {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vff {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfg:hover {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfh:active {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfi {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfj:hover {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfk:active {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfl {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfm:hover {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfn:active {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfo {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfp:hover {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfq:active {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfr {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vfs:hover {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vft:active {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vfu {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfv:hover {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfw:active {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfx {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vfy:hover {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vfz:active {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vg0 {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg1:hover {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg2:active {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg3 {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg4:hover {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg5:active {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg6 {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg7:hover {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg8:active {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg9 {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vga:hover {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vgb:active {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vgc {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vgd:hover {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vge:active {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vgf {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgg:hover {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgh:active {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgi {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgj:hover {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgk:active {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgl {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgm:hover {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgn:active {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgo {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgp:hover {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgq:active {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgr {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgs:hover {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgt:active {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgu {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgv:hover {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgw:active {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgx {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vgy:hover {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vgz:active {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vh0 {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh1:hover {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh2:active {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh3 {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh4:hover {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh5:active {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh6 {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh7:hover {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh8:active {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh9 {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vha:hover {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vhb:active {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vhc {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhd:hover {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhe:active {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhf {
  color: var(--rk-colors-standby);
}
[data-rk] .ju367vhg:hover {
  color: var(--rk-colors-standby);
}
[data-rk] .ju367vhh:active {
  color: var(--rk-colors-standby);
}
@media screen and (min-width: 768px) {
  [data-rk] .ju367v1 {
    align-items: flex-start;
  }
  [data-rk] .ju367v3 {
    align-items: flex-end;
  }
  [data-rk] .ju367v5 {
    align-items: center;
  }
  [data-rk] .ju367v7 {
    display: none;
  }
  [data-rk] .ju367v9 {
    display: block;
  }
  [data-rk] .ju367vb {
    display: flex;
  }
  [data-rk] .ju367vd {
    display: inline;
  }
}

/* vanilla-extract-css-ns:src/css/touchableStyles.css.ts.vanilla.css?source=Ll8xMmNibzhpMywuXzEyY2JvOGkzOjphZnRlciB7CiAgLS1fMTJjYm84aTA6IDE7CiAgLS1fMTJjYm84aTE6IDE7Cn0KLl8xMmNibzhpMzpob3ZlciB7CiAgdHJhbnNmb3JtOiBzY2FsZSh2YXIoLS1fMTJjYm84aTApKTsKfQouXzEyY2JvOGkzOmFjdGl2ZSB7CiAgdHJhbnNmb3JtOiBzY2FsZSh2YXIoLS1fMTJjYm84aTEpKTsKfQouXzEyY2JvOGkzOmFjdGl2ZTo6YWZ0ZXIgewogIGNvbnRlbnQ6ICIiOwogIGJvdHRvbTogLTFweDsKICBkaXNwbGF5OiBibG9jazsKICBsZWZ0OiAtMXB4OwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICByaWdodDogLTFweDsKICB0b3A6IC0xcHg7CiAgdHJhbnNmb3JtOiBzY2FsZShjYWxjKCgxIC8gdmFyKC0tXzEyY2JvOGkxKSkgKiB2YXIoLS1fMTJjYm84aTApKSk7Cn0KLl8xMmNibzhpNCwuXzEyY2JvOGk0OjphZnRlciB7CiAgLS1fMTJjYm84aTA6IDEuMDI1Owp9Ci5fMTJjYm84aTUsLl8xMmNibzhpNTo6YWZ0ZXIgewogIC0tXzEyY2JvOGkwOiAxLjE7Cn0KLl8xMmNibzhpNiwuXzEyY2JvOGk2OjphZnRlciB7CiAgLS1fMTJjYm84aTE6IDAuOTU7Cn0KLl8xMmNibzhpNywuXzEyY2JvOGk3OjphZnRlciB7CiAgLS1fMTJjYm84aTE6IDAuOTsKfQ== */
[data-rk] ._12cbo8i3,
[data-rk] ._12cbo8i3::after {
  --_12cbo8i0: 1;
  --_12cbo8i1: 1;
}
[data-rk] ._12cbo8i3:hover {
  transform: scale(var(--_12cbo8i0));
}
[data-rk] ._12cbo8i3:active {
  transform: scale(var(--_12cbo8i1));
}
[data-rk] ._12cbo8i3:active::after {
  content: "";
  bottom: -1px;
  display: block;
  left: -1px;
  position: absolute;
  right: -1px;
  top: -1px;
  transform: scale(calc((1 / var(--_12cbo8i1)) * var(--_12cbo8i0)));
}
[data-rk] ._12cbo8i4,
[data-rk] ._12cbo8i4::after {
  --_12cbo8i0: 1.025;
}
[data-rk] ._12cbo8i5,
[data-rk] ._12cbo8i5::after {
  --_12cbo8i0: 1.1;
}
[data-rk] ._12cbo8i6,
[data-rk] ._12cbo8i6::after {
  --_12cbo8i1: 0.95;
}
[data-rk] ._12cbo8i7,
[data-rk] ._12cbo8i7::after {
  --_12cbo8i1: 0.9;
}

/* vanilla-extract-css-ns:src/components/Icons/Icons.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfMWx1dWxlNDEgewogIDAlIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOwogIH0KICAxMDAlIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7CiAgfQp9Ci5fMWx1dWxlNDIgewogIGFuaW1hdGlvbjogXzFsdXVsZTQxIDNzIGluZmluaXRlIGxpbmVhcjsKfQouXzFsdXVsZTQzIHsKICBiYWNrZ3JvdW5kOiBjb25pYy1ncmFkaWVudChmcm9tIDE4MGRlZyBhdCA1MCUgNTAlLCByZ2JhKDcyLCAxNDYsIDI1NCwgMCkgMGRlZywgY3VycmVudENvbG9yIDI4Mi4wNGRlZywgcmdiYSg3MiwgMTQ2LCAyNTQsIDApIDMxOS44NmRlZywgcmdiYSg3MiwgMTQ2LCAyNTQsIDApIDM2MGRlZyk7CiAgaGVpZ2h0OiAyMXB4OwogIHdpZHRoOiAyMXB4Owp9 */
@keyframes _1luule41 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
[data-rk] ._1luule42 {
  animation: _1luule41 3s infinite linear;
}
[data-rk] ._1luule43 {
  background:
    conic-gradient(
      from 180deg at 50% 50%,
      rgba(72, 146, 254, 0) 0deg,
      currentColor 282.04deg,
      rgba(72, 146, 254, 0) 319.86deg,
      rgba(72, 146, 254, 0) 360deg);
  height: 21px;
  width: 21px;
}

/* vanilla-extract-css-ns:src/components/Dialog/Dialog.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfOXBtNGtpMCB7CiAgMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDEwMCUpOwogIH0KICAxMDAlIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsKICB9Cn0KQGtleWZyYW1lcyBfOXBtNGtpMSB7CiAgMCUgewogICAgb3BhY2l0eTogMDsKICB9CiAgMTAwJSB7CiAgICBvcGFjaXR5OiAxOwogIH0KfQouXzlwbTRraTMgewogIGFuaW1hdGlvbjogXzlwbTRraTEgMTUwbXMgZWFzZTsKICBib3R0b206IC0yMDBweDsKICBsZWZ0OiAtMjAwcHg7CiAgcGFkZGluZzogMjAwcHg7CiAgcmlnaHQ6IC0yMDBweDsKICB0b3A6IC0yMDBweDsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVooMCk7CiAgei1pbmRleDogMjE0NzQ4MzY0NjsKfQouXzlwbTRraTUgewogIGFuaW1hdGlvbjogXzlwbTRraTAgMzUwbXMgY3ViaWMtYmV6aWVyKC4xNSwxLjE1LDAuNiwxLjAwKSwgXzlwbTRraTEgMTUwbXMgZWFzZTsKICBtYXgtd2lkdGg6IDEwMHZ3Owp9 */
@keyframes _9pm4ki0 {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes _9pm4ki1 {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
[data-rk] ._9pm4ki3 {
  animation: _9pm4ki1 150ms ease;
  bottom: -200px;
  left: -200px;
  padding: 200px;
  right: -200px;
  top: -200px;
  transform: translateZ(0);
  z-index: 2147483646;
}
[data-rk] ._9pm4ki5 {
  animation: _9pm4ki0 350ms cubic-bezier(.15, 1.15, 0.6, 1.00), _9pm4ki1 150ms ease;
  max-width: 100vw;
}

/* vanilla-extract-css-ns:src/components/Dialog/DialogContent.css.ts.vanilla.css?source=Ll8xY2tqcG9rMSB7CiAgYm94LXNpemluZzogY29udGVudC1ib3g7CiAgbWF4LXdpZHRoOiAxMDB2dzsKICB3aWR0aDogMzYwcHg7Cn0KLl8xY2tqcG9rMiB7CiAgd2lkdGg6IDEwMHZ3Owp9Ci5fMWNranBvazMgewogIG1pbi13aWR0aDogNzIwcHg7CiAgd2lkdGg6IDcyMHB4Owp9Ci5fMWNranBvazQgewogIG1pbi13aWR0aDogMzY4cHg7CiAgd2lkdGg6IDM2OHB4Owp9Ci5fMWNranBvazYgewogIGJvcmRlci13aWR0aDogMHB4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgd2lkdGg6IDEwMHZ3Owp9CkBtZWRpYSBzY3JlZW4gYW5kIChtaW4td2lkdGg6IDc2OHB4KSB7CiAgLl8xY2tqcG9rMSB7CiAgICB3aWR0aDogMzYwcHg7CiAgfQogIC5fMWNranBvazIgewogICAgd2lkdGg6IDQ4MHB4OwogIH0KICAuXzFja2pwb2s0IHsKICAgIG1pbi13aWR0aDogMzY4cHg7CiAgICB3aWR0aDogMzY4cHg7CiAgfQp9CkBtZWRpYSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDc2N3B4KSB7CiAgLl8xY2tqcG9rNyB7CiAgICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAwOwogICAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDA7CiAgICBtYXJnaW4tdG9wOiAtMjAwcHg7CiAgICBwYWRkaW5nLWJvdHRvbTogMjAwcHg7CiAgICB0b3A6IDIwMHB4OwogIH0KfQ== */
[data-rk] ._1ckjpok1 {
  box-sizing: content-box;
  max-width: 100vw;
  width: 360px;
}
[data-rk] ._1ckjpok2 {
  width: 100vw;
}
[data-rk] ._1ckjpok3 {
  min-width: 720px;
  width: 720px;
}
[data-rk] ._1ckjpok4 {
  min-width: 368px;
  width: 368px;
}
[data-rk] ._1ckjpok6 {
  border-width: 0px;
  box-sizing: border-box;
  width: 100vw;
}
@media screen and (min-width: 768px) {
  [data-rk] ._1ckjpok1 {
    width: 360px;
  }
  [data-rk] ._1ckjpok2 {
    width: 480px;
  }
  [data-rk] ._1ckjpok4 {
    min-width: 368px;
    width: 368px;
  }
}
@media screen and (max-width: 767px) {
  [data-rk] ._1ckjpok7 {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin-top: -200px;
    padding-bottom: 200px;
    top: 200px;
  }
}

/* vanilla-extract-css-ns:src/components/MenuButton/MenuButton.css.ts.vanilla.css?source=LnY5aG9yYjA6aG92ZXIgewogIGJhY2tncm91bmQ6IHVuc2V0Owp9 */
[data-rk] .v9horb0:hover {
  background: unset;
}

/* vanilla-extract-css-ns:src/components/ChainModal/ChainModal.css.ts.vanilla.css?source=Ll8xOGRxdzl4MCB7CiAgbWF4LWhlaWdodDogNDU2cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBvdmVyZmxvdy14OiBoaWRkZW47Cn0KLl8xOGRxdzl4MSB7CiAgbWF4LWhlaWdodDogNDU2cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBvdmVyZmxvdy14OiBoaWRkZW47CiAgc2Nyb2xsYmFyLXdpZHRoOiBub25lOwp9Ci5fMThkcXc5eDE6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICBkaXNwbGF5OiBub25lOwp9 */
[data-rk] ._18dqw9x0 {
  max-height: 456px;
  overflow-y: auto;
  overflow-x: hidden;
}
[data-rk] ._18dqw9x1 {
  max-height: 456px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}
[data-rk] ._18dqw9x1::-webkit-scrollbar {
  display: none;
}

/* vanilla-extract-css-ns:src/components/ModalSelection/ModalSelection.css.ts.vanilla.css?source=Lmc1a2wwbDAgewogIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7Cn0= */
[data-rk] .g5kl0l0 {
  border-color: transparent;
}

/* vanilla-extract-css-ns:src/components/ConnectOptions/DesktopOptions.css.ts.vanilla.css?source=Ll8xdnd0MGNnMCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgY29sb3I6IGJsYWNrOwp9Ci5fMXZ3dDBjZzIgewogIG1heC1oZWlnaHQ6IDQ1NHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KLl8xdnd0MGNnMyB7CiAgbWluLXdpZHRoOiAyODdweDsKfQouXzF2d3QwY2c0IHsKICBtaW4td2lkdGg6IDEwMCU7Cn0= */
[data-rk] ._1vwt0cg0 {
  background: white;
  color: black;
}
[data-rk] ._1vwt0cg2 {
  max-height: 454px;
  overflow-y: auto;
}
[data-rk] ._1vwt0cg3 {
  min-width: 287px;
}
[data-rk] ._1vwt0cg4 {
  min-width: 100%;
}

/* vanilla-extract-css-ns:src/components/ConnectOptions/MobileOptions.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfMWFtMTQ0MTEgewogIDAlIHsKICAgIHN0cm9rZS1kYXNob2Zmc2V0OiAwOwogIH0KICAxMDAlIHsKICAgIHN0cm9rZS1kYXNob2Zmc2V0OiAtMjgzOwogIH0KfQouXzFhbTE0NDEwIHsKICBvdmVyZmxvdzogYXV0bzsKICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVaKDApOwp9Ci5fMWFtMTQ0MTA6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICBkaXNwbGF5OiBub25lOwp9Ci5fMWFtMTQ0MTIgewogIGFuaW1hdGlvbjogXzFhbTE0NDExIDFzIGxpbmVhciBpbmZpbml0ZTsKICBzdHJva2UtZGFzaGFycmF5OiA5OCAxOTY7CiAgZmlsbDogbm9uZTsKICBzdHJva2UtbGluZWNhcDogcm91bmQ7CiAgc3Ryb2tlLXdpZHRoOiA0Owp9Ci5fMWFtMTQ0MTMgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKfQ== */
@keyframes _1am14411 {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -283;
  }
}
[data-rk] ._1am14410 {
  overflow: auto;
  scrollbar-width: none;
  transform: translateZ(0);
}
[data-rk] ._1am14410::-webkit-scrollbar {
  display: none;
}
[data-rk] ._1am14412 {
  animation: _1am14411 1s linear infinite;
  stroke-dasharray: 98 196;
  fill: none;
  stroke-linecap: round;
  stroke-width: 4;
}
[data-rk] ._1am14413 {
  position: absolute;
}

/* vanilla-extract-css-ns:src/components/WalletButton/WalletButton.css.ts.vanilla.css?source=Ll8xeTJsbmZpMCB7CiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxNiwgMjEsIDMxLCAwLjA2KTsKfQouXzF5MmxuZmkxIHsKICBtYXgtd2lkdGg6IGZpdC1jb250ZW50Owp9 */
[data-rk] ._1y2lnfi0 {
  border: 1px solid rgba(16, 21, 31, 0.06);
}
[data-rk] ._1y2lnfi1 {
  max-width: -moz-fit-content;
  max-width: fit-content;
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-orange-50: oklch(98% 0.016 73.684);
    --color-orange-200: oklch(90.1% 0.076 70.697);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-orange-700: oklch(55.3% 0.195 38.402);
    --color-orange-800: oklch(47% 0.157 37.304);
    --color-orange-900: oklch(40.8% 0.123 38.172);
    --color-yellow-50: oklch(98.7% 0.026 102.212);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-yellow-700: oklch(55.4% 0.135 66.442);
    --color-yellow-800: oklch(47.6% 0.114 61.907);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-green-900: oklch(39.3% 0.095 152.535);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-purple-800: oklch(43.8% 0.218 303.724);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wider: 0.05em;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --animate-spin: spin 1s linear infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .z-50 {
    z-index: 50;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .-mb-px {
    margin-bottom: -1px;
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-flex {
    display: inline-flex;
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .max-h-\[90vh\] {
    max-height: 90vh;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .scale-90 {
    --tw-scale-x: 90%;
    --tw-scale-y: 90%;
    --tw-scale-z: 90%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-6 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-500 {
    border-color: var(--color-green-500);
  }
  .border-orange-200 {
    border-color: var(--color-orange-200);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }
  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }
  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-green-50 {
    --tw-gradient-from: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-green-100 {
    --tw-gradient-to: var(--color-green-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }
  .break-all {
    word-break: break-all;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-orange-700 {
    color: var(--color-orange-700);
  }
  .text-orange-800 {
    color: var(--color-orange-800);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .underline {
    text-decoration-line: underline;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:border-gray-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-400);
      }
    }
  }
  .hover\:bg-blue-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-green-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .hover\:bg-green-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-700);
      }
    }
  }
  .hover\:bg-orange-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-600);
      }
    }
  }
  .hover\:bg-purple-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-600);
      }
    }
  }
  .hover\:bg-red-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-200);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-yellow-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-600);
      }
    }
  }
  .hover\:bg-yellow-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-700);
      }
    }
  }
  .hover\:text-blue-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-900);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-green-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-600);
      }
    }
  }
  .hover\:text-green-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-700);
      }
    }
  }
  .hover\:text-green-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-900);
      }
    }
  }
  .hover\:text-orange-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-orange-900);
      }
    }
  }
  .hover\:text-red-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-900);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-green-500 {
    &:focus {
      --tw-ring-color: var(--color-green-500);
    }
  }
  .disabled\:bg-gray-300 {
    &:disabled {
      background-color: var(--color-gray-300);
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:justify-between {
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .sm\:space-y-0 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-x-4 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:px-3 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
}
:root {
  --background: #ffffff;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
    }
  }
}

