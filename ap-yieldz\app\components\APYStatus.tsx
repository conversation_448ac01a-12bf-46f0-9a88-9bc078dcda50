'use client';

import { useState } from 'react';
import { useAPYData } from '../blockchain/hooks/useAPYData';
import { testAaveAPIConnection } from '../services/aaveAPI';
import { RefreshCw, Wifi, WifiOff, AlertCircle } from 'lucide-react';

export default function APYStatus() {
  const { apyData, loading, error, refresh } = useAPYData();
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'failed'>('unknown');
  const [connectionDetails, setConnectionDetails] = useState<string>('');

  const testConnection = async () => {
    setTestingConnection(true);
    try {
      const result = await testAaveAPIConnection();
      setConnectionStatus(result.success ? 'connected' : 'failed');
      setConnectionDetails(result.success
        ? `Connected via ${result.dataSource}`
        : `Failed: ${result.error || 'Unknown error'}`
      );
    } catch (error) {
      setConnectionStatus('failed');
      setConnectionDetails('Test failed with exception');
    } finally {
      setTestingConnection(false);
    }
  };

  const handleRefresh = async () => {
    await refresh();
    // Also test connection when refreshing
    await testConnection();
  };

  const getStatusIcon = () => {
    if (loading || testingConnection) {
      return <RefreshCw className="animate-spin" size={16} />;
    }
    
    if (error) {
      return <WifiOff size={16} className="text-orange-600" />;
    }
    
    return <Wifi size={16} className="text-green-600" />;
  };

  const getStatusText = () => {
    if (loading) return 'Loading rates...';
    if (testingConnection) return 'Testing connection...';
    if (error) return 'Using fallback data';
    return 'Live rates active';
  };

  const getStatusColor = () => {
    if (loading || testingConnection) return 'text-blue-600';
    if (error) return 'text-orange-600';
    return 'text-green-600';
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">APY Data Status</h3>
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        {apyData.map((asset) => (
          <div key={asset.symbol} className="bg-gray-50 rounded-lg p-3">
            <div className="font-medium text-gray-800 mb-2">{asset.symbol}</div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Supply APY:</span>
                <span className="font-medium text-green-600">
                  {asset.aaveSupplyAPY.toFixed(2)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Borrow APY:</span>
                <span className="font-medium text-red-600">
                  {asset.aaveBorrowAPY.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleRefresh}
            disabled={loading || testingConnection}
            className="flex items-center space-x-2 bg-blue-500 text-white px-3 py-2 rounded hover:bg-blue-600 disabled:opacity-50 text-sm"
          >
            <RefreshCw size={14} className={loading ? 'animate-spin' : ''} />
            <span>Refresh Rates</span>
          </button>

          <button
            onClick={testConnection}
            disabled={testingConnection}
            className="flex items-center space-x-2 bg-gray-500 text-white px-3 py-2 rounded hover:bg-gray-600 disabled:opacity-50 text-sm"
          >
            <Wifi size={14} />
            <span>Test API</span>
          </button>
        </div>

        <div className="text-xs text-gray-500">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>

      {error && (
        <div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <AlertCircle size={16} className="text-orange-600" />
            <span className="text-orange-800 text-sm">
              <strong>Notice:</strong> {error}
            </span>
          </div>
          <p className="text-orange-700 text-xs mt-1">
            The app is using fallback rates. Live rates will be restored automatically when the API becomes available.
          </p>
        </div>
      )}

      {connectionStatus === 'connected' && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Wifi size={16} className="text-green-600" />
            <span className="text-green-800 text-sm">
              <strong>Connected:</strong> {connectionDetails}
            </span>
          </div>
        </div>
      )}

      {connectionStatus === 'failed' && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <WifiOff size={16} className="text-red-600" />
            <span className="text-red-800 text-sm">
              <strong>Connection Failed:</strong> {connectionDetails}
            </span>
          </div>
          <p className="text-red-700 text-xs mt-1">
            Using fallback rates. The contract functionality remains fully operational.
          </p>
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500 border-t pt-3">
        <p><strong>Data Source:</strong> Aave V3 Protocol on Avalanche Fuji</p>
        <p><strong>Update Frequency:</strong> Every 60 seconds</p>
        <p><strong>Fallback:</strong> Static rates if API unavailable</p>
      </div>
    </div>
  );
}
