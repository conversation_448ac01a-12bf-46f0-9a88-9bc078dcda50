"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/valtio";
exports.ids = ["vendor-chunks/valtio"];
exports.modules = {

/***/ "(ssr)/./node_modules/valtio/esm/vanilla.mjs":
/*!*********************************************!*\
  !*** ./node_modules/valtio/esm/vanilla.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   proxy: () => (/* binding */ proxy),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   snapshot: () => (/* binding */ snapshot),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   unstable_buildProxyFunction: () => (/* binding */ unstable_buildProxyFunction)\n/* harmony export */ });\n/* harmony import */ var proxy_compare__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! proxy-compare */ \"(ssr)/./node_modules/proxy-compare/dist/index.modern.js\");\n\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nconst proxyStateMap = /* @__PURE__ */ new WeakMap();\nconst refSet = /* @__PURE__ */ new WeakSet();\nconst buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {\n  switch (promise.status) {\n    case \"fulfilled\":\n      return promise.value;\n    case \"rejected\":\n      throw promise.reason;\n    default:\n      throw promise;\n  }\n}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version, handlePromise = defaultHandlePromise) => {\n  const cache = snapCache.get(target);\n  if ((cache == null ? void 0 : cache[0]) === version) {\n    return cache[1];\n  }\n  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));\n  (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(snap, true);\n  snapCache.set(target, [version, snap]);\n  Reflect.ownKeys(target).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(snap, key)) {\n      return;\n    }\n    const value = Reflect.get(target, key);\n    const { enumerable } = Reflect.getOwnPropertyDescriptor(\n      target,\n      key\n    );\n    const desc = {\n      value,\n      enumerable,\n      // This is intentional to avoid copying with proxy-compare.\n      // It's still non-writable, so it avoids assigning a value.\n      configurable: true\n    };\n    if (refSet.has(value)) {\n      (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(value, false);\n    } else if (value instanceof Promise) {\n      delete desc.value;\n      desc.get = () => handlePromise(value);\n    } else if (proxyStateMap.has(value)) {\n      const [target2, ensureVersion] = proxyStateMap.get(\n        value\n      );\n      desc.value = createSnapshot(\n        target2,\n        ensureVersion(),\n        handlePromise\n      );\n    }\n    Object.defineProperty(snap, key, desc);\n  });\n  return Object.preventExtensions(snap);\n}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {\n  if (!isObject(initialObject)) {\n    throw new Error(\"object required\");\n  }\n  const found = proxyCache.get(initialObject);\n  if (found) {\n    return found;\n  }\n  let version = versionHolder[0];\n  const listeners = /* @__PURE__ */ new Set();\n  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {\n    if (version !== nextVersion) {\n      version = nextVersion;\n      listeners.forEach((listener) => listener(op, nextVersion));\n    }\n  };\n  let checkVersion = versionHolder[1];\n  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {\n    if (checkVersion !== nextCheckVersion && !listeners.size) {\n      checkVersion = nextCheckVersion;\n      propProxyStates.forEach(([propProxyState]) => {\n        const propVersion = propProxyState[1](nextCheckVersion);\n        if (propVersion > version) {\n          version = propVersion;\n        }\n      });\n    }\n    return version;\n  };\n  const createPropListener = (prop) => (op, nextVersion) => {\n    const newOp = [...op];\n    newOp[1] = [prop, ...newOp[1]];\n    notifyUpdate(newOp, nextVersion);\n  };\n  const propProxyStates = /* @__PURE__ */ new Map();\n  const addPropListener = (prop, propProxyState) => {\n    if (( false ? 0 : void 0) !== \"production\" && propProxyStates.has(prop)) {\n      throw new Error(\"prop listener already exists\");\n    }\n    if (listeners.size) {\n      const remove = propProxyState[3](createPropListener(prop));\n      propProxyStates.set(prop, [propProxyState, remove]);\n    } else {\n      propProxyStates.set(prop, [propProxyState]);\n    }\n  };\n  const removePropListener = (prop) => {\n    var _a;\n    const entry = propProxyStates.get(prop);\n    if (entry) {\n      propProxyStates.delete(prop);\n      (_a = entry[1]) == null ? void 0 : _a.call(entry);\n    }\n  };\n  const addListener = (listener) => {\n    listeners.add(listener);\n    if (listeners.size === 1) {\n      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {\n        if (( false ? 0 : void 0) !== \"production\" && prevRemove) {\n          throw new Error(\"remove already exists\");\n        }\n        const remove = propProxyState[3](createPropListener(prop));\n        propProxyStates.set(prop, [propProxyState, remove]);\n      });\n    }\n    const removeListener = () => {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        propProxyStates.forEach(([propProxyState, remove], prop) => {\n          if (remove) {\n            remove();\n            propProxyStates.set(prop, [propProxyState]);\n          }\n        });\n      }\n    };\n    return removeListener;\n  };\n  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));\n  const handler = {\n    deleteProperty(target, prop) {\n      const prevValue = Reflect.get(target, prop);\n      removePropListener(prop);\n      const deleted = Reflect.deleteProperty(target, prop);\n      if (deleted) {\n        notifyUpdate([\"delete\", [prop], prevValue]);\n      }\n      return deleted;\n    },\n    set(target, prop, value, receiver) {\n      const hasPrevValue = Reflect.has(target, prop);\n      const prevValue = Reflect.get(target, prop, receiver);\n      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {\n        return true;\n      }\n      removePropListener(prop);\n      if (isObject(value)) {\n        value = (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.getUntracked)(value) || value;\n      }\n      let nextValue = value;\n      if (value instanceof Promise) {\n        value.then((v) => {\n          value.status = \"fulfilled\";\n          value.value = v;\n          notifyUpdate([\"resolve\", [prop], v]);\n        }).catch((e) => {\n          value.status = \"rejected\";\n          value.reason = e;\n          notifyUpdate([\"reject\", [prop], e]);\n        });\n      } else {\n        if (!proxyStateMap.has(value) && canProxy(value)) {\n          nextValue = proxyFunction(value);\n        }\n        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);\n        if (childProxyState) {\n          addPropListener(prop, childProxyState);\n        }\n      }\n      Reflect.set(target, prop, nextValue, receiver);\n      notifyUpdate([\"set\", [prop], value, prevValue]);\n      return true;\n    }\n  };\n  const proxyObject = newProxy(baseObject, handler);\n  proxyCache.set(initialObject, proxyObject);\n  const proxyState = [\n    baseObject,\n    ensureVersion,\n    createSnapshot,\n    addListener\n  ];\n  proxyStateMap.set(proxyObject, proxyState);\n  Reflect.ownKeys(initialObject).forEach((key) => {\n    const desc = Object.getOwnPropertyDescriptor(\n      initialObject,\n      key\n    );\n    if (\"value\" in desc) {\n      proxyObject[key] = initialObject[key];\n      delete desc.value;\n      delete desc.writable;\n    }\n    Object.defineProperty(baseObject, key, desc);\n  });\n  return proxyObject;\n}) => [\n  // public functions\n  proxyFunction,\n  // shared state\n  proxyStateMap,\n  refSet,\n  // internal things\n  objectIs,\n  newProxy,\n  canProxy,\n  defaultHandlePromise,\n  snapCache,\n  createSnapshot,\n  proxyCache,\n  versionHolder\n];\nconst [defaultProxyFunction] = buildProxyFunction();\nfunction proxy(initialObject = {}) {\n  return defaultProxyFunction(initialObject);\n}\nfunction getVersion(proxyObject) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  return proxyState == null ? void 0 : proxyState[1]();\n}\nfunction subscribe(proxyObject, callback, notifyInSync) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  let promise;\n  const ops = [];\n  const addListener = proxyState[3];\n  let isListenerActive = false;\n  const listener = (op) => {\n    ops.push(op);\n    if (notifyInSync) {\n      callback(ops.splice(0));\n      return;\n    }\n    if (!promise) {\n      promise = Promise.resolve().then(() => {\n        promise = void 0;\n        if (isListenerActive) {\n          callback(ops.splice(0));\n        }\n      });\n    }\n  };\n  const removeListener = addListener(listener);\n  isListenerActive = true;\n  return () => {\n    isListenerActive = false;\n    removeListener();\n  };\n}\nfunction snapshot(proxyObject, handlePromise) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  const [target, ensureVersion, createSnapshot] = proxyState;\n  return createSnapshot(target, ensureVersion(), handlePromise);\n}\nfunction ref(obj) {\n  refSet.add(obj);\n  return obj;\n}\nconst unstable_buildProxyFunction = buildProxyFunction;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/valtio/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/valtio/esm/vanilla/utils.mjs":
/*!***************************************************!*\
  !*** ./node_modules/valtio/esm/vanilla/utils.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addComputed: () => (/* binding */ addComputed_DEPRECATED),\n/* harmony export */   derive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   proxyMap: () => (/* binding */ proxyMap),\n/* harmony export */   proxySet: () => (/* binding */ proxySet),\n/* harmony export */   proxyWithComputed: () => (/* binding */ proxyWithComputed_DEPRECATED),\n/* harmony export */   proxyWithHistory: () => (/* binding */ proxyWithHistory_DEPRECATED),\n/* harmony export */   subscribeKey: () => (/* binding */ subscribeKey),\n/* harmony export */   underive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.underive),\n/* harmony export */   unstable_deriveSubscriptions: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.unstable_deriveSubscriptions),\n/* harmony export */   watch: () => (/* binding */ watch)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! valtio/vanilla */ \"(ssr)/./node_modules/valtio/esm/vanilla.mjs\");\n/* harmony import */ var derive_valtio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! derive-valtio */ \"(ssr)/./node_modules/derive-valtio/dist/index.modern.js\");\n\n\n\n\nfunction subscribeKey(proxyObject, key, callback, notifyInSync) {\n  let prevValue = proxyObject[key];\n  return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(\n    proxyObject,\n    () => {\n      const nextValue = proxyObject[key];\n      if (!Object.is(prevValue, nextValue)) {\n        callback(prevValue = nextValue);\n      }\n    },\n    notifyInSync\n  );\n}\n\nlet currentCleanups;\nfunction watch(callback, options) {\n  let alive = true;\n  const cleanups = /* @__PURE__ */ new Set();\n  const subscriptions = /* @__PURE__ */ new Map();\n  const cleanup = () => {\n    if (alive) {\n      alive = false;\n      cleanups.forEach((clean) => clean());\n      cleanups.clear();\n      subscriptions.forEach((unsubscribe) => unsubscribe());\n      subscriptions.clear();\n    }\n  };\n  const revalidate = async () => {\n    if (!alive) {\n      return;\n    }\n    cleanups.forEach((clean) => clean());\n    cleanups.clear();\n    const proxiesToSubscribe = /* @__PURE__ */ new Set();\n    const parent = currentCleanups;\n    currentCleanups = cleanups;\n    try {\n      const promiseOrPossibleCleanup = callback((proxyObject) => {\n        proxiesToSubscribe.add(proxyObject);\n        if (alive && !subscriptions.has(proxyObject)) {\n          const unsubscribe = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, revalidate, options == null ? void 0 : options.sync);\n          subscriptions.set(proxyObject, unsubscribe);\n        }\n        return proxyObject;\n      });\n      const couldBeCleanup = promiseOrPossibleCleanup && promiseOrPossibleCleanup instanceof Promise ? await promiseOrPossibleCleanup : promiseOrPossibleCleanup;\n      if (couldBeCleanup) {\n        if (alive) {\n          cleanups.add(couldBeCleanup);\n        } else {\n          cleanup();\n        }\n      }\n    } finally {\n      currentCleanups = parent;\n    }\n    subscriptions.forEach((unsubscribe, proxyObject) => {\n      if (!proxiesToSubscribe.has(proxyObject)) {\n        subscriptions.delete(proxyObject);\n        unsubscribe();\n      }\n    });\n  };\n  if (currentCleanups) {\n    currentCleanups.add(cleanup);\n  }\n  revalidate();\n  return cleanup;\n}\n\nconst DEVTOOLS = Symbol();\nfunction devtools(proxyObject, options) {\n  if (typeof options === \"string\") {\n    console.warn(\n      \"string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400\"\n    );\n    options = { name: options };\n  }\n  const { enabled, name = \"\", ...rest } = options || {};\n  let extension;\n  try {\n    extension = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extension) {\n    if (( false ? 0 : void 0) !== \"production\" && enabled) {\n      console.warn(\"[Warning] Please install/enable Redux devtools extension\");\n    }\n    return;\n  }\n  let isTimeTraveling = false;\n  const devtools2 = extension.connect({ name, ...rest });\n  const unsub1 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n    const action = ops.filter(([_, path]) => path[0] !== DEVTOOLS).map(([op, path]) => `${op}:${path.map(String).join(\".\")}`).join(\", \");\n    if (!action) {\n      return;\n    }\n    if (isTimeTraveling) {\n      isTimeTraveling = false;\n    } else {\n      const snapWithoutDevtools = Object.assign({}, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n      delete snapWithoutDevtools[DEVTOOLS];\n      devtools2.send(\n        {\n          type: action,\n          updatedAt: (/* @__PURE__ */ new Date()).toLocaleString()\n        },\n        snapWithoutDevtools\n      );\n    }\n  });\n  const unsub2 = devtools2.subscribe((message) => {\n    var _a, _b, _c, _d, _e, _f;\n    if (message.type === \"ACTION\" && message.payload) {\n      try {\n        Object.assign(proxyObject, JSON.parse(message.payload));\n      } catch (e) {\n        console.error(\n          \"please dispatch a serializable value that JSON.parse() and proxy() support\\n\",\n          e\n        );\n      }\n    }\n    if (message.type === \"DISPATCH\" && message.state) {\n      if (((_a = message.payload) == null ? void 0 : _a.type) === \"JUMP_TO_ACTION\" || ((_b = message.payload) == null ? void 0 : _b.type) === \"JUMP_TO_STATE\") {\n        isTimeTraveling = true;\n        const state = JSON.parse(message.state);\n        Object.assign(proxyObject, state);\n      }\n      proxyObject[DEVTOOLS] = message;\n    } else if (message.type === \"DISPATCH\" && ((_c = message.payload) == null ? void 0 : _c.type) === \"COMMIT\") {\n      devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    } else if (message.type === \"DISPATCH\" && ((_d = message.payload) == null ? void 0 : _d.type) === \"IMPORT_STATE\") {\n      const actions = (_e = message.payload.nextLiftedState) == null ? void 0 : _e.actionsById;\n      const computedStates = ((_f = message.payload.nextLiftedState) == null ? void 0 : _f.computedStates) || [];\n      isTimeTraveling = true;\n      computedStates.forEach(({ state }, index) => {\n        const action = actions[index] || \"No action found\";\n        Object.assign(proxyObject, state);\n        if (index === 0) {\n          devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        } else {\n          devtools2.send(action, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        }\n      });\n    }\n  });\n  devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n  return () => {\n    unsub1();\n    unsub2 == null ? void 0 : unsub2();\n  };\n}\n\nfunction addComputed_DEPRECATED(proxyObject, computedFns_FAKE, targetObject = proxyObject) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"addComputed is deprecated. Please consider using `derive`. Falling back to emulation with derive. https://github.com/pmndrs/valtio/pull/201\"\n    );\n  }\n  const derivedFns = {};\n  Object.keys(computedFns_FAKE).forEach((key) => {\n    derivedFns[key] = (get) => computedFns_FAKE[key](get(proxyObject));\n  });\n  return (0,derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive)(derivedFns, { proxy: targetObject });\n}\n\nfunction proxyWithComputed_DEPRECATED(initialObject, computedFns) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithComputed is deprecated. Please follow \"Computed Properties\" guide in docs.'\n    );\n  }\n  Object.keys(computedFns).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(initialObject, key)) {\n      throw new Error(\"object property already defined\");\n    }\n    const computedFn = computedFns[key];\n    const { get, set } = typeof computedFn === \"function\" ? { get: computedFn } : computedFn;\n    const desc = {};\n    desc.get = () => get((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    if (set) {\n      desc.set = (newValue) => set(proxyObject, newValue);\n    }\n    Object.defineProperty(initialObject, key, desc);\n  });\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(initialObject);\n  return proxyObject;\n}\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nlet refSet;\nconst deepClone = (obj) => {\n  if (!refSet) {\n    refSet = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.unstable_buildProxyFunction)()[2];\n  }\n  if (!isObject(obj) || refSet.has(obj)) {\n    return obj;\n  }\n  const baseObject = Array.isArray(obj) ? [] : Object.create(Object.getPrototypeOf(obj));\n  Reflect.ownKeys(obj).forEach((key) => {\n    baseObject[key] = deepClone(obj[key]);\n  });\n  return baseObject;\n};\nfunction proxyWithHistory_DEPRECATED(initialValue, skipSubscribe = false) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithHistory is deprecated. Please use the \"valtio-history\" package; refer to the docs'\n    );\n  }\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    value: initialValue,\n    history: (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.ref)({\n      wip: void 0,\n      // to avoid infinite loop\n      snapshots: [],\n      index: -1\n    }),\n    clone: deepClone,\n    canUndo: () => proxyObject.history.index > 0,\n    undo: () => {\n      if (proxyObject.canUndo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[--proxyObject.history.index]\n        );\n      }\n    },\n    canRedo: () => proxyObject.history.index < proxyObject.history.snapshots.length - 1,\n    redo: () => {\n      if (proxyObject.canRedo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[++proxyObject.history.index]\n        );\n      }\n    },\n    saveHistory: () => {\n      proxyObject.history.snapshots.splice(proxyObject.history.index + 1);\n      proxyObject.history.snapshots.push((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject).value);\n      ++proxyObject.history.index;\n    },\n    subscribe: () => (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n      if (ops.every(\n        (op) => op[1][0] === \"value\" && (op[0] !== \"set\" || op[2] !== proxyObject.history.wip)\n      )) {\n        proxyObject.saveHistory();\n      }\n    })\n  });\n  proxyObject.saveHistory();\n  if (!skipSubscribe) {\n    proxyObject.subscribe();\n  }\n  return proxyObject;\n}\n\nfunction proxySet(initialValues) {\n  const set = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(new Set(initialValues)),\n    has(value) {\n      return this.data.indexOf(value) !== -1;\n    },\n    add(value) {\n      let hasProxy = false;\n      if (typeof value === \"object\" && value !== null) {\n        hasProxy = this.data.indexOf((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(value)) !== -1;\n      }\n      if (this.data.indexOf(value) === -1 && !hasProxy) {\n        this.data.push(value);\n      }\n      return this;\n    },\n    delete(value) {\n      const index = this.data.indexOf(value);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    forEach(cb) {\n      this.data.forEach((value) => {\n        cb(value, value, this);\n      });\n    },\n    get [Symbol.toStringTag]() {\n      return \"Set\";\n    },\n    toJSON() {\n      return new Set(this.data);\n    },\n    [Symbol.iterator]() {\n      return this.data[Symbol.iterator]();\n    },\n    values() {\n      return this.data.values();\n    },\n    keys() {\n      return this.data.values();\n    },\n    entries() {\n      return new Set(this.data).entries();\n    }\n  });\n  Object.defineProperties(set, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(set);\n  return set;\n}\n\nfunction proxyMap(entries) {\n  const map = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(entries || []),\n    has(key) {\n      return this.data.some((p) => p[0] === key);\n    },\n    set(key, value) {\n      const record = this.data.find((p) => p[0] === key);\n      if (record) {\n        record[1] = value;\n      } else {\n        this.data.push([key, value]);\n      }\n      return this;\n    },\n    get(key) {\n      var _a;\n      return (_a = this.data.find((p) => p[0] === key)) == null ? void 0 : _a[1];\n    },\n    delete(key) {\n      const index = this.data.findIndex((p) => p[0] === key);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    toJSON() {\n      return new Map(this.data);\n    },\n    forEach(cb) {\n      this.data.forEach((p) => {\n        cb(p[1], p[0], this);\n      });\n    },\n    keys() {\n      return this.data.map((p) => p[0]).values();\n    },\n    values() {\n      return this.data.map((p) => p[1]).values();\n    },\n    entries() {\n      return new Map(this.data).entries();\n    },\n    get [Symbol.toStringTag]() {\n      return \"Map\";\n    },\n    [Symbol.iterator]() {\n      return this.entries();\n    }\n  });\n  Object.defineProperties(map, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(map);\n  return map;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/valtio/esm/vanilla/utils.mjs\n");

/***/ })

};
;