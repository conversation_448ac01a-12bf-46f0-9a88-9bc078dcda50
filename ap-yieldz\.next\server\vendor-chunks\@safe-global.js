"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@safe-global";
exports.ids = ["vendor-chunks/@safe-global"];
exports.modules = {

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nvar provider_1 = __webpack_require__(/*! ./provider */ \"(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/provider.js\");\nObject.defineProperty(exports, \"SafeAppProvider\", ({ enumerable: true, get: function () { return provider_1.SafeAppProvider; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1wcm92aWRlci9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHVCQUF1QjtBQUN2QixpQkFBaUIsbUJBQU8sQ0FBQyx5RkFBWTtBQUNyQyxtREFBa0QsRUFBRSxxQ0FBcUMsc0NBQXNDLEVBQUM7QUFDaEkiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtcHJvdmlkZXJcXGRpc3RcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5TYWZlQXBwUHJvdmlkZXIgPSB2b2lkIDA7XG52YXIgcHJvdmlkZXJfMSA9IHJlcXVpcmUoXCIuL3Byb3ZpZGVyXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiU2FmZUFwcFByb3ZpZGVyXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBwcm92aWRlcl8xLlNhZmVBcHBQcm92aWRlcjsgfSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/provider.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/provider.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nconst safe_apps_sdk_1 = __webpack_require__(/*! @safe-global/safe-apps-sdk */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js\");\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/utils.js\");\n// The API is based on Ethereum JavaScript API Provider Standard. Link: https://eips.ethereum.org/EIPS/eip-1193\nclass SafeAppProvider extends events_1.EventEmitter {\n    constructor(safe, sdk) {\n        super();\n        this.submittedTxs = new Map();\n        this.safe = safe;\n        this.sdk = sdk;\n    }\n    async connect() {\n        this.emit('connect', { chainId: this.chainId });\n        return;\n    }\n    async disconnect() {\n        return;\n    }\n    get chainId() {\n        return this.safe.chainId;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async request(request) {\n        const { method, params = [] } = request;\n        switch (method) {\n            case 'eth_accounts':\n                return [this.safe.safeAddress];\n            case 'net_version':\n            case 'eth_chainId':\n                return (0, utils_1.numberToHex)(this.chainId);\n            case 'personal_sign': {\n                const [message, address] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(message);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sign': {\n                const [address, messageHash] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase() || !messageHash.startsWith('0x')) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(messageHash);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_signTypedData':\n            case 'eth_signTypedData_v4': {\n                const [address, typedData] = params;\n                const parsedTypedData = typeof typedData === 'string' ? JSON.parse(typedData) : typedData;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address is invalid');\n                }\n                const response = await this.sdk.txs.signTypedMessage(parsedTypedData);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sendTransaction':\n                // `value` or `data` can be explicitly set as `undefined` for example in Viem. The spread will overwrite the fallback value.\n                const tx = {\n                    ...params[0],\n                    value: params[0].value || '0',\n                    data: params[0].data || '0x',\n                };\n                // Some ethereum libraries might pass the gas as a hex-encoded string\n                // We need to convert it to a number because the SDK expects a number and our backend only supports\n                // Decimal numbers\n                if (typeof tx.gas === 'string' && tx.gas.startsWith('0x')) {\n                    tx.gas = parseInt(tx.gas, 16);\n                }\n                const resp = await this.sdk.txs.send({\n                    txs: [tx],\n                    params: { safeTxGas: tx.gas },\n                });\n                // Store fake transaction\n                this.submittedTxs.set(resp.safeTxHash, {\n                    from: this.safe.safeAddress,\n                    hash: resp.safeTxHash,\n                    gas: 0,\n                    gasPrice: '0x00',\n                    nonce: 0,\n                    input: tx.data,\n                    value: tx.value,\n                    to: tx.to,\n                    blockHash: null,\n                    blockNumber: null,\n                    transactionIndex: null,\n                });\n                return resp.safeTxHash;\n            case 'eth_blockNumber':\n                const block = await this.sdk.eth.getBlockByNumber(['latest']);\n                return block.number;\n            case 'eth_getBalance':\n                return this.sdk.eth.getBalance([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getCode':\n                return this.sdk.eth.getCode([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getTransactionCount':\n                return this.sdk.eth.getTransactionCount([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getStorageAt':\n                return this.sdk.eth.getStorageAt([(0, utils_1.getLowerCase)(params[0]), params[1], params[2]]);\n            case 'eth_getBlockByNumber':\n                return this.sdk.eth.getBlockByNumber([params[0], params[1]]);\n            case 'eth_getBlockByHash':\n                return this.sdk.eth.getBlockByHash([params[0], params[1]]);\n            case 'eth_getTransactionByHash':\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                // Use fake transaction if we don't have a real tx hash\n                if (this.submittedTxs.has(txHash)) {\n                    return this.submittedTxs.get(txHash);\n                }\n                return this.sdk.eth.getTransactionByHash([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.hash = params[0];\n                    }\n                    return tx;\n                });\n            case 'eth_getTransactionReceipt': {\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                return this.sdk.eth.getTransactionReceipt([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.transactionHash = params[0];\n                    }\n                    return tx;\n                });\n            }\n            case 'eth_estimateGas': {\n                return this.sdk.eth.getEstimateGas(params[0]);\n            }\n            case 'eth_call': {\n                return this.sdk.eth.call([params[0], params[1]]);\n            }\n            case 'eth_getLogs':\n                return this.sdk.eth.getPastLogs([params[0]]);\n            case 'eth_gasPrice':\n                return this.sdk.eth.getGasPrice();\n            case 'wallet_getPermissions':\n                return this.sdk.wallet.getPermissions();\n            case 'wallet_requestPermissions':\n                return this.sdk.wallet.requestPermissions(params[0]);\n            case 'safe_setSettings':\n                return this.sdk.eth.setSafeSettings([params[0]]);\n            case 'wallet_sendCalls': {\n                const { from, calls, chainId } = params[0];\n                if (chainId !== (0, utils_1.numberToHex)(this.chainId)) {\n                    throw new Error(`Safe is not on chain ${chainId}`);\n                }\n                if (from !== this.safe.safeAddress) {\n                    throw Error('Invalid from address');\n                }\n                const txs = calls.map((call, i) => {\n                    if (!call.to) {\n                        throw new Error(`Invalid call #${i}: missing \"to\" field`);\n                    }\n                    return {\n                        to: call.to,\n                        data: call.data ?? '0x',\n                        value: call.value ?? (0, utils_1.numberToHex)(0),\n                    };\n                });\n                const { safeTxHash } = await this.sdk.txs.send({ txs });\n                const result = {\n                    id: safeTxHash,\n                };\n                return result;\n            }\n            case 'wallet_getCallsStatus': {\n                const safeTxHash = params[0];\n                const CallStatus = {\n                    [safe_apps_sdk_1.TransactionStatus.AWAITING_CONFIRMATIONS]: 100,\n                    [safe_apps_sdk_1.TransactionStatus.AWAITING_EXECUTION]: 100,\n                    [safe_apps_sdk_1.TransactionStatus.SUCCESS]: 200,\n                    [safe_apps_sdk_1.TransactionStatus.CANCELLED]: 400,\n                    [safe_apps_sdk_1.TransactionStatus.FAILED]: 500,\n                };\n                const tx = await this.sdk.txs.getBySafeTxHash(safeTxHash);\n                const result = {\n                    version: '1.0',\n                    id: safeTxHash,\n                    chainId: (0, utils_1.numberToHex)(this.chainId),\n                    status: CallStatus[tx.txStatus],\n                };\n                // Transaction is queued\n                if (!tx.txHash) {\n                    return result;\n                }\n                // If transaction is executing, receipt is null\n                const receipt = await this.sdk.eth.getTransactionReceipt([tx.txHash]);\n                if (!receipt) {\n                    return result;\n                }\n                const calls = tx.txData?.dataDecoded?.method !== 'multiSend'\n                    ? 1\n                    : // Number of batched transactions\n                        tx.txData.dataDecoded.parameters?.[0].valueDecoded?.length ?? 1;\n                // Typed as number; is hex\n                const blockNumber = Number(receipt.blockNumber);\n                const gasUsed = Number(receipt.gasUsed);\n                result.receipts = Array(calls).fill({\n                    logs: receipt.logs,\n                    status: (0, utils_1.numberToHex)(tx.txStatus === safe_apps_sdk_1.TransactionStatus.SUCCESS ? 1 : 0),\n                    blockHash: receipt.blockHash,\n                    blockNumber: (0, utils_1.numberToHex)(blockNumber),\n                    gasUsed: (0, utils_1.numberToHex)(gasUsed),\n                    transactionHash: tx.txHash,\n                });\n                return result;\n            }\n            case 'wallet_showCallsStatus': {\n                // Cannot open transaction details page via SDK\n                throw new Error(`\"${request.method}\" not supported`);\n            }\n            case 'wallet_getCapabilities': {\n                return {\n                    [(0, utils_1.numberToHex)(this.chainId)]: {\n                        atomicBatch: {\n                            supported: true,\n                        },\n                    },\n                };\n            }\n            default:\n                throw Error(`\"${request.method}\" not implemented`);\n        }\n    }\n    // this method is needed for ethers v4\n    // https://github.com/ethers-io/ethers.js/blob/427e16826eb15d52d25c4f01027f8db22b74b76c/src.ts/providers/web3-provider.ts#L41-L55\n    send(request, callback) {\n        if (!request)\n            callback('Undefined request');\n        this.request(request)\n            .then((result) => callback(null, { jsonrpc: '2.0', id: request.id, result }))\n            .catch((error) => callback(error, null));\n    }\n}\nexports.SafeAppProvider = SafeAppProvider;\n//# sourceMappingURL=provider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.numberToHex = exports.getLowerCase = void 0;\nfunction getLowerCase(value) {\n    if (value) {\n        return value.toLowerCase();\n    }\n    return value;\n}\nexports.getLowerCase = getLowerCase;\nfunction numberToHex(value) {\n    return `0x${value.toString(16)}`;\n}\nexports.numberToHex = numberToHex;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1wcm92aWRlci9kaXN0L3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQixHQUFHLG9CQUFvQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQSxnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0EsbUJBQW1CO0FBQ25CIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXByb3ZpZGVyXFxkaXN0XFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMubnVtYmVyVG9IZXggPSBleHBvcnRzLmdldExvd2VyQ2FzZSA9IHZvaWQgMDtcbmZ1bmN0aW9uIGdldExvd2VyQ2FzZSh2YWx1ZSkge1xuICAgIGlmICh2YWx1ZSkge1xuICAgICAgICByZXR1cm4gdmFsdWUudG9Mb3dlckNhc2UoKTtcbiAgICB9XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuZXhwb3J0cy5nZXRMb3dlckNhc2UgPSBnZXRMb3dlckNhc2U7XG5mdW5jdGlvbiBudW1iZXJUb0hleCh2YWx1ZSkge1xuICAgIHJldHVybiBgMHgke3ZhbHVlLnRvU3RyaW5nKDE2KX1gO1xufVxuZXhwb3J0cy5udW1iZXJUb0hleCA9IG51bWJlclRvSGV4O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst messageFormatter_js_1 = __webpack_require__(/*! ./messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\");\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = messageFormatter_js_1.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\nexports[\"default\"] = PostMessageCommunicator;\n__exportStar(__webpack_require__(/*! ./methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MessageFormatter = void 0;\nconst version_js_1 = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js\");\nclass MessageFormatter {\n}\nexports.MessageFormatter = MessageFormatter;\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0, utils_js_1.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0, version_js_1.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHdCQUF3QjtBQUN4QixxQkFBcUIsbUJBQU8sQ0FBQywwRkFBZTtBQUM1QyxtQkFBbUIsbUJBQU8sQ0FBQyxtR0FBWTtBQUN2QztBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcY29tbXVuaWNhdGlvblxcbWVzc2FnZUZvcm1hdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuTWVzc2FnZUZvcm1hdHRlciA9IHZvaWQgMDtcbmNvbnN0IHZlcnNpb25fanNfMSA9IHJlcXVpcmUoXCIuLi92ZXJzaW9uLmpzXCIpO1xuY29uc3QgdXRpbHNfanNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzLmpzXCIpO1xuY2xhc3MgTWVzc2FnZUZvcm1hdHRlciB7XG59XG5leHBvcnRzLk1lc3NhZ2VGb3JtYXR0ZXIgPSBNZXNzYWdlRm9ybWF0dGVyO1xuTWVzc2FnZUZvcm1hdHRlci5tYWtlUmVxdWVzdCA9IChtZXRob2QsIHBhcmFtcykgPT4ge1xuICAgIGNvbnN0IGlkID0gKDAsIHV0aWxzX2pzXzEuZ2VuZXJhdGVSZXF1ZXN0SWQpKCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgaWQsXG4gICAgICAgIG1ldGhvZCxcbiAgICAgICAgcGFyYW1zLFxuICAgICAgICBlbnY6IHtcbiAgICAgICAgICAgIHNka1ZlcnNpb246ICgwLCB2ZXJzaW9uX2pzXzEuZ2V0U0RLVmVyc2lvbikoKSxcbiAgICAgICAgfSxcbiAgICB9O1xufTtcbk1lc3NhZ2VGb3JtYXR0ZXIubWFrZVJlc3BvbnNlID0gKGlkLCBkYXRhLCB2ZXJzaW9uKSA9PiAoe1xuICAgIGlkLFxuICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgdmVyc2lvbixcbiAgICBkYXRhLFxufSk7XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VFcnJvclJlc3BvbnNlID0gKGlkLCBlcnJvciwgdmVyc2lvbikgPT4gKHtcbiAgICBpZCxcbiAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICBlcnJvcixcbiAgICB2ZXJzaW9uLFxufSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXNzYWdlRm9ybWF0dGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RestrictedMethods = exports.Methods = void 0;\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (exports.Methods = Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (exports.RestrictedMethods = RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRequestId = void 0;\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\nexports.generateRequestId = generateRequestId;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvY29tbXVuaWNhdGlvbi91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXGNvbW11bmljYXRpb25cXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZW5lcmF0ZVJlcXVlc3RJZCA9IHZvaWQgMDtcbi8vIGkuZS4gMC0yNTUgLT4gJzAwJy0nZmYnXG5jb25zdCBkZWMyaGV4ID0gKGRlYykgPT4gZGVjLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCAnMCcpO1xuY29uc3QgZ2VuZXJhdGVJZCA9IChsZW4pID0+IHtcbiAgICBjb25zdCBhcnIgPSBuZXcgVWludDhBcnJheSgobGVuIHx8IDQwKSAvIDIpO1xuICAgIHdpbmRvdy5jcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKGFycik7XG4gICAgcmV0dXJuIEFycmF5LmZyb20oYXJyLCBkZWMyaGV4KS5qb2luKCcnKTtcbn07XG5jb25zdCBnZW5lcmF0ZVJlcXVlc3RJZCA9ICgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgcmV0dXJuIGdlbmVyYXRlSWQoMTApO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IERhdGUoKS5nZXRUaW1lKCkudG9TdHJpbmcoMzYpO1xufTtcbmV4cG9ydHMuZ2VuZXJhdGVSZXF1ZXN0SWQgPSBnZW5lcmF0ZVJlcXVlc3RJZDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst index_js_1 = __webpack_require__(/*! ../wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\");\nconst permissions_js_1 = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\");\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new index_js_1.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new permissions_js_1.PermissionsError('Permissions rejected', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\nexports[\"default\"] = requirePermission;\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RPC_CALLS = void 0;\nexports.RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvZXRoL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcZXRoXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlJQQ19DQUxMUyA9IHZvaWQgMDtcbmV4cG9ydHMuUlBDX0NBTExTID0ge1xuICAgIGV0aF9jYWxsOiAnZXRoX2NhbGwnLFxuICAgIGV0aF9nYXNQcmljZTogJ2V0aF9nYXNQcmljZScsXG4gICAgZXRoX2dldExvZ3M6ICdldGhfZ2V0TG9ncycsXG4gICAgZXRoX2dldEJhbGFuY2U6ICdldGhfZ2V0QmFsYW5jZScsXG4gICAgZXRoX2dldENvZGU6ICdldGhfZ2V0Q29kZScsXG4gICAgZXRoX2dldEJsb2NrQnlIYXNoOiAnZXRoX2dldEJsb2NrQnlIYXNoJyxcbiAgICBldGhfZ2V0QmxvY2tCeU51bWJlcjogJ2V0aF9nZXRCbG9ja0J5TnVtYmVyJyxcbiAgICBldGhfZ2V0U3RvcmFnZUF0OiAnZXRoX2dldFN0b3JhZ2VBdCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uQnlIYXNoOiAnZXRoX2dldFRyYW5zYWN0aW9uQnlIYXNoJyxcbiAgICBldGhfZ2V0VHJhbnNhY3Rpb25SZWNlaXB0OiAnZXRoX2dldFRyYW5zYWN0aW9uUmVjZWlwdCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uQ291bnQ6ICdldGhfZ2V0VHJhbnNhY3Rpb25Db3VudCcsXG4gICAgZXRoX2VzdGltYXRlR2FzOiAnZXRoX2VzdGltYXRlR2FzJyxcbiAgICBzYWZlX3NldFNldHRpbmdzOiAnc2FmZV9zZXRTZXR0aW5ncycsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Eth = void 0;\nconst constants_js_1 = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\");\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\nexports.Eth = Eth;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getSDKVersion = void 0;\nconst sdk_js_1 = __importDefault(__webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\"));\nexports[\"default\"] = sdk_js_1.default;\n__exportStar(__webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\"), exports);\n__exportStar(__webpack_require__(/*! ./communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\"), exports);\n__exportStar(__webpack_require__(/*! ./communication/messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\"), exports);\nvar version_js_1 = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\");\nObject.defineProperty(exports, \"getSDKVersion\", ({ enumerable: true, get: function () { return version_js_1.getSDKVersion; } }));\n__exportStar(__webpack_require__(/*! ./eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Safe = void 0;\nconst viem_1 = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_cjs/index.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js\");\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst constants_js_1 = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\");\nconst index_js_1 = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\");\nconst requirePermissions_js_1 = __importDefault(__webpack_require__(/*! ../decorators/requirePermissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js\"));\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(methods_js_1.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0, viem_1.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: constants_js_1.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === signatures_js_1.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0, viem_1.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: constants_js_1.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === signatures_js_1.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0, viem_1.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0, viem_1.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(methods_js_1.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0, index_js_1.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(methods_js_1.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\nexports.Safe = Safe;\n__decorate([\n    (0, requirePermissions_js_1.default)()\n], Safe.prototype, \"requestAddressBook\", null);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvc2FmZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxRQUFRO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QztBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxZQUFZO0FBQ1osZUFBZSxtQkFBTyxDQUFDLHFEQUFNO0FBQzdCLHdCQUF3QixtQkFBTyxDQUFDLG9HQUFpQjtBQUNqRCxxQkFBcUIsbUJBQU8sQ0FBQyxzSEFBNkI7QUFDMUQsdUJBQXVCLG1CQUFPLENBQUMsc0dBQXFCO0FBQ3BELG1CQUFtQixtQkFBTyxDQUFDLGtHQUFtQjtBQUM5QyxnREFBZ0QsbUJBQU8sQ0FBQyxzSUFBcUM7QUFDN0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsbUJBQW1CLElBQUk7QUFDNUQ7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxSUFBcUksTUFBTTtBQUMzSTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXHNhZmVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fZGVjb3JhdGUgPSAodGhpcyAmJiB0aGlzLl9fZGVjb3JhdGUpIHx8IGZ1bmN0aW9uIChkZWNvcmF0b3JzLCB0YXJnZXQsIGtleSwgZGVzYykge1xuICAgIHZhciBjID0gYXJndW1lbnRzLmxlbmd0aCwgciA9IGMgPCAzID8gdGFyZ2V0IDogZGVzYyA9PT0gbnVsbCA/IGRlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHRhcmdldCwga2V5KSA6IGRlc2MsIGQ7XG4gICAgaWYgKHR5cGVvZiBSZWZsZWN0ID09PSBcIm9iamVjdFwiICYmIHR5cGVvZiBSZWZsZWN0LmRlY29yYXRlID09PSBcImZ1bmN0aW9uXCIpIHIgPSBSZWZsZWN0LmRlY29yYXRlKGRlY29yYXRvcnMsIHRhcmdldCwga2V5LCBkZXNjKTtcbiAgICBlbHNlIGZvciAodmFyIGkgPSBkZWNvcmF0b3JzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSBpZiAoZCA9IGRlY29yYXRvcnNbaV0pIHIgPSAoYyA8IDMgPyBkKHIpIDogYyA+IDMgPyBkKHRhcmdldCwga2V5LCByKSA6IGQodGFyZ2V0LCBrZXkpKSB8fCByO1xuICAgIHJldHVybiBjID4gMyAmJiByICYmIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgciksIHI7XG59O1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5TYWZlID0gdm9pZCAwO1xuY29uc3QgdmllbV8xID0gcmVxdWlyZShcInZpZW1cIik7XG5jb25zdCBzaWduYXR1cmVzX2pzXzEgPSByZXF1aXJlKFwiLi9zaWduYXR1cmVzLmpzXCIpO1xuY29uc3QgbWV0aG9kc19qc18xID0gcmVxdWlyZShcIi4uL2NvbW11bmljYXRpb24vbWV0aG9kcy5qc1wiKTtcbmNvbnN0IGNvbnN0YW50c19qc18xID0gcmVxdWlyZShcIi4uL2V0aC9jb25zdGFudHMuanNcIik7XG5jb25zdCBpbmRleF9qc18xID0gcmVxdWlyZShcIi4uL3R5cGVzL2luZGV4LmpzXCIpO1xuY29uc3QgcmVxdWlyZVBlcm1pc3Npb25zX2pzXzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIi4uL2RlY29yYXRvcnMvcmVxdWlyZVBlcm1pc3Npb25zLmpzXCIpKTtcbmNsYXNzIFNhZmUge1xuICAgIGNvbnN0cnVjdG9yKGNvbW11bmljYXRvcikge1xuICAgICAgICB0aGlzLmNvbW11bmljYXRvciA9IGNvbW11bmljYXRvcjtcbiAgICB9XG4gICAgYXN5bmMgZ2V0Q2hhaW5JbmZvKCkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY29tbXVuaWNhdG9yLnNlbmQobWV0aG9kc19qc18xLk1ldGhvZHMuZ2V0Q2hhaW5JbmZvLCB1bmRlZmluZWQpO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9XG4gICAgYXN5bmMgZ2V0SW5mbygpIHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNvbW11bmljYXRvci5zZW5kKG1ldGhvZHNfanNfMS5NZXRob2RzLmdldFNhZmVJbmZvLCB1bmRlZmluZWQpO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9XG4gICAgLy8gVGhlcmUgaXMgYSBwb3NzaWJpbGl0eSB0aGF0IHRoaXMgbWV0aG9kIHdpbGwgY2hhbmdlIGJlY2F1c2Ugd2UgbWF5IGFkZCBwYWdpbmF0aW9uIHRvIHRoZSBlbmRwb2ludFxuICAgIGFzeW5jIGV4cGVyaW1lbnRhbF9nZXRCYWxhbmNlcyh7IGN1cnJlbmN5ID0gJ3VzZCcgfSA9IHt9KSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jb21tdW5pY2F0b3Iuc2VuZChtZXRob2RzX2pzXzEuTWV0aG9kcy5nZXRTYWZlQmFsYW5jZXMsIHtcbiAgICAgICAgICAgIGN1cnJlbmN5LFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gICAgfVxuICAgIGFzeW5jIGNoZWNrMTI3MVNpZ25hdHVyZShtZXNzYWdlSGFzaCwgc2lnbmF0dXJlID0gJzB4Jykge1xuICAgICAgICBjb25zdCBzYWZlSW5mbyA9IGF3YWl0IHRoaXMuZ2V0SW5mbygpO1xuICAgICAgICBjb25zdCBlbmNvZGVkSXNWYWxpZFNpZ25hdHVyZUNhbGwgPSAoMCwgdmllbV8xLmVuY29kZUZ1bmN0aW9uRGF0YSkoe1xuICAgICAgICAgICAgYWJpOiBbXG4gICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBjb25zdGFudDogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGlucHV0czogW1xuICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICdfZGF0YUhhc2gnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdieXRlczMyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogJ19zaWduYXR1cmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdieXRlcycsXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgICAgICBuYW1lOiAnaXNWYWxpZFNpZ25hdHVyZScsXG4gICAgICAgICAgICAgICAgICAgIG91dHB1dHM6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnYnl0ZXM0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgICAgICAgIHBheWFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBzdGF0ZU11dGFiaWxpdHk6ICdub25wYXlhYmxlJyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Z1bmN0aW9uJyxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIGZ1bmN0aW9uTmFtZTogJ2lzVmFsaWRTaWduYXR1cmUnLFxuICAgICAgICAgICAgYXJnczogW21lc3NhZ2VIYXNoLCBzaWduYXR1cmVdLFxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHtcbiAgICAgICAgICAgIGNhbGw6IGNvbnN0YW50c19qc18xLlJQQ19DQUxMUy5ldGhfY2FsbCxcbiAgICAgICAgICAgIHBhcmFtczogW1xuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgdG86IHNhZmVJbmZvLnNhZmVBZGRyZXNzLFxuICAgICAgICAgICAgICAgICAgICBkYXRhOiBlbmNvZGVkSXNWYWxpZFNpZ25hdHVyZUNhbGwsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAnbGF0ZXN0JyxcbiAgICAgICAgICAgIF0sXG4gICAgICAgIH07XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY29tbXVuaWNhdG9yLnNlbmQobWV0aG9kc19qc18xLk1ldGhvZHMucnBjQ2FsbCwgcGF5bG9hZCk7XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5zbGljZSgwLCAxMCkudG9Mb3dlckNhc2UoKSA9PT0gc2lnbmF0dXJlc19qc18xLk1BR0lDX1ZBTFVFO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBhc3luYyBjaGVjazEyNzFTaWduYXR1cmVCeXRlcyhtZXNzYWdlSGFzaCwgc2lnbmF0dXJlID0gJzB4Jykge1xuICAgICAgICBjb25zdCBzYWZlSW5mbyA9IGF3YWl0IHRoaXMuZ2V0SW5mbygpO1xuICAgICAgICBjb25zdCBlbmNvZGVkSXNWYWxpZFNpZ25hdHVyZUNhbGwgPSAoMCwgdmllbV8xLmVuY29kZUZ1bmN0aW9uRGF0YSkoe1xuICAgICAgICAgICAgYWJpOiBbXG4gICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBjb25zdGFudDogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGlucHV0czogW1xuICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICdfZGF0YScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2J5dGVzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogJ19zaWduYXR1cmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdieXRlcycsXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgICAgICBuYW1lOiAnaXNWYWxpZFNpZ25hdHVyZScsXG4gICAgICAgICAgICAgICAgICAgIG91dHB1dHM6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnYnl0ZXM0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgICAgICAgIHBheWFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBzdGF0ZU11dGFiaWxpdHk6ICdub25wYXlhYmxlJyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Z1bmN0aW9uJyxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIGZ1bmN0aW9uTmFtZTogJ2lzVmFsaWRTaWduYXR1cmUnLFxuICAgICAgICAgICAgYXJnczogW21lc3NhZ2VIYXNoLCBzaWduYXR1cmVdLFxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHtcbiAgICAgICAgICAgIGNhbGw6IGNvbnN0YW50c19qc18xLlJQQ19DQUxMUy5ldGhfY2FsbCxcbiAgICAgICAgICAgIHBhcmFtczogW1xuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgdG86IHNhZmVJbmZvLnNhZmVBZGRyZXNzLFxuICAgICAgICAgICAgICAgICAgICBkYXRhOiBlbmNvZGVkSXNWYWxpZFNpZ25hdHVyZUNhbGwsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAnbGF0ZXN0JyxcbiAgICAgICAgICAgIF0sXG4gICAgICAgIH07XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY29tbXVuaWNhdG9yLnNlbmQobWV0aG9kc19qc18xLk1ldGhvZHMucnBjQ2FsbCwgcGF5bG9hZCk7XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5zbGljZSgwLCAxMCkudG9Mb3dlckNhc2UoKSA9PT0gc2lnbmF0dXJlc19qc18xLk1BR0lDX1ZBTFVFX0JZVEVTO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjYWxjdWxhdGVNZXNzYWdlSGFzaChtZXNzYWdlKSB7XG4gICAgICAgIHJldHVybiAoMCwgdmllbV8xLmhhc2hNZXNzYWdlKShtZXNzYWdlKTtcbiAgICB9XG4gICAgY2FsY3VsYXRlVHlwZWRNZXNzYWdlSGFzaCh0eXBlZE1lc3NhZ2UpIHtcbiAgICAgICAgY29uc3QgY2hhaW5JZCA9IHR5cGVvZiB0eXBlZE1lc3NhZ2UuZG9tYWluLmNoYWluSWQgPT09ICdvYmplY3QnXG4gICAgICAgICAgICA/IHR5cGVkTWVzc2FnZS5kb21haW4uY2hhaW5JZC50b051bWJlcigpXG4gICAgICAgICAgICA6IE51bWJlcih0eXBlZE1lc3NhZ2UuZG9tYWluLmNoYWluSWQpO1xuICAgICAgICBsZXQgcHJpbWFyeVR5cGUgPSB0eXBlZE1lc3NhZ2UucHJpbWFyeVR5cGU7XG4gICAgICAgIGlmICghcHJpbWFyeVR5cGUpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkcyA9IE9iamVjdC52YWx1ZXModHlwZWRNZXNzYWdlLnR5cGVzKTtcbiAgICAgICAgICAgIC8vIFdlIHRyeSB0byBpbmZlciBwcmltYXJ5VHlwZSAoc2ltcGxpZmllZCBldGhlcidzIHZlcnNpb24pXG4gICAgICAgICAgICBjb25zdCBwcmltYXJ5VHlwZXMgPSBPYmplY3Qua2V5cyh0eXBlZE1lc3NhZ2UudHlwZXMpLmZpbHRlcigodHlwZU5hbWUpID0+IGZpZWxkcy5ldmVyeSgoZGF0YVR5cGVzKSA9PiBkYXRhVHlwZXMuZXZlcnkoKHsgdHlwZSB9KSA9PiB0eXBlLnJlcGxhY2UoJ1snLCAnJykucmVwbGFjZSgnXScsICcnKSAhPT0gdHlwZU5hbWUpKSk7XG4gICAgICAgICAgICBpZiAocHJpbWFyeVR5cGVzLmxlbmd0aCA9PT0gMCB8fCBwcmltYXJ5VHlwZXMubGVuZ3RoID4gMSlcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1BsZWFzZSBzcGVjaWZ5IHByaW1hcnlUeXBlJyk7XG4gICAgICAgICAgICBwcmltYXJ5VHlwZSA9IHByaW1hcnlUeXBlc1swXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gKDAsIHZpZW1fMS5oYXNoVHlwZWREYXRhKSh7XG4gICAgICAgICAgICBtZXNzYWdlOiB0eXBlZE1lc3NhZ2UubWVzc2FnZSxcbiAgICAgICAgICAgIGRvbWFpbjoge1xuICAgICAgICAgICAgICAgIC4uLnR5cGVkTWVzc2FnZS5kb21haW4sXG4gICAgICAgICAgICAgICAgY2hhaW5JZCxcbiAgICAgICAgICAgICAgICB2ZXJpZnlpbmdDb250cmFjdDogdHlwZWRNZXNzYWdlLmRvbWFpbi52ZXJpZnlpbmdDb250cmFjdCxcbiAgICAgICAgICAgICAgICBzYWx0OiB0eXBlZE1lc3NhZ2UuZG9tYWluLnNhbHQsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgdHlwZXM6IHR5cGVkTWVzc2FnZS50eXBlcyxcbiAgICAgICAgICAgIHByaW1hcnlUeXBlLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgYXN5bmMgZ2V0T2ZmQ2hhaW5TaWduYXR1cmUobWVzc2FnZUhhc2gpIHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNvbW11bmljYXRvci5zZW5kKG1ldGhvZHNfanNfMS5NZXRob2RzLmdldE9mZkNoYWluU2lnbmF0dXJlLCBtZXNzYWdlSGFzaCk7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH1cbiAgICBhc3luYyBpc01lc3NhZ2VTaWduZWQobWVzc2FnZSwgc2lnbmF0dXJlID0gJzB4Jykge1xuICAgICAgICBsZXQgY2hlY2s7XG4gICAgICAgIGlmICh0eXBlb2YgbWVzc2FnZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgIGNoZWNrID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2VIYXNoID0gdGhpcy5jYWxjdWxhdGVNZXNzYWdlSGFzaChtZXNzYWdlKTtcbiAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlSGFzaFNpZ25lZCA9IGF3YWl0IHRoaXMuaXNNZXNzYWdlSGFzaFNpZ25lZChtZXNzYWdlSGFzaCwgc2lnbmF0dXJlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbWVzc2FnZUhhc2hTaWduZWQ7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGlmICgoMCwgaW5kZXhfanNfMS5pc09iamVjdEVJUDcxMlR5cGVkRGF0YSkobWVzc2FnZSkpIHtcbiAgICAgICAgICAgIGNoZWNrID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2VIYXNoID0gdGhpcy5jYWxjdWxhdGVUeXBlZE1lc3NhZ2VIYXNoKG1lc3NhZ2UpO1xuICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2VIYXNoU2lnbmVkID0gYXdhaXQgdGhpcy5pc01lc3NhZ2VIYXNoU2lnbmVkKG1lc3NhZ2VIYXNoLCBzaWduYXR1cmUpO1xuICAgICAgICAgICAgICAgIHJldHVybiBtZXNzYWdlSGFzaFNpZ25lZDtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNoZWNrKSB7XG4gICAgICAgICAgICBjb25zdCBpc1ZhbGlkID0gYXdhaXQgY2hlY2soKTtcbiAgICAgICAgICAgIHJldHVybiBpc1ZhbGlkO1xuICAgICAgICB9XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBtZXNzYWdlIHR5cGUnKTtcbiAgICB9XG4gICAgYXN5bmMgaXNNZXNzYWdlSGFzaFNpZ25lZChtZXNzYWdlSGFzaCwgc2lnbmF0dXJlID0gJzB4Jykge1xuICAgICAgICBjb25zdCBjaGVja3MgPSBbdGhpcy5jaGVjazEyNzFTaWduYXR1cmUuYmluZCh0aGlzKSwgdGhpcy5jaGVjazEyNzFTaWduYXR1cmVCeXRlcy5iaW5kKHRoaXMpXTtcbiAgICAgICAgZm9yIChjb25zdCBjaGVjayBvZiBjaGVja3MpIHtcbiAgICAgICAgICAgIGNvbnN0IGlzVmFsaWQgPSBhd2FpdCBjaGVjayhtZXNzYWdlSGFzaCwgc2lnbmF0dXJlKTtcbiAgICAgICAgICAgIGlmIChpc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBhc3luYyBnZXRFbnZpcm9ubWVudEluZm8oKSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jb21tdW5pY2F0b3Iuc2VuZChtZXRob2RzX2pzXzEuTWV0aG9kcy5nZXRFbnZpcm9ubWVudEluZm8sIHVuZGVmaW5lZCk7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH1cbiAgICBhc3luYyByZXF1ZXN0QWRkcmVzc0Jvb2soKSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jb21tdW5pY2F0b3Iuc2VuZChtZXRob2RzX2pzXzEuTWV0aG9kcy5yZXF1ZXN0QWRkcmVzc0Jvb2ssIHVuZGVmaW5lZCk7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH1cbn1cbmV4cG9ydHMuU2FmZSA9IFNhZmU7XG5fX2RlY29yYXRlKFtcbiAgICAoMCwgcmVxdWlyZVBlcm1pc3Npb25zX2pzXzEuZGVmYXVsdCkoKVxuXSwgU2FmZS5wcm90b3R5cGUsIFwicmVxdWVzdEFkZHJlc3NCb29rXCIsIG51bGwpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MAGIC_VALUE_BYTES = exports.MAGIC_VALUE = void 0;\nconst MAGIC_VALUE = '0x1626ba7e';\nexports.MAGIC_VALUE = MAGIC_VALUE;\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\nexports.MAGIC_VALUE_BYTES = MAGIC_VALUE_BYTES;\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvc2FmZS9zaWduYXR1cmVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHlCQUF5QixHQUFHLG1CQUFtQjtBQUMvQztBQUNBLG1CQUFtQjtBQUNuQjtBQUNBLHlCQUF5QjtBQUN6QiIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcc2FmZVxcc2lnbmF0dXJlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuTUFHSUNfVkFMVUVfQllURVMgPSBleHBvcnRzLk1BR0lDX1ZBTFVFID0gdm9pZCAwO1xuY29uc3QgTUFHSUNfVkFMVUUgPSAnMHgxNjI2YmE3ZSc7XG5leHBvcnRzLk1BR0lDX1ZBTFVFID0gTUFHSUNfVkFMVUU7XG5jb25zdCBNQUdJQ19WQUxVRV9CWVRFUyA9ICcweDIwYzEzYjBiJztcbmV4cG9ydHMuTUFHSUNfVkFMVUVfQllURVMgPSBNQUdJQ19WQUxVRV9CWVRFUztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpZ25hdHVyZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst index_js_1 = __importDefault(__webpack_require__(/*! ./communication/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js\"));\nconst index_js_2 = __webpack_require__(/*! ./txs/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js\");\nconst index_js_3 = __webpack_require__(/*! ./eth/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js\");\nconst index_js_4 = __webpack_require__(/*! ./safe/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js\");\nconst index_js_5 = __webpack_require__(/*! ./wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\");\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new index_js_1.default(allowedDomains, debug);\n        this.eth = new index_js_3.Eth(this.communicator);\n        this.txs = new index_js_2.TXs(this.communicator);\n        this.safe = new index_js_4.Safe(this.communicator);\n        this.wallet = new index_js_5.Wallet(this.communicator);\n    }\n}\nexports[\"default\"] = SafeAppsSDK;\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TXs = void 0;\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst index_js_1 = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\");\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(methods_js_1.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(methods_js_1.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0, index_js_1.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(methods_js_1.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(methods_js_1.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\nexports.TXs = TXs;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TransferDirection = exports.TransactionStatus = exports.TokenType = exports.Operation = void 0;\nvar safe_gateway_typescript_sdk_1 = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\nObject.defineProperty(exports, \"Operation\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.Operation; } }));\nObject.defineProperty(exports, \"TokenType\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TokenType; } }));\nObject.defineProperty(exports, \"TransactionStatus\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TransactionStatus; } }));\nObject.defineProperty(exports, \"TransferDirection\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TransferDirection; } }));\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvZ2F0ZXdheS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUIsR0FBRyx5QkFBeUIsR0FBRyxpQkFBaUIsR0FBRyxpQkFBaUI7QUFDN0Ysb0NBQW9DLG1CQUFPLENBQUMsNkhBQTBDO0FBQ3RGLDZDQUE0QyxFQUFFLHFDQUFxQyxtREFBbUQsRUFBQztBQUN2SSw2Q0FBNEMsRUFBRSxxQ0FBcUMsbURBQW1ELEVBQUM7QUFDdkkscURBQW9ELEVBQUUscUNBQXFDLDJEQUEyRCxFQUFDO0FBQ3ZKLHFEQUFvRCxFQUFFLHFDQUFxQywyREFBMkQsRUFBQztBQUN2SiIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcdHlwZXNcXGdhdGV3YXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlRyYW5zZmVyRGlyZWN0aW9uID0gZXhwb3J0cy5UcmFuc2FjdGlvblN0YXR1cyA9IGV4cG9ydHMuVG9rZW5UeXBlID0gZXhwb3J0cy5PcGVyYXRpb24gPSB2b2lkIDA7XG52YXIgc2FmZV9nYXRld2F5X3R5cGVzY3JpcHRfc2RrXzEgPSByZXF1aXJlKFwiQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1wiKTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIk9wZXJhdGlvblwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gc2FmZV9nYXRld2F5X3R5cGVzY3JpcHRfc2RrXzEuT3BlcmF0aW9uOyB9IH0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiVG9rZW5UeXBlXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBzYWZlX2dhdGV3YXlfdHlwZXNjcmlwdF9zZGtfMS5Ub2tlblR5cGU7IH0gfSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJUcmFuc2FjdGlvblN0YXR1c1wiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gc2FmZV9nYXRld2F5X3R5cGVzY3JpcHRfc2RrXzEuVHJhbnNhY3Rpb25TdGF0dXM7IH0gfSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJUcmFuc2ZlckRpcmVjdGlvblwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gc2FmZV9nYXRld2F5X3R5cGVzY3JpcHRfc2RrXzEuVHJhbnNmZXJEaXJlY3Rpb247IH0gfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nYXRld2F5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js\"), exports);\n__exportStar(__webpack_require__(/*! ./rpc.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js\"), exports);\n__exportStar(__webpack_require__(/*! ./gateway.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js\"), exports);\n__exportStar(__webpack_require__(/*! ./messaging.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvbWVzc2FnaW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQixtQkFBTyxDQUFDLHNIQUE2QjtBQUMxRCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcdHlwZXNcXG1lc3NhZ2luZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmNvbnN0IG1ldGhvZHNfanNfMSA9IHJlcXVpcmUoXCIuLi9jb21tdW5pY2F0aW9uL21ldGhvZHMuanNcIik7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXNzYWdpbmcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PermissionsError = exports.PERMISSIONS_REQUEST_REJECTED = void 0;\nexports.PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\nexports.PermissionsError = PermissionsError;\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvcGVybWlzc2lvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCLEdBQUcsb0NBQW9DO0FBQy9ELG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QiIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcdHlwZXNcXHBlcm1pc3Npb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5QZXJtaXNzaW9uc0Vycm9yID0gZXhwb3J0cy5QRVJNSVNTSU9OU19SRVFVRVNUX1JFSkVDVEVEID0gdm9pZCAwO1xuZXhwb3J0cy5QRVJNSVNTSU9OU19SRVFVRVNUX1JFSkVDVEVEID0gNDAwMTtcbmNsYXNzIFBlcm1pc3Npb25zRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgY29kZSwgZGF0YSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5jb2RlID0gY29kZTtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YTtcbiAgICAgICAgLy8gU2hvdWxkIGFkanVzdCBwcm90b3R5cGUgbWFudWFsbHkgYmVjYXVzZSBob3cgVFMgaGFuZGxlcyB0aGUgdHlwZSBleHRlbnNpb24gY29tcGlsYXRpb25cbiAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL01pY3Jvc29mdC9UeXBlU2NyaXB0L3dpa2kvQnJlYWtpbmctQ2hhbmdlcyNleHRlbmRpbmctYnVpbHQtaW5zLWxpa2UtZXJyb3ItYXJyYXktYW5kLW1hcC1tYXktbm8tbG9uZ2VyLXdvcmtcbiAgICAgICAgT2JqZWN0LnNldFByb3RvdHlwZU9mKHRoaXMsIFBlcm1pc3Npb25zRXJyb3IucHJvdG90eXBlKTtcbiAgICB9XG59XG5leHBvcnRzLlBlcm1pc3Npb25zRXJyb3IgPSBQZXJtaXNzaW9uc0Vycm9yO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGVybWlzc2lvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvcnBjLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcY2pzXFx0eXBlc1xccnBjLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cnBjLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectEIP712TypedData = void 0;\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\nexports.isObjectEIP712TypedData = isObjectEIP712TypedData;\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvc2RrLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0IiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXHR5cGVzXFxzZGsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmlzT2JqZWN0RUlQNzEyVHlwZWREYXRhID0gdm9pZCAwO1xuY29uc3QgaXNPYmplY3RFSVA3MTJUeXBlZERhdGEgPSAob2JqKSA9PiB7XG4gICAgcmV0dXJuIHR5cGVvZiBvYmogPT09ICdvYmplY3QnICYmIG9iaiAhPSBudWxsICYmICdkb21haW4nIGluIG9iaiAmJiAndHlwZXMnIGluIG9iaiAmJiAnbWVzc2FnZScgaW4gb2JqO1xufTtcbmV4cG9ydHMuaXNPYmplY3RFSVA3MTJUeXBlZERhdGEgPSBpc09iamVjdEVJUDcxMlR5cGVkRGF0YTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNkay5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getSDKVersion = void 0;\nconst getSDKVersion = () => '9.1.0';\nexports.getSDKVersion = getSDKVersion;\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckIiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmdldFNES1ZlcnNpb24gPSB2b2lkIDA7XG5jb25zdCBnZXRTREtWZXJzaW9uID0gKCkgPT4gJzkuMS4wJztcbmV4cG9ydHMuZ2V0U0RLVmVyc2lvbiA9IGdldFNES1ZlcnNpb247XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Wallet = void 0;\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst permissions_js_1 = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\");\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(methods_js_1.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new permissions_js_1.PermissionsError('Permissions request is invalid', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new permissions_js_1.PermissionsError('Permissions rejected', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(methods_js_1.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\nexports.Wallet = Wallet;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvd2FsbGV0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZCxxQkFBcUIsbUJBQU8sQ0FBQyxzSEFBNkI7QUFDMUQseUJBQXlCLG1CQUFPLENBQUMsOEdBQXlCO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLGNBQWM7QUFDZCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcd2FsbGV0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuV2FsbGV0ID0gdm9pZCAwO1xuY29uc3QgbWV0aG9kc19qc18xID0gcmVxdWlyZShcIi4uL2NvbW11bmljYXRpb24vbWV0aG9kcy5qc1wiKTtcbmNvbnN0IHBlcm1pc3Npb25zX2pzXzEgPSByZXF1aXJlKFwiLi4vdHlwZXMvcGVybWlzc2lvbnMuanNcIik7XG5jbGFzcyBXYWxsZXQge1xuICAgIGNvbnN0cnVjdG9yKGNvbW11bmljYXRvcikge1xuICAgICAgICB0aGlzLmNvbW11bmljYXRvciA9IGNvbW11bmljYXRvcjtcbiAgICB9XG4gICAgYXN5bmMgZ2V0UGVybWlzc2lvbnMoKSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jb21tdW5pY2F0b3Iuc2VuZChtZXRob2RzX2pzXzEuTWV0aG9kcy53YWxsZXRfZ2V0UGVybWlzc2lvbnMsIHVuZGVmaW5lZCk7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH1cbiAgICBhc3luYyByZXF1ZXN0UGVybWlzc2lvbnMocGVybWlzc2lvbnMpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzUGVybWlzc2lvblJlcXVlc3RWYWxpZChwZXJtaXNzaW9ucykpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBwZXJtaXNzaW9uc19qc18xLlBlcm1pc3Npb25zRXJyb3IoJ1Blcm1pc3Npb25zIHJlcXVlc3QgaXMgaW52YWxpZCcsIHBlcm1pc3Npb25zX2pzXzEuUEVSTUlTU0lPTlNfUkVRVUVTVF9SRUpFQ1RFRCk7XG4gICAgICAgIH1cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jb21tdW5pY2F0b3Iuc2VuZChtZXRob2RzX2pzXzEuTWV0aG9kcy53YWxsZXRfcmVxdWVzdFBlcm1pc3Npb25zLCBwZXJtaXNzaW9ucyk7XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgcGVybWlzc2lvbnNfanNfMS5QZXJtaXNzaW9uc0Vycm9yKCdQZXJtaXNzaW9ucyByZWplY3RlZCcsIHBlcm1pc3Npb25zX2pzXzEuUEVSTUlTU0lPTlNfUkVRVUVTVF9SRUpFQ1RFRCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaXNQZXJtaXNzaW9uUmVxdWVzdFZhbGlkKHBlcm1pc3Npb25zKSB7XG4gICAgICAgIHJldHVybiBwZXJtaXNzaW9ucy5ldmVyeSgocHIpID0+IHtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgcHIgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5rZXlzKHByKS5ldmVyeSgobWV0aG9kKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChPYmplY3QudmFsdWVzKG1ldGhvZHNfanNfMS5SZXN0cmljdGVkTWV0aG9kcykuaW5jbHVkZXMobWV0aG9kKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnRzLldhbGxldCA9IFdhbGxldDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.RestrictedMethods),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PostMessageCommunicator);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* binding */ MessageFormatter)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\");\n\n\nclass MessageFormatter {\n}\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0,_version_js__WEBPACK_IMPORTED_MODULE_0__.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUNDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNERBQWlCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsMERBQWE7QUFDckMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQzJCO0FBQzVCIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFxjb21tdW5pY2F0aW9uXFxtZXNzYWdlRm9ybWF0dGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFNES1ZlcnNpb24gfSBmcm9tICcuLi92ZXJzaW9uLmpzJztcbmltcG9ydCB7IGdlbmVyYXRlUmVxdWVzdElkIH0gZnJvbSAnLi91dGlscy5qcyc7XG5jbGFzcyBNZXNzYWdlRm9ybWF0dGVyIHtcbn1cbk1lc3NhZ2VGb3JtYXR0ZXIubWFrZVJlcXVlc3QgPSAobWV0aG9kLCBwYXJhbXMpID0+IHtcbiAgICBjb25zdCBpZCA9IGdlbmVyYXRlUmVxdWVzdElkKCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgaWQsXG4gICAgICAgIG1ldGhvZCxcbiAgICAgICAgcGFyYW1zLFxuICAgICAgICBlbnY6IHtcbiAgICAgICAgICAgIHNka1ZlcnNpb246IGdldFNES1ZlcnNpb24oKSxcbiAgICAgICAgfSxcbiAgICB9O1xufTtcbk1lc3NhZ2VGb3JtYXR0ZXIubWFrZVJlc3BvbnNlID0gKGlkLCBkYXRhLCB2ZXJzaW9uKSA9PiAoe1xuICAgIGlkLFxuICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgdmVyc2lvbixcbiAgICBkYXRhLFxufSk7XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VFcnJvclJlc3BvbnNlID0gKGlkLCBlcnJvciwgdmVyc2lvbikgPT4gKHtcbiAgICBpZCxcbiAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICBlcnJvcixcbiAgICB2ZXJzaW9uLFxufSk7XG5leHBvcnQgeyBNZXNzYWdlRm9ybWF0dGVyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXNzYWdlRm9ybWF0dGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* binding */ Methods),\n/* harmony export */   RestrictedMethods: () => (/* binding */ RestrictedMethods)\n/* harmony export */ });\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateRequestId: () => (/* binding */ generateRequestId)\n/* harmony export */ });\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vY29tbXVuaWNhdGlvbi91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDNkI7QUFDN0IiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXGNvbW11bmljYXRpb25cXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGkuZS4gMC0yNTUgLT4gJzAwJy0nZmYnXG5jb25zdCBkZWMyaGV4ID0gKGRlYykgPT4gZGVjLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCAnMCcpO1xuY29uc3QgZ2VuZXJhdGVJZCA9IChsZW4pID0+IHtcbiAgICBjb25zdCBhcnIgPSBuZXcgVWludDhBcnJheSgobGVuIHx8IDQwKSAvIDIpO1xuICAgIHdpbmRvdy5jcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKGFycik7XG4gICAgcmV0dXJuIEFycmF5LmZyb20oYXJyLCBkZWMyaGV4KS5qb2luKCcnKTtcbn07XG5jb25zdCBnZW5lcmF0ZVJlcXVlc3RJZCA9ICgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgcmV0dXJuIGdlbmVyYXRlSWQoMTApO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IERhdGUoKS5nZXRUaW1lKCkudG9TdHJpbmcoMzYpO1xufTtcbmV4cG9ydCB7IGdlbmVyYXRlUmVxdWVzdElkIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (requirePermission);\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RPC_CALLS: () => (/* binding */ RPC_CALLS)\n/* harmony export */ });\nconst RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vZXRoL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXGV0aFxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBSUENfQ0FMTFMgPSB7XG4gICAgZXRoX2NhbGw6ICdldGhfY2FsbCcsXG4gICAgZXRoX2dhc1ByaWNlOiAnZXRoX2dhc1ByaWNlJyxcbiAgICBldGhfZ2V0TG9nczogJ2V0aF9nZXRMb2dzJyxcbiAgICBldGhfZ2V0QmFsYW5jZTogJ2V0aF9nZXRCYWxhbmNlJyxcbiAgICBldGhfZ2V0Q29kZTogJ2V0aF9nZXRDb2RlJyxcbiAgICBldGhfZ2V0QmxvY2tCeUhhc2g6ICdldGhfZ2V0QmxvY2tCeUhhc2gnLFxuICAgIGV0aF9nZXRCbG9ja0J5TnVtYmVyOiAnZXRoX2dldEJsb2NrQnlOdW1iZXInLFxuICAgIGV0aF9nZXRTdG9yYWdlQXQ6ICdldGhfZ2V0U3RvcmFnZUF0JyxcbiAgICBldGhfZ2V0VHJhbnNhY3Rpb25CeUhhc2g6ICdldGhfZ2V0VHJhbnNhY3Rpb25CeUhhc2gnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvblJlY2VpcHQ6ICdldGhfZ2V0VHJhbnNhY3Rpb25SZWNlaXB0JyxcbiAgICBldGhfZ2V0VHJhbnNhY3Rpb25Db3VudDogJ2V0aF9nZXRUcmFuc2FjdGlvbkNvdW50JyxcbiAgICBldGhfZXN0aW1hdGVHYXM6ICdldGhfZXN0aW1hdGVHYXMnLFxuICAgIHNhZmVfc2V0U2V0dGluZ3M6ICdzYWZlX3NldFNldHRpbmdzJyxcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eth: () => (/* binding */ Eth)\n/* harmony export */ });\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* reexport safe */ _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__.MessageFormatter),\n/* harmony export */   Methods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.Methods),\n/* harmony export */   Operation: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.Operation),\n/* harmony export */   RPC_CALLS: () => (/* reexport safe */ _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__.RPC_CALLS),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.RestrictedMethods),\n/* harmony export */   TokenType: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransferDirection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSDKVersion: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_4__.getSDKVersion),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communication/messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_sdk_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkI7QUFDM0IsaUVBQWUsK0NBQUcsRUFBQztBQUNNO0FBQ1E7QUFDVTtBQUNTO0FBQ1A7QUFDVjtBQUNuQyIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNESyBmcm9tICcuL3Nkay5qcyc7XG5leHBvcnQgZGVmYXVsdCBTREs7XG5leHBvcnQgKiBmcm9tICcuL3Nkay5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzL2luZGV4LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vY29tbXVuaWNhdGlvbi9tZXRob2RzLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzJztcbmV4cG9ydCB7IGdldFNES1ZlcnNpb24gfSBmcm9tICcuL3ZlcnNpb24uanMnO1xuZXhwb3J0ICogZnJvbSAnLi9ldGgvY29uc3RhbnRzLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Safe: () => (/* binding */ Safe)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/abi/encodeFunctionData.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/signature/hashMessage.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/signature/hashTypedData.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../decorators/requirePermissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\");\nvar __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\n\n\n\n\n\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0,viem__WEBPACK_IMPORTED_MODULE_6__.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0,viem__WEBPACK_IMPORTED_MODULE_7__.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0,_types_index_js__WEBPACK_IMPORTED_MODULE_3__.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\n__decorate([\n    (0,_decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n], Safe.prototype, \"requestAddressBook\", null);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAGIC_VALUE: () => (/* binding */ MAGIC_VALUE),\n/* harmony export */   MAGIC_VALUE_BYTES: () => (/* binding */ MAGIC_VALUE_BYTES)\n/* harmony export */ });\nconst MAGIC_VALUE = '0x1626ba7e';\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\n\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2FmZS9zaWduYXR1cmVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUMwQztBQUMxQyIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcc2FmZVxcc2lnbmF0dXJlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBNQUdJQ19WQUxVRSA9ICcweDE2MjZiYTdlJztcbmNvbnN0IE1BR0lDX1ZBTFVFX0JZVEVTID0gJzB4MjBjMTNiMGInO1xuZXhwb3J0IHsgTUFHSUNfVkFMVUUsIE1BR0lDX1ZBTFVFX0JZVEVTIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zaWduYXR1cmVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _communication_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./communication/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\");\n/* harmony import */ var _txs_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./txs/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\");\n/* harmony import */ var _eth_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./eth/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\");\n/* harmony import */ var _safe_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./safe/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\");\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n\n\n\n\n\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new _communication_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](allowedDomains, debug);\n        this.eth = new _eth_index_js__WEBPACK_IMPORTED_MODULE_2__.Eth(this.communicator);\n        this.txs = new _txs_index_js__WEBPACK_IMPORTED_MODULE_1__.TXs(this.communicator);\n        this.safe = new _safe_index_js__WEBPACK_IMPORTED_MODULE_3__.Safe(this.communicator);\n        this.wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__.Wallet(this.communicator);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeAppsSDK);\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUN4QjtBQUNBO0FBQ0U7QUFDSTtBQUMzQztBQUNBLHlCQUF5QjtBQUN6QixnQkFBZ0IsdUNBQXVDO0FBQ3ZELGdDQUFnQywrREFBcUI7QUFDckQsdUJBQXVCLDhDQUFHO0FBQzFCLHVCQUF1Qiw4Q0FBRztBQUMxQix3QkFBd0IsZ0RBQUk7QUFDNUIsMEJBQTBCLG9EQUFNO0FBQ2hDO0FBQ0E7QUFDQSxpRUFBZSxXQUFXLEVBQUM7QUFDM0IiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHNkay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSW50ZXJmYWNlQ29tbXVuaWNhdG9yIGZyb20gJy4vY29tbXVuaWNhdGlvbi9pbmRleC5qcyc7XG5pbXBvcnQgeyBUWHMgfSBmcm9tICcuL3R4cy9pbmRleC5qcyc7XG5pbXBvcnQgeyBFdGggfSBmcm9tICcuL2V0aC9pbmRleC5qcyc7XG5pbXBvcnQgeyBTYWZlIH0gZnJvbSAnLi9zYWZlL2luZGV4LmpzJztcbmltcG9ydCB7IFdhbGxldCB9IGZyb20gJy4vd2FsbGV0L2luZGV4LmpzJztcbmNsYXNzIFNhZmVBcHBzU0RLIHtcbiAgICBjb25zdHJ1Y3RvcihvcHRzID0ge30pIHtcbiAgICAgICAgY29uc3QgeyBhbGxvd2VkRG9tYWlucyA9IG51bGwsIGRlYnVnID0gZmFsc2UgfSA9IG9wdHM7XG4gICAgICAgIHRoaXMuY29tbXVuaWNhdG9yID0gbmV3IEludGVyZmFjZUNvbW11bmljYXRvcihhbGxvd2VkRG9tYWlucywgZGVidWcpO1xuICAgICAgICB0aGlzLmV0aCA9IG5ldyBFdGgodGhpcy5jb21tdW5pY2F0b3IpO1xuICAgICAgICB0aGlzLnR4cyA9IG5ldyBUWHModGhpcy5jb21tdW5pY2F0b3IpO1xuICAgICAgICB0aGlzLnNhZmUgPSBuZXcgU2FmZSh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgICAgIHRoaXMud2FsbGV0ID0gbmV3IFdhbGxldCh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgU2FmZUFwcHNTREs7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZGsuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TXs: () => (/* binding */ TXs)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n\n\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0,_types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransferDirection)\n/* harmony export */ });\n/* harmony import */ var _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\n\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvZ2F0ZXdheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF1SDtBQUN2SCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcdHlwZXNcXGdhdGV3YXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgT3BlcmF0aW9uLCBUb2tlblR5cGUsIFRyYW5zYWN0aW9uU3RhdHVzLCBUcmFuc2ZlckRpcmVjdGlvbiwgfSBmcm9tICdAc2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdhdGV3YXkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransferDirection),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _sdk_js__WEBPACK_IMPORTED_MODULE_0__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\");\n/* harmony import */ var _rpc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rpc.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\");\n/* harmony import */ var _gateway_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gateway.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\");\n/* harmony import */ var _messaging_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messaging.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXlCO0FBQ0E7QUFDSTtBQUNFO0FBQy9CIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx0eXBlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9zZGsuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9ycGMuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9nYXRld2F5LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vbWVzc2FnaW5nLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvbWVzc2FnaW5nLmpzIiwibWFwcGluZ3MiOiI7O0FBQXNEO0FBQ3REIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx0eXBlc1xcbWVzc2FnaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1ldGhvZHMgfSBmcm9tICcuLi9jb21tdW5pY2F0aW9uL21ldGhvZHMuanMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVzc2FnaW5nLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS_REQUEST_REJECTED: () => (/* binding */ PERMISSIONS_REQUEST_REJECTED),\n/* harmony export */   PermissionsError: () => (/* binding */ PermissionsError)\n/* harmony export */ });\nconst PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvcGVybWlzc2lvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHR5cGVzXFxwZXJtaXNzaW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUEVSTUlTU0lPTlNfUkVRVUVTVF9SRUpFQ1RFRCA9IDQwMDE7XG5leHBvcnQgY2xhc3MgUGVybWlzc2lvbnNFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBjb2RlLCBkYXRhKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICAgICAgICB0aGlzLmRhdGEgPSBkYXRhO1xuICAgICAgICAvLyBTaG91bGQgYWRqdXN0IHByb3RvdHlwZSBtYW51YWxseSBiZWNhdXNlIGhvdyBUUyBoYW5kbGVzIHRoZSB0eXBlIGV4dGVuc2lvbiBjb21waWxhdGlvblxuICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vTWljcm9zb2Z0L1R5cGVTY3JpcHQvd2lraS9CcmVha2luZy1DaGFuZ2VzI2V4dGVuZGluZy1idWlsdC1pbnMtbGlrZS1lcnJvci1hcnJheS1hbmQtbWFwLW1heS1uby1sb25nZXItd29ya1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgUGVybWlzc2lvbnNFcnJvci5wcm90b3R5cGUpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBlcm1pc3Npb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvcnBjLmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx0eXBlc1xccnBjLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJwYy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectEIP712TypedData: () => (/* binding */ isObjectEIP712TypedData)\n/* harmony export */ });\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcdHlwZXNcXHNkay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgaXNPYmplY3RFSVA3MTJUeXBlZERhdGEgPSAob2JqKSA9PiB7XG4gICAgcmV0dXJuIHR5cGVvZiBvYmogPT09ICdvYmplY3QnICYmIG9iaiAhPSBudWxsICYmICdkb21haW4nIGluIG9iaiAmJiAndHlwZXMnIGluIG9iaiAmJiAnbWVzc2FnZScgaW4gb2JqO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNkay5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSDKVersion: () => (/* binding */ getSDKVersion)\n/* harmony export */ });\nconst getSDKVersion = () => '9.1.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZ2V0U0RLVmVyc2lvbiA9ICgpID0+ICc5LjEuMCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Wallet: () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions request is invalid', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_BASE_URL = void 0;\nexports.DEFAULT_BASE_URL = 'https://safe-client.safe.global';\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0I7QUFDeEIsd0JBQXdCO0FBQ3hCIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFxjb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkRFRkFVTFRfQkFTRV9VUkwgPSB2b2lkIDA7XG5leHBvcnRzLkRFRkFVTFRfQkFTRV9VUkwgPSAnaHR0cHM6Ly9zYWZlLWNsaWVudC5zYWZlLmdsb2JhbCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25maWcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.postEndpoint = postEndpoint;\nexports.putEndpoint = putEndpoint;\nexports.deleteEndpoint = deleteEndpoint;\nexports.getEndpoint = getEndpoint;\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\");\nfunction makeUrl(baseUrl, path, pathParams, query) {\n    const pathname = (0, utils_1.insertParams)(path, pathParams);\n    const search = (0, utils_1.stringifyQuery)(query);\n    return `${baseUrl}${pathname}${search}`;\n}\nfunction postEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'POST', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction putEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'PUT', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction deleteEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'DELETE', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction getEndpoint(baseUrl, path, params, rawUrl) {\n    if (rawUrl) {\n        return (0, utils_1.getData)(rawUrl, undefined, params === null || params === void 0 ? void 0 : params.credentials);\n    }\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.getData)(url, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\n//# sourceMappingURL=endpoint.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setBaseUrl = void 0;\nexports.relayTransaction = relayTransaction;\nexports.getRelayCount = getRelayCount;\nexports.getSafeInfo = getSafeInfo;\nexports.getIncomingTransfers = getIncomingTransfers;\nexports.getModuleTransactions = getModuleTransactions;\nexports.getMultisigTransactions = getMultisigTransactions;\nexports.getBalances = getBalances;\nexports.getFiatCurrencies = getFiatCurrencies;\nexports.getOwnedSafes = getOwnedSafes;\nexports.getAllOwnedSafes = getAllOwnedSafes;\nexports.getCollectibles = getCollectibles;\nexports.getCollectiblesPage = getCollectiblesPage;\nexports.getTransactionHistory = getTransactionHistory;\nexports.getTransactionQueue = getTransactionQueue;\nexports.getTransactionDetails = getTransactionDetails;\nexports.deleteTransaction = deleteTransaction;\nexports.postSafeGasEstimation = postSafeGasEstimation;\nexports.getNonces = getNonces;\nexports.proposeTransaction = proposeTransaction;\nexports.getConfirmationView = getConfirmationView;\nexports.getTxPreview = getTxPreview;\nexports.getChainsConfig = getChainsConfig;\nexports.getChainConfig = getChainConfig;\nexports.getSafeApps = getSafeApps;\nexports.getMasterCopies = getMasterCopies;\nexports.getDecodedData = getDecodedData;\nexports.getSafeMessages = getSafeMessages;\nexports.getSafeMessage = getSafeMessage;\nexports.proposeSafeMessage = proposeSafeMessage;\nexports.confirmSafeMessage = confirmSafeMessage;\nexports.getDelegates = getDelegates;\nexports.registerDevice = registerDevice;\nexports.unregisterSafe = unregisterSafe;\nexports.unregisterDevice = unregisterDevice;\nexports.registerEmail = registerEmail;\nexports.changeEmail = changeEmail;\nexports.resendEmailVerificationCode = resendEmailVerificationCode;\nexports.verifyEmail = verifyEmail;\nexports.getRegisteredEmail = getRegisteredEmail;\nexports.deleteRegisteredEmail = deleteRegisteredEmail;\nexports.registerRecoveryModule = registerRecoveryModule;\nexports.unsubscribeSingle = unsubscribeSingle;\nexports.unsubscribeAll = unsubscribeAll;\nexports.getSafeOverviews = getSafeOverviews;\nexports.getContract = getContract;\nexports.getAuthNonce = getAuthNonce;\nexports.verifyAuth = verifyAuth;\nexports.createAccount = createAccount;\nexports.getAccount = getAccount;\nexports.deleteAccount = deleteAccount;\nexports.getAccountDataTypes = getAccountDataTypes;\nexports.getAccountDataSettings = getAccountDataSettings;\nexports.putAccountDataSettings = putAccountDataSettings;\nexports.getIndexingStatus = getIndexingStatus;\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\");\nconst config_1 = __webpack_require__(/*! ./config */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\");\n__exportStar(__webpack_require__(/*! ./types/safe-info */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-apps */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/transactions */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/chains */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/common */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/master-copies */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/decoded-data */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-messages */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/notifications */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/relay */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\"), exports);\n// Can be set externally to a different CGW host\nlet baseUrl = config_1.DEFAULT_BASE_URL;\n/**\n * Set the base CGW URL\n */\nconst setBaseUrl = (url) => {\n    baseUrl = url;\n};\nexports.setBaseUrl = setBaseUrl;\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Relay a transaction from a Safe\n */\nfunction relayTransaction(chainId, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/relay', { path: { chainId }, body });\n}\n/**\n * Get the relay limit and number of remaining relays remaining\n */\nfunction getRelayCount(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/relay/{address}', { path: { chainId, address } });\n}\n/**\n * Get basic information about a Safe. E.g. owners, modules, version etc\n */\nfunction getSafeInfo(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}', { path: { chainId, address } });\n}\n/**\n * Get filterable list of incoming transactions\n */\nfunction getIncomingTransfers(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/incoming-transfers/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of module transactions\n */\nfunction getModuleTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/module-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of multisig transactions\n */\nfunction getMultisigTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/multisig-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get the total balance and all assets stored in a Safe\n */\nfunction getBalances(chainId, address, currency = 'usd', query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/balances/{currency}', {\n        path: { chainId, address, currency },\n        query,\n    });\n}\n/**\n * Get a list of supported fiat currencies (e.g. USD, EUR etc)\n */\nfunction getFiatCurrencies() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/balances/supported-fiat-codes');\n}\n/**\n * Get the addresses of all Safes belonging to an owner\n */\nfunction getOwnedSafes(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/owners/{address}/safes', { path: { chainId, address } });\n}\n/**\n * Get the addresses of all Safes belonging to an owner on all chains\n */\nfunction getAllOwnedSafes(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/owners/{address}/safes', { path: { address } });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectibles(chainId, address, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/collectibles', {\n        path: { chainId, address },\n        query,\n    });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectiblesPage(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{address}/collectibles', { path: { chainId, address }, query }, pageUrl);\n}\n/**\n * Get a list of past Safe transactions\n */\nfunction getTransactionHistory(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/history', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the list of pending transactions\n */\nfunction getTransactionQueue(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/queued', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the details of an individual transaction by its id\n */\nfunction getTransactionDetails(chainId, transactionId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{transactionId}', {\n        path: { chainId, transactionId },\n    });\n}\n/**\n * Delete a transaction by its safeTxHash\n */\nfunction deleteTransaction(chainId, safeTxHash, signature) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safeTxHash}', {\n        path: { chainId, safeTxHash },\n        body: { signature },\n    });\n}\n/**\n * Request a gas estimate & recommmended tx nonce for a created transaction\n */\nfunction postSafeGasEstimation(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{safe_address}/multisig-transactions/estimations', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nfunction getNonces(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/nonces', {\n        path: { chainId, safe_address: address },\n    });\n}\n/**\n * Propose a new transaction for other owners to sign/execute\n */\nfunction proposeTransaction(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/propose', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getConfirmationView(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/views/transaction-confirmation', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Get a tx preview\n */\nfunction getTxPreview(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/preview', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Returns all defined chain configs\n */\nfunction getChainsConfig(query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains', {\n        query,\n    });\n}\n/**\n * Returns a chain config\n */\nfunction getChainConfig(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns Safe Apps List\n */\nfunction getSafeApps(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safe-apps', {\n        path: { chainId: chainId },\n        query,\n    });\n}\n/**\n * Returns list of Master Copies\n */\nfunction getMasterCopies(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/master-copies', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getDecodedData(chainId, operation, encodedData, to) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/data-decoder', {\n        path: { chainId: chainId },\n        body: { operation, data: encodedData, to },\n    });\n}\n/**\n * Returns list of `SafeMessage`s\n */\nfunction getSafeMessages(chainId, address, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', { path: { chainId, safe_address: address }, query: {} }, pageUrl);\n}\n/**\n * Returns a `SafeMessage`\n */\nfunction getSafeMessage(chainId, messageHash) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}', {\n        path: { chainId, message_hash: messageHash },\n    });\n}\n/**\n * Propose a new `SafeMessage` for other owners to sign\n */\nfunction proposeSafeMessage(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Add a confirmation to a `SafeMessage`\n */\nfunction confirmSafeMessage(chainId, messageHash, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}/signatures', {\n        path: { chainId, message_hash: messageHash },\n        body,\n    });\n}\n/**\n * Returns a list of delegates\n */\nfunction getDelegates(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/delegates', {\n        path: { chainId },\n        query,\n    });\n}\n/**\n * Registers a device/Safe for notifications\n */\nfunction registerDevice(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/register/notifications', {\n        body,\n    });\n}\n/**\n * Unregisters a Safe from notifications\n */\nfunction unregisterSafe(chainId, address, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}/safes/{safe_address}', {\n        path: { chainId, safe_address: address, uuid },\n    });\n}\n/**\n * Unregisters a device from notifications\n */\nfunction unregisterDevice(chainId, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}', {\n        path: { chainId, uuid },\n    });\n}\n/**\n * Registers a email address for a safe signer.\n *\n * The signer wallet has to sign a message of format: `email-register-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param body Signer address and email address\n * @param headers Signature and Signature timestamp\n * @returns 200 if signature matches the data\n */\nfunction registerEmail(chainId, safeAddress, body, headers) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Changes an already registered email address for a safe signer. The new email address still needs to be verified.\n *\n * The signer wallet has to sign a message of format: `email-edit-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param body New email address\n * @param headers Signature and Signature timestamp\n * @returns 202 if signature matches the data\n */\nfunction changeEmail(chainId, safeAddress, signerAddress, body, headers) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Resends an email verification code.\n */\nfunction resendEmailVerificationCode(chainId, safeAddress, signerAddress) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify-resend', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body: '',\n    });\n}\n/**\n * Verifies a pending email address registration.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address who signed the email registration\n * @param body Verification code\n */\nfunction verifyEmail(chainId, safeAddress, signerAddress, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n    });\n}\n/**\n * Gets the registered email address of the signer\n *\n * The signer wallet will have to sign a message of format: `email-retrieval-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address of the owner of the Safe\n *\n * @returns email address and verified flag\n */\nfunction getRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Delete a registered email address for the signer\n *\n * The signer wallet will have to sign a message of format: `email-delete-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param headers\n */\nfunction deleteRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Register a recovery module for receiving alerts\n * @param chainId\n * @param safeAddress\n * @param body - { moduleAddress: string }\n */\nfunction registerRecoveryModule(chainId, safeAddress, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/recovery', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n    });\n}\n/**\n * Delete email subscription for a single category\n * @param query\n */\nfunction unsubscribeSingle(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions', { query });\n}\n/**\n * Delete email subscription for all categories\n * @param query\n */\nfunction unsubscribeAll(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions/all', { query });\n}\n/**\n * Get Safe overviews per address\n */\nfunction getSafeOverviews(safes, query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/safes', {\n        query: Object.assign(Object.assign({}, query), { safes: safes.join(',') }),\n    });\n}\nfunction getContract(chainId, contractAddress) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/contracts/{contractAddress}', {\n        path: {\n            chainId: chainId,\n            contractAddress: contractAddress,\n        },\n    });\n}\nfunction getAuthNonce() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/auth/nonce', { credentials: 'include' });\n}\nfunction verifyAuth(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/auth/verify', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction createAccount(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/accounts', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction getAccount(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction deleteAccount(address) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction getAccountDataTypes() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/data-types');\n}\nfunction getAccountDataSettings(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction putAccountDataSettings(address, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        body,\n        credentials: 'include',\n    });\n}\nfunction getIndexingStatus(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/indexing', {\n        path: { chainId },\n    });\n}\n/* eslint-enable @typescript-eslint/explicit-module-boundary-types */\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FEATURES = exports.GAS_PRICE_TYPE = exports.RPC_AUTHENTICATION = void 0;\nvar RPC_AUTHENTICATION;\n(function (RPC_AUTHENTICATION) {\n    RPC_AUTHENTICATION[\"API_KEY_PATH\"] = \"API_KEY_PATH\";\n    RPC_AUTHENTICATION[\"NO_AUTHENTICATION\"] = \"NO_AUTHENTICATION\";\n    RPC_AUTHENTICATION[\"UNKNOWN\"] = \"UNKNOWN\";\n})(RPC_AUTHENTICATION || (exports.RPC_AUTHENTICATION = RPC_AUTHENTICATION = {}));\nvar GAS_PRICE_TYPE;\n(function (GAS_PRICE_TYPE) {\n    GAS_PRICE_TYPE[\"ORACLE\"] = \"ORACLE\";\n    GAS_PRICE_TYPE[\"FIXED\"] = \"FIXED\";\n    GAS_PRICE_TYPE[\"FIXED_1559\"] = \"FIXED1559\";\n    GAS_PRICE_TYPE[\"UNKNOWN\"] = \"UNKNOWN\";\n})(GAS_PRICE_TYPE || (exports.GAS_PRICE_TYPE = GAS_PRICE_TYPE = {}));\nvar FEATURES;\n(function (FEATURES) {\n    FEATURES[\"ERC721\"] = \"ERC721\";\n    FEATURES[\"SAFE_APPS\"] = \"SAFE_APPS\";\n    FEATURES[\"CONTRACT_INTERACTION\"] = \"CONTRACT_INTERACTION\";\n    FEATURES[\"DOMAIN_LOOKUP\"] = \"DOMAIN_LOOKUP\";\n    FEATURES[\"SPENDING_LIMIT\"] = \"SPENDING_LIMIT\";\n    FEATURES[\"EIP1559\"] = \"EIP1559\";\n    FEATURES[\"SAFE_TX_GAS_OPTIONAL\"] = \"SAFE_TX_GAS_OPTIONAL\";\n    FEATURES[\"TX_SIMULATION\"] = \"TX_SIMULATION\";\n    FEATURES[\"EIP1271\"] = \"EIP1271\";\n})(FEATURES || (exports.FEATURES = FEATURES = {}));\n//# sourceMappingURL=chains.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TokenType = void 0;\nvar TokenType;\n(function (TokenType) {\n    TokenType[\"ERC20\"] = \"ERC20\";\n    TokenType[\"ERC721\"] = \"ERC721\";\n    TokenType[\"NATIVE_TOKEN\"] = \"NATIVE_TOKEN\";\n    TokenType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TokenType || (exports.TokenType = TokenType = {}));\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQkFBZ0IsaUJBQWlCLGlCQUFpQjtBQUNuRCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcdHlwZXNcXGNvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuVG9rZW5UeXBlID0gdm9pZCAwO1xudmFyIFRva2VuVHlwZTtcbihmdW5jdGlvbiAoVG9rZW5UeXBlKSB7XG4gICAgVG9rZW5UeXBlW1wiRVJDMjBcIl0gPSBcIkVSQzIwXCI7XG4gICAgVG9rZW5UeXBlW1wiRVJDNzIxXCJdID0gXCJFUkM3MjFcIjtcbiAgICBUb2tlblR5cGVbXCJOQVRJVkVfVE9LRU5cIl0gPSBcIk5BVElWRV9UT0tFTlwiO1xuICAgIFRva2VuVHlwZVtcIlVOS05PV05cIl0gPSBcIlVOS05PV05cIjtcbn0pKFRva2VuVHlwZSB8fCAoZXhwb3J0cy5Ub2tlblR5cGUgPSBUb2tlblR5cGUgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tbW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NativeStakingStatus = exports.ConfirmationViewTypes = void 0;\nvar ConfirmationViewTypes;\n(function (ConfirmationViewTypes) {\n    ConfirmationViewTypes[\"GENERIC\"] = \"GENERIC\";\n    ConfirmationViewTypes[\"COW_SWAP_ORDER\"] = \"COW_SWAP_ORDER\";\n    ConfirmationViewTypes[\"COW_SWAP_TWAP_ORDER\"] = \"COW_SWAP_TWAP_ORDER\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_DEPOSIT\"] = \"KILN_NATIVE_STAKING_DEPOSIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_VALIDATORS_EXIT\"] = \"KILN_NATIVE_STAKING_VALIDATORS_EXIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_WITHDRAW\"] = \"KILN_NATIVE_STAKING_WITHDRAW\";\n})(ConfirmationViewTypes || (exports.ConfirmationViewTypes = ConfirmationViewTypes = {}));\nvar NativeStakingStatus;\n(function (NativeStakingStatus) {\n    NativeStakingStatus[\"NOT_STAKED\"] = \"NOT_STAKED\";\n    NativeStakingStatus[\"ACTIVATING\"] = \"ACTIVATING\";\n    NativeStakingStatus[\"DEPOSIT_IN_PROGRESS\"] = \"DEPOSIT_IN_PROGRESS\";\n    NativeStakingStatus[\"ACTIVE\"] = \"ACTIVE\";\n    NativeStakingStatus[\"EXIT_REQUESTED\"] = \"EXIT_REQUESTED\";\n    NativeStakingStatus[\"EXITING\"] = \"EXITING\";\n    NativeStakingStatus[\"EXITED\"] = \"EXITED\";\n    NativeStakingStatus[\"SLASHED\"] = \"SLASHED\";\n})(NativeStakingStatus || (exports.NativeStakingStatus = NativeStakingStatus = {}));\n//# sourceMappingURL=decoded-data.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=master-copies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL21hc3Rlci1jb3BpZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcXGRpc3RcXHR5cGVzXFxtYXN0ZXItY29waWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWFzdGVyLWNvcGllcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeviceType = void 0;\nvar DeviceType;\n(function (DeviceType) {\n    DeviceType[\"ANDROID\"] = \"ANDROID\";\n    DeviceType[\"IOS\"] = \"IOS\";\n    DeviceType[\"WEB\"] = \"WEB\";\n})(DeviceType || (exports.DeviceType = DeviceType = {}));\n//# sourceMappingURL=notifications.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL25vdGlmaWNhdGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGlCQUFpQixrQkFBa0Isa0JBQWtCO0FBQ3REIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFx0eXBlc1xcbm90aWZpY2F0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuRGV2aWNlVHlwZSA9IHZvaWQgMDtcbnZhciBEZXZpY2VUeXBlO1xuKGZ1bmN0aW9uIChEZXZpY2VUeXBlKSB7XG4gICAgRGV2aWNlVHlwZVtcIkFORFJPSURcIl0gPSBcIkFORFJPSURcIjtcbiAgICBEZXZpY2VUeXBlW1wiSU9TXCJdID0gXCJJT1NcIjtcbiAgICBEZXZpY2VUeXBlW1wiV0VCXCJdID0gXCJXRUJcIjtcbn0pKERldmljZVR5cGUgfHwgKGV4cG9ydHMuRGV2aWNlVHlwZSA9IERldmljZVR5cGUgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bm90aWZpY2F0aW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=relay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3JlbGF5LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFx0eXBlc1xccmVsYXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWxheS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppSocialPlatforms = exports.SafeAppFeatures = exports.SafeAppAccessPolicyTypes = void 0;\nvar SafeAppAccessPolicyTypes;\n(function (SafeAppAccessPolicyTypes) {\n    SafeAppAccessPolicyTypes[\"NoRestrictions\"] = \"NO_RESTRICTIONS\";\n    SafeAppAccessPolicyTypes[\"DomainAllowlist\"] = \"DOMAIN_ALLOWLIST\";\n})(SafeAppAccessPolicyTypes || (exports.SafeAppAccessPolicyTypes = SafeAppAccessPolicyTypes = {}));\nvar SafeAppFeatures;\n(function (SafeAppFeatures) {\n    SafeAppFeatures[\"BATCHED_TRANSACTIONS\"] = \"BATCHED_TRANSACTIONS\";\n})(SafeAppFeatures || (exports.SafeAppFeatures = SafeAppFeatures = {}));\nvar SafeAppSocialPlatforms;\n(function (SafeAppSocialPlatforms) {\n    SafeAppSocialPlatforms[\"TWITTER\"] = \"TWITTER\";\n    SafeAppSocialPlatforms[\"GITHUB\"] = \"GITHUB\";\n    SafeAppSocialPlatforms[\"DISCORD\"] = \"DISCORD\";\n    SafeAppSocialPlatforms[\"TELEGRAM\"] = \"TELEGRAM\";\n})(SafeAppSocialPlatforms || (exports.SafeAppSocialPlatforms = SafeAppSocialPlatforms = {}));\n//# sourceMappingURL=safe-apps.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ImplementationVersionState = void 0;\nvar ImplementationVersionState;\n(function (ImplementationVersionState) {\n    ImplementationVersionState[\"UP_TO_DATE\"] = \"UP_TO_DATE\";\n    ImplementationVersionState[\"OUTDATED\"] = \"OUTDATED\";\n    ImplementationVersionState[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ImplementationVersionState || (exports.ImplementationVersionState = ImplementationVersionState = {}));\n//# sourceMappingURL=safe-info.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtaW5mby5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsaUNBQWlDLGtDQUFrQyxrQ0FBa0M7QUFDdEciLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcXGRpc3RcXHR5cGVzXFxzYWZlLWluZm8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlID0gdm9pZCAwO1xudmFyIEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlO1xuKGZ1bmN0aW9uIChJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSkge1xuICAgIEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlW1wiVVBfVE9fREFURVwiXSA9IFwiVVBfVE9fREFURVwiO1xuICAgIEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlW1wiT1VUREFURURcIl0gPSBcIk9VVERBVEVEXCI7XG4gICAgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGVbXCJVTktOT1dOXCJdID0gXCJVTktOT1dOXCI7XG59KShJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSB8fCAoZXhwb3J0cy5JbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSA9IEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNhZmUtaW5mby5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeMessageStatus = exports.SafeMessageListItemType = void 0;\nvar SafeMessageListItemType;\n(function (SafeMessageListItemType) {\n    SafeMessageListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n    SafeMessageListItemType[\"MESSAGE\"] = \"MESSAGE\";\n})(SafeMessageListItemType || (exports.SafeMessageListItemType = SafeMessageListItemType = {}));\nvar SafeMessageStatus;\n(function (SafeMessageStatus) {\n    SafeMessageStatus[\"NEEDS_CONFIRMATION\"] = \"NEEDS_CONFIRMATION\";\n    SafeMessageStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n})(SafeMessageStatus || (exports.SafeMessageStatus = SafeMessageStatus = {}));\n//# sourceMappingURL=safe-messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtbWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QseUJBQXlCLEdBQUcsK0JBQStCO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4QkFBOEIsK0JBQStCLCtCQUErQjtBQUM3RjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsd0JBQXdCLHlCQUF5Qix5QkFBeUI7QUFDM0UiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcXGRpc3RcXHR5cGVzXFxzYWZlLW1lc3NhZ2VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5TYWZlTWVzc2FnZVN0YXR1cyA9IGV4cG9ydHMuU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUgPSB2b2lkIDA7XG52YXIgU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGU7XG4oZnVuY3Rpb24gKFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlKSB7XG4gICAgU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGVbXCJEQVRFX0xBQkVMXCJdID0gXCJEQVRFX0xBQkVMXCI7XG4gICAgU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGVbXCJNRVNTQUdFXCJdID0gXCJNRVNTQUdFXCI7XG59KShTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSB8fCAoZXhwb3J0cy5TYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSA9IFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlID0ge30pKTtcbnZhciBTYWZlTWVzc2FnZVN0YXR1cztcbihmdW5jdGlvbiAoU2FmZU1lc3NhZ2VTdGF0dXMpIHtcbiAgICBTYWZlTWVzc2FnZVN0YXR1c1tcIk5FRURTX0NPTkZJUk1BVElPTlwiXSA9IFwiTkVFRFNfQ09ORklSTUFUSU9OXCI7XG4gICAgU2FmZU1lc3NhZ2VTdGF0dXNbXCJDT05GSVJNRURcIl0gPSBcIkNPTkZJUk1FRFwiO1xufSkoU2FmZU1lc3NhZ2VTdGF0dXMgfHwgKGV4cG9ydHMuU2FmZU1lc3NhZ2VTdGF0dXMgPSBTYWZlTWVzc2FnZVN0YXR1cyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zYWZlLW1lc3NhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LabelValue = exports.StartTimeValue = exports.DurationType = exports.DetailedExecutionInfoType = exports.TransactionListItemType = exports.ConflictType = exports.TransactionInfoType = exports.SettingsInfoType = exports.TransactionTokenType = exports.TransferDirection = exports.TransactionStatus = exports.Operation = void 0;\nvar Operation;\n(function (Operation) {\n    Operation[Operation[\"CALL\"] = 0] = \"CALL\";\n    Operation[Operation[\"DELEGATE\"] = 1] = \"DELEGATE\";\n})(Operation || (exports.Operation = Operation = {}));\nvar TransactionStatus;\n(function (TransactionStatus) {\n    TransactionStatus[\"AWAITING_CONFIRMATIONS\"] = \"AWAITING_CONFIRMATIONS\";\n    TransactionStatus[\"AWAITING_EXECUTION\"] = \"AWAITING_EXECUTION\";\n    TransactionStatus[\"CANCELLED\"] = \"CANCELLED\";\n    TransactionStatus[\"FAILED\"] = \"FAILED\";\n    TransactionStatus[\"SUCCESS\"] = \"SUCCESS\";\n})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));\nvar TransferDirection;\n(function (TransferDirection) {\n    TransferDirection[\"INCOMING\"] = \"INCOMING\";\n    TransferDirection[\"OUTGOING\"] = \"OUTGOING\";\n    TransferDirection[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TransferDirection || (exports.TransferDirection = TransferDirection = {}));\nvar TransactionTokenType;\n(function (TransactionTokenType) {\n    TransactionTokenType[\"ERC20\"] = \"ERC20\";\n    TransactionTokenType[\"ERC721\"] = \"ERC721\";\n    TransactionTokenType[\"NATIVE_COIN\"] = \"NATIVE_COIN\";\n})(TransactionTokenType || (exports.TransactionTokenType = TransactionTokenType = {}));\nvar SettingsInfoType;\n(function (SettingsInfoType) {\n    SettingsInfoType[\"SET_FALLBACK_HANDLER\"] = \"SET_FALLBACK_HANDLER\";\n    SettingsInfoType[\"ADD_OWNER\"] = \"ADD_OWNER\";\n    SettingsInfoType[\"REMOVE_OWNER\"] = \"REMOVE_OWNER\";\n    SettingsInfoType[\"SWAP_OWNER\"] = \"SWAP_OWNER\";\n    SettingsInfoType[\"CHANGE_THRESHOLD\"] = \"CHANGE_THRESHOLD\";\n    SettingsInfoType[\"CHANGE_IMPLEMENTATION\"] = \"CHANGE_IMPLEMENTATION\";\n    SettingsInfoType[\"ENABLE_MODULE\"] = \"ENABLE_MODULE\";\n    SettingsInfoType[\"DISABLE_MODULE\"] = \"DISABLE_MODULE\";\n    SettingsInfoType[\"SET_GUARD\"] = \"SET_GUARD\";\n    SettingsInfoType[\"DELETE_GUARD\"] = \"DELETE_GUARD\";\n})(SettingsInfoType || (exports.SettingsInfoType = SettingsInfoType = {}));\nvar TransactionInfoType;\n(function (TransactionInfoType) {\n    TransactionInfoType[\"TRANSFER\"] = \"Transfer\";\n    TransactionInfoType[\"SETTINGS_CHANGE\"] = \"SettingsChange\";\n    TransactionInfoType[\"CUSTOM\"] = \"Custom\";\n    TransactionInfoType[\"CREATION\"] = \"Creation\";\n    TransactionInfoType[\"SWAP_ORDER\"] = \"SwapOrder\";\n    TransactionInfoType[\"TWAP_ORDER\"] = \"TwapOrder\";\n    TransactionInfoType[\"SWAP_TRANSFER\"] = \"SwapTransfer\";\n    TransactionInfoType[\"NATIVE_STAKING_DEPOSIT\"] = \"NativeStakingDeposit\";\n    TransactionInfoType[\"NATIVE_STAKING_VALIDATORS_EXIT\"] = \"NativeStakingValidatorsExit\";\n    TransactionInfoType[\"NATIVE_STAKING_WITHDRAW\"] = \"NativeStakingWithdraw\";\n})(TransactionInfoType || (exports.TransactionInfoType = TransactionInfoType = {}));\nvar ConflictType;\n(function (ConflictType) {\n    ConflictType[\"NONE\"] = \"None\";\n    ConflictType[\"HAS_NEXT\"] = \"HasNext\";\n    ConflictType[\"END\"] = \"End\";\n})(ConflictType || (exports.ConflictType = ConflictType = {}));\nvar TransactionListItemType;\n(function (TransactionListItemType) {\n    TransactionListItemType[\"TRANSACTION\"] = \"TRANSACTION\";\n    TransactionListItemType[\"LABEL\"] = \"LABEL\";\n    TransactionListItemType[\"CONFLICT_HEADER\"] = \"CONFLICT_HEADER\";\n    TransactionListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n})(TransactionListItemType || (exports.TransactionListItemType = TransactionListItemType = {}));\nvar DetailedExecutionInfoType;\n(function (DetailedExecutionInfoType) {\n    DetailedExecutionInfoType[\"MULTISIG\"] = \"MULTISIG\";\n    DetailedExecutionInfoType[\"MODULE\"] = \"MODULE\";\n})(DetailedExecutionInfoType || (exports.DetailedExecutionInfoType = DetailedExecutionInfoType = {}));\nvar DurationType;\n(function (DurationType) {\n    DurationType[\"AUTO\"] = \"AUTO\";\n    DurationType[\"LIMIT_DURATION\"] = \"LIMIT_DURATION\";\n})(DurationType || (exports.DurationType = DurationType = {}));\nvar StartTimeValue;\n(function (StartTimeValue) {\n    StartTimeValue[\"AT_MINING_TIME\"] = \"AT_MINING_TIME\";\n    StartTimeValue[\"AT_EPOCH\"] = \"AT_EPOCH\";\n})(StartTimeValue || (exports.StartTimeValue = StartTimeValue = {}));\nvar LabelValue;\n(function (LabelValue) {\n    LabelValue[\"Queued\"] = \"Queued\";\n    LabelValue[\"Next\"] = \"Next\";\n})(LabelValue || (exports.LabelValue = LabelValue = {}));\n//# sourceMappingURL=transactions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.insertParams = insertParams;\nexports.stringifyQuery = stringifyQuery;\nexports.fetchData = fetchData;\nexports.getData = getData;\nconst isErrorResponse = (data) => {\n    const isObject = typeof data === 'object' && data !== null;\n    return isObject && ('code' in data || 'statusCode' in data) && 'message' in data;\n};\nfunction replaceParam(str, key, value) {\n    return str.replace(new RegExp(`\\\\{${key}\\\\}`, 'g'), value);\n}\nfunction insertParams(template, params) {\n    return params\n        ? Object.keys(params).reduce((result, key) => {\n            return replaceParam(result, key, String(params[key]));\n        }, template)\n        : template;\n}\nfunction stringifyQuery(query) {\n    if (!query) {\n        return '';\n    }\n    const searchParams = new URLSearchParams();\n    Object.keys(query).forEach((key) => {\n        if (query[key] != null) {\n            searchParams.append(key, String(query[key]));\n        }\n    });\n    const searchString = searchParams.toString();\n    return searchString ? `?${searchString}` : '';\n}\nfunction parseResponse(resp) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        let json;\n        try {\n            json = yield resp.json();\n        }\n        catch (_b) {\n            json = {};\n        }\n        if (!resp.ok) {\n            const errTxt = isErrorResponse(json)\n                ? `CGW error - ${(_a = json.code) !== null && _a !== void 0 ? _a : json.statusCode}: ${json.message}`\n                : `CGW error - status ${resp.statusText}`;\n            throw new Error(errTxt);\n        }\n        return json;\n    });\n}\nfunction fetchData(url, method, body, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const requestHeaders = Object.assign({ 'Content-Type': 'application/json' }, headers);\n        const options = {\n            method: method !== null && method !== void 0 ? method : 'POST',\n            headers: requestHeaders,\n        };\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        if (body != null) {\n            options.body = typeof body === 'string' ? body : JSON.stringify(body);\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\nfunction getData(url, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const options = {\n            method: 'GET',\n        };\n        if (headers) {\n            options['headers'] = Object.assign(Object.assign({}, headers), { 'Content-Type': 'application/json' });\n        }\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw0QkFBNEIsK0RBQStELGlCQUFpQjtBQUM1RztBQUNBLG9DQUFvQyxNQUFNLCtCQUErQixZQUFZO0FBQ3JGLG1DQUFtQyxNQUFNLG1DQUFtQyxZQUFZO0FBQ3hGLGdDQUFnQztBQUNoQztBQUNBLEtBQUs7QUFDTDtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0I7QUFDcEIsc0JBQXNCO0FBQ3RCLGlCQUFpQjtBQUNqQixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxFQUFFLElBQUksR0FBRztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSw4QkFBOEIsYUFBYTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxrRUFBa0UsSUFBSSxhQUFhO0FBQ3BILHdDQUF3QyxnQkFBZ0I7QUFDeEQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyxvQ0FBb0M7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStELGNBQWMsb0NBQW9DO0FBQ2pIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2F3YWl0ZXIgPSAodGhpcyAmJiB0aGlzLl9fYXdhaXRlcikgfHwgZnVuY3Rpb24gKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICAgIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gICAgfSk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5pbnNlcnRQYXJhbXMgPSBpbnNlcnRQYXJhbXM7XG5leHBvcnRzLnN0cmluZ2lmeVF1ZXJ5ID0gc3RyaW5naWZ5UXVlcnk7XG5leHBvcnRzLmZldGNoRGF0YSA9IGZldGNoRGF0YTtcbmV4cG9ydHMuZ2V0RGF0YSA9IGdldERhdGE7XG5jb25zdCBpc0Vycm9yUmVzcG9uc2UgPSAoZGF0YSkgPT4ge1xuICAgIGNvbnN0IGlzT2JqZWN0ID0gdHlwZW9mIGRhdGEgPT09ICdvYmplY3QnICYmIGRhdGEgIT09IG51bGw7XG4gICAgcmV0dXJuIGlzT2JqZWN0ICYmICgnY29kZScgaW4gZGF0YSB8fCAnc3RhdHVzQ29kZScgaW4gZGF0YSkgJiYgJ21lc3NhZ2UnIGluIGRhdGE7XG59O1xuZnVuY3Rpb24gcmVwbGFjZVBhcmFtKHN0ciwga2V5LCB2YWx1ZSkge1xuICAgIHJldHVybiBzdHIucmVwbGFjZShuZXcgUmVnRXhwKGBcXFxceyR7a2V5fVxcXFx9YCwgJ2cnKSwgdmFsdWUpO1xufVxuZnVuY3Rpb24gaW5zZXJ0UGFyYW1zKHRlbXBsYXRlLCBwYXJhbXMpIHtcbiAgICByZXR1cm4gcGFyYW1zXG4gICAgICAgID8gT2JqZWN0LmtleXMocGFyYW1zKS5yZWR1Y2UoKHJlc3VsdCwga2V5KSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gcmVwbGFjZVBhcmFtKHJlc3VsdCwga2V5LCBTdHJpbmcocGFyYW1zW2tleV0pKTtcbiAgICAgICAgfSwgdGVtcGxhdGUpXG4gICAgICAgIDogdGVtcGxhdGU7XG59XG5mdW5jdGlvbiBzdHJpbmdpZnlRdWVyeShxdWVyeSkge1xuICAgIGlmICghcXVlcnkpIHtcbiAgICAgICAgcmV0dXJuICcnO1xuICAgIH1cbiAgICBjb25zdCBzZWFyY2hQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgT2JqZWN0LmtleXMocXVlcnkpLmZvckVhY2goKGtleSkgPT4ge1xuICAgICAgICBpZiAocXVlcnlba2V5XSAhPSBudWxsKSB7XG4gICAgICAgICAgICBzZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgU3RyaW5nKHF1ZXJ5W2tleV0pKTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIGNvbnN0IHNlYXJjaFN0cmluZyA9IHNlYXJjaFBhcmFtcy50b1N0cmluZygpO1xuICAgIHJldHVybiBzZWFyY2hTdHJpbmcgPyBgPyR7c2VhcmNoU3RyaW5nfWAgOiAnJztcbn1cbmZ1bmN0aW9uIHBhcnNlUmVzcG9uc2UocmVzcCkge1xuICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgbGV0IGpzb247XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBqc29uID0geWllbGQgcmVzcC5qc29uKCk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKF9iKSB7XG4gICAgICAgICAgICBqc29uID0ge307XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFyZXNwLm9rKSB7XG4gICAgICAgICAgICBjb25zdCBlcnJUeHQgPSBpc0Vycm9yUmVzcG9uc2UoanNvbilcbiAgICAgICAgICAgICAgICA/IGBDR1cgZXJyb3IgLSAkeyhfYSA9IGpzb24uY29kZSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDoganNvbi5zdGF0dXNDb2RlfTogJHtqc29uLm1lc3NhZ2V9YFxuICAgICAgICAgICAgICAgIDogYENHVyBlcnJvciAtIHN0YXR1cyAke3Jlc3Auc3RhdHVzVGV4dH1gO1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVyclR4dCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGpzb247XG4gICAgfSk7XG59XG5mdW5jdGlvbiBmZXRjaERhdGEodXJsLCBtZXRob2QsIGJvZHksIGhlYWRlcnMsIGNyZWRlbnRpYWxzKSB7XG4gICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgY29uc3QgcmVxdWVzdEhlYWRlcnMgPSBPYmplY3QuYXNzaWduKHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LCBoZWFkZXJzKTtcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAgICAgICAgIG1ldGhvZDogbWV0aG9kICE9PSBudWxsICYmIG1ldGhvZCAhPT0gdm9pZCAwID8gbWV0aG9kIDogJ1BPU1QnLFxuICAgICAgICAgICAgaGVhZGVyczogcmVxdWVzdEhlYWRlcnMsXG4gICAgICAgIH07XG4gICAgICAgIGlmIChjcmVkZW50aWFscykge1xuICAgICAgICAgICAgb3B0aW9uc1snY3JlZGVudGlhbHMnXSA9IGNyZWRlbnRpYWxzO1xuICAgICAgICB9XG4gICAgICAgIGlmIChib2R5ICE9IG51bGwpIHtcbiAgICAgICAgICAgIG9wdGlvbnMuYm9keSA9IHR5cGVvZiBib2R5ID09PSAnc3RyaW5nJyA/IGJvZHkgOiBKU09OLnN0cmluZ2lmeShib2R5KTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXNwID0geWllbGQgZmV0Y2godXJsLCBvcHRpb25zKTtcbiAgICAgICAgcmV0dXJuIHBhcnNlUmVzcG9uc2UocmVzcCk7XG4gICAgfSk7XG59XG5mdW5jdGlvbiBnZXREYXRhKHVybCwgaGVhZGVycywgY3JlZGVudGlhbHMpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKGhlYWRlcnMpIHtcbiAgICAgICAgICAgIG9wdGlvbnNbJ2hlYWRlcnMnXSA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgaGVhZGVycyksIHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9KTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY3JlZGVudGlhbHMpIHtcbiAgICAgICAgICAgIG9wdGlvbnNbJ2NyZWRlbnRpYWxzJ10gPSBjcmVkZW50aWFscztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXNwID0geWllbGQgZmV0Y2godXJsLCBvcHRpb25zKTtcbiAgICAgICAgcmV0dXJuIHBhcnNlUmVzcG9uc2UocmVzcCk7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\n");

/***/ })

};
;