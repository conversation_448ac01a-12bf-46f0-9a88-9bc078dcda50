import { getDefaultConfig, getDefaultWallets } from '@rainbow-me/rainbowkit';
import { avalancheFuji } from 'wagmi/chains';
import {
  injectedWallet,
} from "@rainbow-me/rainbowkit/wallets";
import { http, createStorage, cookieStorage } from 'wagmi';
import { getAddress } from 'viem';

if (!process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID) {
  throw new Error('Missing NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID');
}

const { wallets } = getDefaultWallets();

export const config = getDefaultConfig({
  appName: 'Alligator',
  projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
  wallets: [
    {
      groupName: "Core Wallet",
      wallets: [injectedWallet],
    },
  ],
  chains: [
    avalancheFuji,
  ],
  transports: {
    [avalancheFuji.id]: http(process.env.NEXT_PUBLIC_AVALANCHE_FUJI_RPC || 'https://api.avax-test.network/ext/bc/C/rpc'),
  },
  ssr: true,
  storage: createStorage({
    storage: cookieStorage,
  }),
});

// Contract address - properly checksummed
const CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS || '******************************************';
export const LENDING_APY_AGGREGATOR_ADDRESS = getAddress(CONTRACT_ADDRESS) as `0x${string}`;

// Debug function to check contract
export const getContractInfo = () => {
  console.log('Contract Address:', LENDING_APY_AGGREGATOR_ADDRESS);
  console.log('Environment Variable:', process.env.NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS);
  return {
    address: LENDING_APY_AGGREGATOR_ADDRESS,
    envVar: process.env.NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS
  };
};

export const SUPPORTED_TOKENS = {
  // Avalanche Fuji Testnet addresses for testing
  fuji: {
    USDC: '******************************************',
    WAVAX: '******************************************',
    USDT: '******************************************',
  },
  // Avalanche Mainnet addresses (for future use)
  avalanche: {
    USDC: '******************************************',
    USDT: '******************************************',
    WETH: '******************************************',
    WBTC: '******************************************',
    WAVAX: '******************************************',
  },
  // Base addresses (for future Morpho integration)
  base: {
    USDC: '******************************************',
    WETH: '******************************************',
  }
};

// Current network configuration - change this to switch between testnet and mainnet
export const CURRENT_NETWORK = 'fuji'; // 'fuji' for testnet, 'avalanche' for mainnet

// Get tokens for current network
export const getCurrentTokens = () => SUPPORTED_TOKENS[CURRENT_NETWORK as keyof typeof SUPPORTED_TOKENS];