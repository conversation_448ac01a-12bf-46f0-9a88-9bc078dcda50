# Frontend Integration Guide for Aave-Only Lending Aggregator

## Prerequisites
- Contract deployed via Remix (see REMIX_DEPLOYMENT_GUIDE.md)
- Contract tested and working (see AAVE_TESTING_GUIDE.md)
- Node.js and npm installed

## Step 1: Environment Setup

### Create Environment File
Create `ap-yieldz/.env.local`:
```bash
# Your deployed contract address
NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS=0xYOUR_CONTRACT_ADDRESS_HERE

# WalletConnect Project ID (get from https://cloud.walletconnect.com/)
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id_here

# RPC URL (optional, defaults to public RPC)
NEXT_PUBLIC_AVALANCHE_FUJI_RPC=https://api.avax-test.network/ext/bc/C/rpc
```

### Update Contract Address
In `ap-yieldz/app/blockchain/config/wagmi.ts`, the contract address will be read from the environment variable above.

## Step 2: Update Component to Use Simple Aggregator

### Update the useAggregator Hook Import
In components that use the aggregator, replace the import:

```typescript
// OLD
import { useAggregatorOperations, useUserPosition } from '../blockchain/hooks/useAggregator';

// NEW
import { useSimpleAggregatorOperations, useUserPosition } from '../blockchain/hooks/useSimpleAggregator';
```

### Update Function Calls
The simplified aggregator has slightly different function signatures:

```typescript
// OLD
const { supplyToAave } = useAggregatorOperations();
await supplyToAave(assetAddress, amount);

// NEW
const { supplyToAave } = useSimpleAggregatorOperations();
await supplyToAave(assetAddress, amount, decimals); // Note: decimals parameter added
```

## Step 3: Test Frontend Integration

### Start the Development Server
```bash
cd ap-yieldz
npm install
npm run dev
```

### Test Basic Functionality

1. **Connect Wallet**:
   - Open http://localhost:3000
   - Connect MetaMask
   - Switch to Avalanche Fuji network

2. **Check Contract Connection**:
   - Verify the app loads without errors
   - Check browser console for any connection issues

3. **Test Token Display**:
   - Should show USDC and WAVAX (Fuji testnet tokens)
   - Verify token balances load correctly

4. **Test Supply Operation**:
   - Try supplying a small amount of USDC
   - Check that approval and supply transactions work
   - Verify position updates in the UI

## Step 4: Component Updates

### Update APY Data Hook
The APY data hook should work as-is, but you may want to focus on Aave data only:

```typescript
// In useAPYData.ts, you can simplify to focus on Aave only
const apyData = [
  {
    asset: 'usdc',
    symbol: 'USDC',
    aaveSupplyAPY: 3.25,
    aaveBorrowAPY: 4.15,
    morphoSupplyAPY: 0, // Set to 0 since Morpho is disabled
    morphoBorrowAPY: 0,
    bestSupplyProtocol: 'aave',
    bestBorrowProtocol: 'aave',
  },
  // ... other tokens
];
```

### Update Trading Modal
In `TradingModal.tsx`, disable Morpho options:

```typescript
// Hide or disable Morpho-related UI elements
const showMorphoOptions = false; // Set to false for Aave-only

// In the protocol selection:
{showMorphoOptions && (
  <option value="morpho">Morpho</option>
)}
```

### Update Dashboard
In `Dashboard.tsx`, focus on Aave positions:

```typescript
// Show only Aave positions
const position = useUserPosition(tokenAddress);
// position will have: aaveSupplied, aaveBorrowed, lastUpdate
```

## Step 5: Testing Checklist

### Basic Functionality
- [ ] App loads without errors
- [ ] Wallet connects successfully
- [ ] Contract address is correct
- [ ] Supported tokens display correctly

### Token Operations
- [ ] Token balances load correctly
- [ ] Token approval works
- [ ] Supply to Aave works
- [ ] Borrow from Aave works (with collateral)
- [ ] Withdraw from Aave works
- [ ] Repay to Aave works

### UI/UX
- [ ] Position updates reflect in real-time
- [ ] Transaction status shows correctly
- [ ] Error messages are helpful
- [ ] Loading states work properly

### Data Display
- [ ] APY data displays (even if mock)
- [ ] User positions show correctly
- [ ] Transaction history works
- [ ] Portfolio overview accurate

## Step 6: Common Issues and Solutions

### Issue: "Contract not found"
**Solution**: 
- Verify contract address in `.env.local`
- Check that you're on the correct network (Fuji)
- Ensure contract was deployed successfully

### Issue: "Function not found"
**Solution**:
- Verify you're using the correct ABI (`SimpleLendingAggregator`)
- Check function names match the contract
- Ensure contract is compiled with correct Solidity version

### Issue: "Transaction fails"
**Solution**:
- Check gas limits
- Verify token approvals
- Ensure sufficient balance
- Check if asset is supported in contract

### Issue: "APY data not loading"
**Solution**:
- Use fallback/mock data for testing
- Check API endpoints are accessible
- Verify network connectivity

## Step 7: Production Considerations

### Before Mainnet Deployment
1. **Thorough Testing**: Test all functions extensively on testnet
2. **Security Review**: Have contracts audited
3. **Gas Optimization**: Optimize for lower gas costs
4. **Error Handling**: Implement comprehensive error handling
5. **User Experience**: Polish UI/UX based on testnet feedback

### Mainnet Configuration
```typescript
// Update CURRENT_NETWORK in wagmi.ts
export const CURRENT_NETWORK = 'avalanche'; // Switch to mainnet

// Update contract address for mainnet deployment
NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS=0xMAINNET_CONTRACT_ADDRESS
```

## Step 8: Next Steps

Once Aave-only functionality is working perfectly:

1. **Add More Protocols**: Integrate Compound, Euler, etc.
2. **Cross-Chain**: Add Morpho integration with CCIP bridge
3. **Advanced Features**: 
   - Yield farming strategies
   - Liquidation protection
   - Portfolio optimization
4. **Analytics**: Add detailed analytics and reporting
5. **Mobile**: Optimize for mobile experience

## Useful Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Check for TypeScript errors
npm run type-check

# Run linting
npm run lint
```

## Support Resources

- **Avalanche Docs**: https://docs.avax.network/
- **Aave V3 Docs**: https://docs.aave.com/developers/
- **Wagmi Docs**: https://wagmi.sh/
- **RainbowKit Docs**: https://www.rainbowkit.com/

This guide should get your frontend fully integrated with the Aave-only lending aggregator. Test thoroughly on Fuji testnet before considering mainnet deployment!
