"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_all-wallets_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allWalletsSvg: () => (/* binding */ allWalletsSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst allWalletsSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 24 24\">\n  <path\n    style=\"fill: var(--wui-color-accent-100);\"\n    d=\"M10.2 6.6a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0ZM21 6.6a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0ZM10.2 17.4a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0ZM21 17.4a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0Z\"\n  />\n</svg>`;\n//# sourceMappingURL=all-wallets.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2FsbC13YWxsZXRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCO0FBQ25CLHNCQUFzQix3Q0FBRztBQUNoQztBQUNBLDZDQUE2QztBQUM3QztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAcmVvd25cXGFwcGtpdC11aVxcZGlzdFxcZXNtXFxzcmNcXGFzc2V0c1xcc3ZnXFxhbGwtd2FsbGV0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IGFsbFdhbGxldHNTdmcgPSBzdmcgYDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gIDxwYXRoXG4gICAgc3R5bGU9XCJmaWxsOiB2YXIoLS13dWktY29sb3ItYWNjZW50LTEwMCk7XCJcbiAgICBkPVwiTTEwLjIgNi42YTMuNiAzLjYgMCAxIDEtNy4yIDAgMy42IDMuNiAwIDAgMSA3LjIgMFpNMjEgNi42YTMuNiAzLjYgMCAxIDEtNy4yIDAgMy42IDMuNiAwIDAgMSA3LjIgMFpNMTAuMiAxNy40YTMuNiAzLjYgMCAxIDEtNy4yIDAgMy42IDMuNiAwIDAgMSA3LjIgMFpNMjEgMTcuNGEzLjYgMy42IDAgMSAxLTcuMiAwIDMuNiAzLjYgMCAwIDEgNy4yIDBaXCJcbiAgLz5cbjwvc3ZnPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hbGwtd2FsbGV0cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js\n"));

/***/ })

}]);