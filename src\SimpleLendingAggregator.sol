// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

// Simplified version for easy deployment via Remix
// This version uses standard OpenZeppelin imports that work with Remix

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

// Simplified Aave Pool interface
interface ISimpleAavePool {
    function supply(address asset, uint256 amount, address onBehalfOf, uint16 referralCode) external;
    function borrow(address asset, uint256 amount, uint256 interestRateMode, uint16 referralCode, address onBehalfOf) external;
    function withdraw(address asset, uint256 amount, address to) external returns (uint256);
    function repay(address asset, uint256 amount, uint256 interestRateMode, address onBehalfOf) external returns (uint256);
}

/**
 * @title SimpleLendingAggregator
 * @dev Simplified Aave-only lending aggregator for easy deployment and testing
 */
contract SimpleLendingAggregator is Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;
    
    // Position tracking for users
    struct UserPosition {
        uint256 aaveSupplied;
        uint256 aaveBorrowed;
        uint256 lastUpdate;
    }
    
    // Contract addresses
    ISimpleAavePool public immutable aavePool;
    
    // Supported assets
    mapping(address => bool) public supportedAssets;
    address[] public assetList;
    
    // User positions
    mapping(address => mapping(address => UserPosition)) public userPositions;
    
    // Events
    event AssetAdded(address indexed asset);
    event AssetRemoved(address indexed asset);
    event SupplyExecuted(address indexed user, address indexed asset, uint256 amount);
    event BorrowExecuted(address indexed user, address indexed asset, uint256 amount);
    event WithdrawExecuted(address indexed user, address indexed asset, uint256 amount);
    event RepayExecuted(address indexed user, address indexed asset, uint256 amount);
    
    // Custom errors
    error UnsupportedAsset();
    error InvalidAmount();
    error InsufficientBalance();
    
    constructor(address _aavePool, address _owner) Ownable(_owner) {
        aavePool = ISimpleAavePool(_aavePool);
    }
    
    /**
     * @dev Add supported asset
     */
    function addSupportedAsset(address asset) external onlyOwner {
        require(!supportedAssets[asset], "Asset already supported");
        supportedAssets[asset] = true;
        assetList.push(asset);
        emit AssetAdded(asset);
    }
    
    /**
     * @dev Remove supported asset
     */
    function removeSupportedAsset(address asset) external onlyOwner {
        require(supportedAssets[asset], "Asset not supported");
        supportedAssets[asset] = false;
        // Remove from array
        for (uint256 i = 0; i < assetList.length; i++) {
            if (assetList[i] == asset) {
                assetList[i] = assetList[assetList.length - 1];
                assetList.pop();
                break;
            }
        }
        emit AssetRemoved(asset);
    }
    
    /**
     * @dev Supply to Aave
     */
    function supplyToAave(address asset, uint256 amount) external nonReentrant {
        if (!supportedAssets[asset]) revert UnsupportedAsset();
        if (amount == 0) revert InvalidAmount();

        IERC20(asset).safeTransferFrom(msg.sender, address(this), amount);
        IERC20(asset).approve(address(aavePool), amount);
        aavePool.supply(asset, amount, msg.sender, 0);
        
        userPositions[msg.sender][asset].aaveSupplied += amount;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        
        emit SupplyExecuted(msg.sender, asset, amount);
    }
    
    /**
     * @dev Borrow from Aave
     */
    function borrowFromAave(address asset, uint256 amount) external nonReentrant {
        if (!supportedAssets[asset]) revert UnsupportedAsset();
        if (amount == 0) revert InvalidAmount();

        aavePool.borrow(asset, amount, 2, 0, msg.sender); // 2 = variable rate
        
        userPositions[msg.sender][asset].aaveBorrowed += amount;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        
        emit BorrowExecuted(msg.sender, asset, amount);
    }
    
    /**
     * @dev Withdraw from Aave
     */
    function withdrawFromAave(address asset, uint256 amount) external nonReentrant returns (uint256) {
        if (!supportedAssets[asset]) revert UnsupportedAsset();
        if (amount == 0) revert InvalidAmount();
        
        uint256 withdrawn = aavePool.withdraw(asset, amount, msg.sender);
        
        if (userPositions[msg.sender][asset].aaveSupplied >= withdrawn) {
            userPositions[msg.sender][asset].aaveSupplied -= withdrawn;
        }
        
        emit WithdrawExecuted(msg.sender, asset, withdrawn);
        return withdrawn;
    }

    /**
     * @dev Repay Aave debt
     */
    function repayToAave(address asset, uint256 amount) external nonReentrant returns (uint256) {
        if (!supportedAssets[asset]) revert UnsupportedAsset();
        if (amount == 0) revert InvalidAmount();
        
        IERC20(asset).safeTransferFrom(msg.sender, address(this), amount);
        IERC20(asset).approve(address(aavePool), amount);
        uint256 repaid = aavePool.repay(asset, amount, 2, msg.sender); // 2 = variable rate
        
        if (userPositions[msg.sender][asset].aaveBorrowed >= repaid) {
            userPositions[msg.sender][asset].aaveBorrowed -= repaid;
        }
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        
        emit RepayExecuted(msg.sender, asset, repaid);
        return repaid;
    }
    
    /**
     * @dev Get user's position for an asset
     */
    function getUserPosition(address user, address asset)
        external
        view
        returns (
            uint256 aaveSupplied,
            uint256 aaveBorrowed,
            uint256 lastUpdate
        )
    {
        UserPosition memory pos = userPositions[user][asset];
        return (pos.aaveSupplied, pos.aaveBorrowed, pos.lastUpdate);
    }
    
    /**
     * @dev Get all supported assets
     */
    function getSupportedAssets() external view returns (address[] memory) {
        return assetList;
    }
    
    /**
     * @dev Emergency withdraw function
     */
    function emergencyWithdraw(address asset, uint256 amount) external onlyOwner {
        IERC20(asset).safeTransfer(owner(), amount);
    }
}
