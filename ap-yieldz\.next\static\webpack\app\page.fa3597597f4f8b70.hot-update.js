"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/blockchain/config/wagmi.ts":
/*!****************************************!*\
  !*** ./app/blockchain/config/wagmi.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_NETWORK: () => (/* binding */ CURRENT_NETWORK),\n/* harmony export */   LENDING_APY_AGGREGATOR_ADDRESS: () => (/* binding */ LENDING_APY_AGGREGATOR_ADDRESS),\n/* harmony export */   SUPPORTED_TOKENS: () => (/* binding */ SUPPORTED_TOKENS),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getCurrentTokens: () => (/* binding */ getCurrentTokens)\n/* harmony export */ });\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi/chains */ \"(app-pages-browser)/./node_modules/viem/_esm/chains/definitions/avalancheFuji.js\");\n/* harmony import */ var _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @rainbow-me/rainbowkit/wallets */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-RTDGOYZC.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/viem/_esm/clients/transports/http.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/cookie.js\");\n\n\n\n\nif (false) {}\nconst { wallets } = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultWallets)();\nconst config = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultConfig)({\n    appName: 'Alligator',\n    projectId: \"b11d3071871bb2c95781af8517fd1cfe\",\n    wallets: [\n        {\n            groupName: \"Core Wallet\",\n            wallets: [\n                _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__.injectedWallet\n            ]\n        }\n    ],\n    chains: [\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji\n    ],\n    transports: {\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_3__.http)(\"https://api.avax-test.network/ext/bc/C/rpc\" || 0)\n    },\n    ssr: true,\n    storage: (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.createStorage)({\n        storage: wagmi__WEBPACK_IMPORTED_MODULE_5__.cookieStorage\n    })\n});\nconst LENDING_APY_AGGREGATOR_ADDRESS = \"******************************************\" || 0;\nconst SUPPORTED_TOKENS = {\n    // Avalanche Fuji Testnet addresses for testing\n    fuji: {\n        USDC: '******************************************',\n        WAVAX: '******************************************',\n        USDT: '******************************************'\n    },\n    // Avalanche Mainnet addresses (for future use)\n    avalanche: {\n        USDC: '******************************************',\n        USDT: '******************************************',\n        WETH: '******************************************',\n        WBTC: '******************************************',\n        WAVAX: '******************************************'\n    },\n    // Base addresses (for future Morpho integration)\n    base: {\n        USDC: '******************************************',\n        WETH: '******************************************'\n    }\n};\n// Current network configuration - change this to switch between testnet and mainnet\nconst CURRENT_NETWORK = 'fuji'; // 'fuji' for testnet, 'avalanche' for mainnet\n// Get tokens for current network\nconst getCurrentTokens = ()=>SUPPORTED_TOKENS[CURRENT_NETWORK];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blockchain/config/wagmi.ts\n"));

/***/ })

});