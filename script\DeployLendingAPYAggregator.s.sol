// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Script} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {LendingAPYAggregator} from "../src/LendingAPYAggregator.sol";

contract DeployLendingAPYAggregator is Script {

    function run() public {
        // Aave V3 Pool addresses
        // Avalanche Mainnet: 0x794a61358D6845594F94dc1DB02A252b5b4814aD
        // Avalanche Fuji Testnet: 0x7d2768dE32b0b80b7a3454c06BdAc94A69DDc7A9
        address aavePool = vm.envOr("AAVE_POOL_ADDRESS", 0x7d2768dE32b0b80b7a3454c06BdAc94A69DDc7A9); // Default to Fuji testnet
        address owner = msg.sender;

        vm.startBroadcast();

        LendingAPYAggregator aggregator = new LendingAPYAggregator(
            aavePool,
            owner
        );

        console.log("LendingAPYAggregator (Aave-only) deployed at:", address(aggregator));
        console.log("Owner:", owner);
        console.log("Aave Pool:", aavePool);

        vm.stopBroadcast();
    }
}
