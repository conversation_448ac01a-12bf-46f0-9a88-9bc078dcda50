"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unstorage";
exports.ids = ["vendor-chunks/unstorage"];
exports.modules = {

/***/ "(ssr)/./node_modules/unstorage/dist/index.mjs":
/*!***********************************************!*\
  !*** ./node_modules/unstorage/dist/index.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   builtinDrivers: () => (/* binding */ builtinDrivers),\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   defineDriver: () => (/* binding */ defineDriver),\n/* harmony export */   filterKeyByBase: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   filterKeyByDepth: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   joinKeys: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   normalizeBaseKey: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   normalizeKey: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   prefixStorage: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   restoreSnapshot: () => (/* binding */ restoreSnapshot),\n/* harmony export */   snapshot: () => (/* binding */ snapshot)\n/* harmony export */ });\n/* harmony import */ var destr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! destr */ \"(ssr)/./node_modules/destr/dist/index.mjs\");\n/* harmony import */ var _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./shared/unstorage.CoCt7NXC.mjs */ \"(ssr)/./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs\");\n\n\n\n\nfunction defineDriver(factory) {\n  return factory;\n}\n\nconst DRIVER_NAME = \"memory\";\nconst memory = defineDriver(() => {\n  const data = /* @__PURE__ */ new Map();\n  return {\n    name: DRIVER_NAME,\n    getInstance: () => data,\n    hasItem(key) {\n      return data.has(key);\n    },\n    getItem(key) {\n      return data.get(key) ?? null;\n    },\n    getItemRaw(key) {\n      return data.get(key) ?? null;\n    },\n    setItem(key, value) {\n      data.set(key, value);\n    },\n    setItemRaw(key, value) {\n      data.set(key, value);\n    },\n    removeItem(key) {\n      data.delete(key);\n    },\n    getKeys() {\n      return [...data.keys()];\n    },\n    clear() {\n      data.clear();\n    },\n    dispose() {\n      data.clear();\n    }\n  };\n});\n\nfunction createStorage(options = {}) {\n  const context = {\n    mounts: { \"\": options.driver || memory() },\n    mountpoints: [\"\"],\n    watching: false,\n    watchListeners: [],\n    unwatch: {}\n  };\n  const getMount = (key) => {\n    for (const base of context.mountpoints) {\n      if (key.startsWith(base)) {\n        return {\n          base,\n          relativeKey: key.slice(base.length),\n          driver: context.mounts[base]\n        };\n      }\n    }\n    return {\n      base: \"\",\n      relativeKey: key,\n      driver: context.mounts[\"\"]\n    };\n  };\n  const getMounts = (base, includeParent) => {\n    return context.mountpoints.filter(\n      (mountpoint) => mountpoint.startsWith(base) || includeParent && base.startsWith(mountpoint)\n    ).map((mountpoint) => ({\n      relativeBase: base.length > mountpoint.length ? base.slice(mountpoint.length) : void 0,\n      mountpoint,\n      driver: context.mounts[mountpoint]\n    }));\n  };\n  const onChange = (event, key) => {\n    if (!context.watching) {\n      return;\n    }\n    key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n    for (const listener of context.watchListeners) {\n      listener(event, key);\n    }\n  };\n  const startWatch = async () => {\n    if (context.watching) {\n      return;\n    }\n    context.watching = true;\n    for (const mountpoint in context.mounts) {\n      context.unwatch[mountpoint] = await watch(\n        context.mounts[mountpoint],\n        onChange,\n        mountpoint\n      );\n    }\n  };\n  const stopWatch = async () => {\n    if (!context.watching) {\n      return;\n    }\n    for (const mountpoint in context.unwatch) {\n      await context.unwatch[mountpoint]();\n    }\n    context.unwatch = {};\n    context.watching = false;\n  };\n  const runBatch = (items, commonOptions, cb) => {\n    const batches = /* @__PURE__ */ new Map();\n    const getBatch = (mount) => {\n      let batch = batches.get(mount.base);\n      if (!batch) {\n        batch = {\n          driver: mount.driver,\n          base: mount.base,\n          items: []\n        };\n        batches.set(mount.base, batch);\n      }\n      return batch;\n    };\n    for (const item of items) {\n      const isStringItem = typeof item === \"string\";\n      const key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(isStringItem ? item : item.key);\n      const value = isStringItem ? void 0 : item.value;\n      const options2 = isStringItem || !item.options ? commonOptions : { ...commonOptions, ...item.options };\n      const mount = getMount(key);\n      getBatch(mount).items.push({\n        key,\n        value,\n        relativeKey: mount.relativeKey,\n        options: options2\n      });\n    }\n    return Promise.all([...batches.values()].map((batch) => cb(batch))).then(\n      (r) => r.flat()\n    );\n  };\n  const storage = {\n    // Item\n    hasItem(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.hasItem, relativeKey, opts);\n    },\n    getItem(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItem, relativeKey, opts).then(\n        (value) => (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value)\n      );\n    },\n    getItems(items, commonOptions = {}) {\n      return runBatch(items, commonOptions, (batch) => {\n        if (batch.driver.getItems) {\n          return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n            batch.driver.getItems,\n            batch.items.map((item) => ({\n              key: item.relativeKey,\n              options: item.options\n            })),\n            commonOptions\n          ).then(\n            (r) => r.map((item) => ({\n              key: (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.j)(batch.base, item.key),\n              value: (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(item.value)\n            }))\n          );\n        }\n        return Promise.all(\n          batch.items.map((item) => {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n              batch.driver.getItem,\n              item.relativeKey,\n              item.options\n            ).then((value) => ({\n              key: item.key,\n              value: (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value)\n            }));\n          })\n        );\n      });\n    },\n    getItemRaw(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (driver.getItemRaw) {\n        return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItemRaw, relativeKey, opts);\n      }\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItem, relativeKey, opts).then(\n        (value) => (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(value)\n      );\n    },\n    async setItem(key, value, opts = {}) {\n      if (value === void 0) {\n        return storage.removeItem(key);\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (!driver.setItem) {\n        return;\n      }\n      await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItem, relativeKey, (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(value), opts);\n      if (!driver.watch) {\n        onChange(\"update\", key);\n      }\n    },\n    async setItems(items, commonOptions) {\n      await runBatch(items, commonOptions, async (batch) => {\n        if (batch.driver.setItems) {\n          return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n            batch.driver.setItems,\n            batch.items.map((item) => ({\n              key: item.relativeKey,\n              value: (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(item.value),\n              options: item.options\n            })),\n            commonOptions\n          );\n        }\n        if (!batch.driver.setItem) {\n          return;\n        }\n        await Promise.all(\n          batch.items.map((item) => {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n              batch.driver.setItem,\n              item.relativeKey,\n              (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(item.value),\n              item.options\n            );\n          })\n        );\n      });\n    },\n    async setItemRaw(key, value, opts = {}) {\n      if (value === void 0) {\n        return storage.removeItem(key, opts);\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (driver.setItemRaw) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItemRaw, relativeKey, value, opts);\n      } else if (driver.setItem) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItem, relativeKey, (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(value), opts);\n      } else {\n        return;\n      }\n      if (!driver.watch) {\n        onChange(\"update\", key);\n      }\n    },\n    async removeItem(key, opts = {}) {\n      if (typeof opts === \"boolean\") {\n        opts = { removeMeta: opts };\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (!driver.removeItem) {\n        return;\n      }\n      await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.removeItem, relativeKey, opts);\n      if (opts.removeMeta || opts.removeMata) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.removeItem, relativeKey + \"$\", opts);\n      }\n      if (!driver.watch) {\n        onChange(\"remove\", key);\n      }\n    },\n    // Meta\n    async getMeta(key, opts = {}) {\n      if (typeof opts === \"boolean\") {\n        opts = { nativeOnly: opts };\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      const meta = /* @__PURE__ */ Object.create(null);\n      if (driver.getMeta) {\n        Object.assign(meta, await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getMeta, relativeKey, opts));\n      }\n      if (!opts.nativeOnly) {\n        const value = await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n          driver.getItem,\n          relativeKey + \"$\",\n          opts\n        ).then((value_) => (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value_));\n        if (value && typeof value === \"object\") {\n          if (typeof value.atime === \"string\") {\n            value.atime = new Date(value.atime);\n          }\n          if (typeof value.mtime === \"string\") {\n            value.mtime = new Date(value.mtime);\n          }\n          Object.assign(meta, value);\n        }\n      }\n      return meta;\n    },\n    setMeta(key, value, opts = {}) {\n      return this.setItem(key + \"$\", value, opts);\n    },\n    removeMeta(key, opts = {}) {\n      return this.removeItem(key + \"$\", opts);\n    },\n    // Keys\n    async getKeys(base, opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      const mounts = getMounts(base, true);\n      let maskedMounts = [];\n      const allKeys = [];\n      let allMountsSupportMaxDepth = true;\n      for (const mount of mounts) {\n        if (!mount.driver.flags?.maxDepth) {\n          allMountsSupportMaxDepth = false;\n        }\n        const rawKeys = await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n          mount.driver.getKeys,\n          mount.relativeBase,\n          opts\n        );\n        for (const key of rawKeys) {\n          const fullKey = mount.mountpoint + (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n          if (!maskedMounts.some((p) => fullKey.startsWith(p))) {\n            allKeys.push(fullKey);\n          }\n        }\n        maskedMounts = [\n          mount.mountpoint,\n          ...maskedMounts.filter((p) => !p.startsWith(mount.mountpoint))\n        ];\n      }\n      const shouldFilterByDepth = opts.maxDepth !== void 0 && !allMountsSupportMaxDepth;\n      return allKeys.filter(\n        (key) => (!shouldFilterByDepth || (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(key, opts.maxDepth)) && (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(key, base)\n      );\n    },\n    // Utils\n    async clear(base, opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      await Promise.all(\n        getMounts(base, false).map(async (m) => {\n          if (m.driver.clear) {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(m.driver.clear, m.relativeBase, opts);\n          }\n          if (m.driver.removeItem) {\n            const keys = await m.driver.getKeys(m.relativeBase || \"\", opts);\n            return Promise.all(\n              keys.map((key) => m.driver.removeItem(key, opts))\n            );\n          }\n        })\n      );\n    },\n    async dispose() {\n      await Promise.all(\n        Object.values(context.mounts).map((driver) => dispose(driver))\n      );\n    },\n    async watch(callback) {\n      await startWatch();\n      context.watchListeners.push(callback);\n      return async () => {\n        context.watchListeners = context.watchListeners.filter(\n          (listener) => listener !== callback\n        );\n        if (context.watchListeners.length === 0) {\n          await stopWatch();\n        }\n      };\n    },\n    async unwatch() {\n      context.watchListeners = [];\n      await stopWatch();\n    },\n    // Mount\n    mount(base, driver) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      if (base && context.mounts[base]) {\n        throw new Error(`already mounted at ${base}`);\n      }\n      if (base) {\n        context.mountpoints.push(base);\n        context.mountpoints.sort((a, b) => b.length - a.length);\n      }\n      context.mounts[base] = driver;\n      if (context.watching) {\n        Promise.resolve(watch(driver, onChange, base)).then((unwatcher) => {\n          context.unwatch[base] = unwatcher;\n        }).catch(console.error);\n      }\n      return storage;\n    },\n    async unmount(base, _dispose = true) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      if (!base || !context.mounts[base]) {\n        return;\n      }\n      if (context.watching && base in context.unwatch) {\n        context.unwatch[base]?.();\n        delete context.unwatch[base];\n      }\n      if (_dispose) {\n        await dispose(context.mounts[base]);\n      }\n      context.mountpoints = context.mountpoints.filter((key) => key !== base);\n      delete context.mounts[base];\n    },\n    getMount(key = \"\") {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key) + \":\";\n      const m = getMount(key);\n      return {\n        driver: m.driver,\n        base: m.base\n      };\n    },\n    getMounts(base = \"\", opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(base);\n      const mounts = getMounts(base, opts.parents);\n      return mounts.map((m) => ({\n        driver: m.driver,\n        base: m.mountpoint\n      }));\n    },\n    // Aliases\n    keys: (base, opts = {}) => storage.getKeys(base, opts),\n    get: (key, opts = {}) => storage.getItem(key, opts),\n    set: (key, value, opts = {}) => storage.setItem(key, value, opts),\n    has: (key, opts = {}) => storage.hasItem(key, opts),\n    del: (key, opts = {}) => storage.removeItem(key, opts),\n    remove: (key, opts = {}) => storage.removeItem(key, opts)\n  };\n  return storage;\n}\nasync function snapshot(storage, base) {\n  base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n  const keys = await storage.getKeys(base);\n  const snapshot2 = {};\n  await Promise.all(\n    keys.map(async (key) => {\n      snapshot2[key.slice(base.length)] = await storage.getItem(key);\n    })\n  );\n  return snapshot2;\n}\nasync function restoreSnapshot(driver, snapshot2, base = \"\") {\n  base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n  await Promise.all(\n    Object.entries(snapshot2).map((e) => driver.setItem(base + e[0], e[1]))\n  );\n}\nfunction watch(driver, onChange, base) {\n  return driver.watch ? driver.watch((event, key) => onChange(event, base + key)) : () => {\n  };\n}\nasync function dispose(driver) {\n  if (typeof driver.dispose === \"function\") {\n    await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.dispose);\n  }\n}\n\nconst builtinDrivers = {\n  \"azure-app-configuration\": \"unstorage/drivers/azure-app-configuration\",\n  \"azureAppConfiguration\": \"unstorage/drivers/azure-app-configuration\",\n  \"azure-cosmos\": \"unstorage/drivers/azure-cosmos\",\n  \"azureCosmos\": \"unstorage/drivers/azure-cosmos\",\n  \"azure-key-vault\": \"unstorage/drivers/azure-key-vault\",\n  \"azureKeyVault\": \"unstorage/drivers/azure-key-vault\",\n  \"azure-storage-blob\": \"unstorage/drivers/azure-storage-blob\",\n  \"azureStorageBlob\": \"unstorage/drivers/azure-storage-blob\",\n  \"azure-storage-table\": \"unstorage/drivers/azure-storage-table\",\n  \"azureStorageTable\": \"unstorage/drivers/azure-storage-table\",\n  \"capacitor-preferences\": \"unstorage/drivers/capacitor-preferences\",\n  \"capacitorPreferences\": \"unstorage/drivers/capacitor-preferences\",\n  \"cloudflare-kv-binding\": \"unstorage/drivers/cloudflare-kv-binding\",\n  \"cloudflareKVBinding\": \"unstorage/drivers/cloudflare-kv-binding\",\n  \"cloudflare-kv-http\": \"unstorage/drivers/cloudflare-kv-http\",\n  \"cloudflareKVHttp\": \"unstorage/drivers/cloudflare-kv-http\",\n  \"cloudflare-r2-binding\": \"unstorage/drivers/cloudflare-r2-binding\",\n  \"cloudflareR2Binding\": \"unstorage/drivers/cloudflare-r2-binding\",\n  \"db0\": \"unstorage/drivers/db0\",\n  \"deno-kv-node\": \"unstorage/drivers/deno-kv-node\",\n  \"denoKVNode\": \"unstorage/drivers/deno-kv-node\",\n  \"deno-kv\": \"unstorage/drivers/deno-kv\",\n  \"denoKV\": \"unstorage/drivers/deno-kv\",\n  \"fs-lite\": \"unstorage/drivers/fs-lite\",\n  \"fsLite\": \"unstorage/drivers/fs-lite\",\n  \"fs\": \"unstorage/drivers/fs\",\n  \"github\": \"unstorage/drivers/github\",\n  \"http\": \"unstorage/drivers/http\",\n  \"indexedb\": \"unstorage/drivers/indexedb\",\n  \"localstorage\": \"unstorage/drivers/localstorage\",\n  \"lru-cache\": \"unstorage/drivers/lru-cache\",\n  \"lruCache\": \"unstorage/drivers/lru-cache\",\n  \"memory\": \"unstorage/drivers/memory\",\n  \"mongodb\": \"unstorage/drivers/mongodb\",\n  \"netlify-blobs\": \"unstorage/drivers/netlify-blobs\",\n  \"netlifyBlobs\": \"unstorage/drivers/netlify-blobs\",\n  \"null\": \"unstorage/drivers/null\",\n  \"overlay\": \"unstorage/drivers/overlay\",\n  \"planetscale\": \"unstorage/drivers/planetscale\",\n  \"redis\": \"unstorage/drivers/redis\",\n  \"s3\": \"unstorage/drivers/s3\",\n  \"session-storage\": \"unstorage/drivers/session-storage\",\n  \"sessionStorage\": \"unstorage/drivers/session-storage\",\n  \"uploadthing\": \"unstorage/drivers/uploadthing\",\n  \"upstash\": \"unstorage/drivers/upstash\",\n  \"vercel-blob\": \"unstorage/drivers/vercel-blob\",\n  \"vercelBlob\": \"unstorage/drivers/vercel-blob\",\n  \"vercel-kv\": \"unstorage/drivers/vercel-kv\",\n  \"vercelKV\": \"unstorage/drivers/vercel-kv\"\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unstorage/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ normalizeKey),\n/* harmony export */   b: () => (/* binding */ asyncCall),\n/* harmony export */   c: () => (/* binding */ filterKeyByBase),\n/* harmony export */   d: () => (/* binding */ stringify),\n/* harmony export */   e: () => (/* binding */ deserializeRaw),\n/* harmony export */   f: () => (/* binding */ filterKeyByDepth),\n/* harmony export */   j: () => (/* binding */ joinKeys),\n/* harmony export */   n: () => (/* binding */ normalizeBaseKey),\n/* harmony export */   p: () => (/* binding */ prefixStorage),\n/* harmony export */   s: () => (/* binding */ serializeRaw)\n/* harmony export */ });\nfunction wrapToPromise(value) {\n  if (!value || typeof value.then !== \"function\") {\n    return Promise.resolve(value);\n  }\n  return value;\n}\nfunction asyncCall(function_, ...arguments_) {\n  try {\n    return wrapToPromise(function_(...arguments_));\n  } catch (error) {\n    return Promise.reject(error);\n  }\n}\nfunction isPrimitive(value) {\n  const type = typeof value;\n  return value === null || type !== \"object\" && type !== \"function\";\n}\nfunction isPureObject(value) {\n  const proto = Object.getPrototypeOf(value);\n  return !proto || proto.isPrototypeOf(Object);\n}\nfunction stringify(value) {\n  if (isPrimitive(value)) {\n    return String(value);\n  }\n  if (isPureObject(value) || Array.isArray(value)) {\n    return JSON.stringify(value);\n  }\n  if (typeof value.toJSON === \"function\") {\n    return stringify(value.toJSON());\n  }\n  throw new Error(\"[unstorage] Cannot stringify value!\");\n}\nconst BASE64_PREFIX = \"base64:\";\nfunction serializeRaw(value) {\n  if (typeof value === \"string\") {\n    return value;\n  }\n  return BASE64_PREFIX + base64Encode(value);\n}\nfunction deserializeRaw(value) {\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  if (!value.startsWith(BASE64_PREFIX)) {\n    return value;\n  }\n  return base64Decode(value.slice(BASE64_PREFIX.length));\n}\nfunction base64Decode(input) {\n  if (globalThis.Buffer) {\n    return Buffer.from(input, \"base64\");\n  }\n  return Uint8Array.from(\n    globalThis.atob(input),\n    (c) => c.codePointAt(0)\n  );\n}\nfunction base64Encode(input) {\n  if (globalThis.Buffer) {\n    return Buffer.from(input).toString(\"base64\");\n  }\n  return globalThis.btoa(String.fromCodePoint(...input));\n}\n\nconst storageKeyProperties = [\n  \"has\",\n  \"hasItem\",\n  \"get\",\n  \"getItem\",\n  \"getItemRaw\",\n  \"set\",\n  \"setItem\",\n  \"setItemRaw\",\n  \"del\",\n  \"remove\",\n  \"removeItem\",\n  \"getMeta\",\n  \"setMeta\",\n  \"removeMeta\",\n  \"getKeys\",\n  \"clear\",\n  \"mount\",\n  \"unmount\"\n];\nfunction prefixStorage(storage, base) {\n  base = normalizeBaseKey(base);\n  if (!base) {\n    return storage;\n  }\n  const nsStorage = { ...storage };\n  for (const property of storageKeyProperties) {\n    nsStorage[property] = (key = \"\", ...args) => (\n      // @ts-ignore\n      storage[property](base + key, ...args)\n    );\n  }\n  nsStorage.getKeys = (key = \"\", ...arguments_) => storage.getKeys(base + key, ...arguments_).then((keys) => keys.map((key2) => key2.slice(base.length)));\n  nsStorage.getItems = async (items, commonOptions) => {\n    const prefixedItems = items.map(\n      (item) => typeof item === \"string\" ? base + item : { ...item, key: base + item.key }\n    );\n    const results = await storage.getItems(prefixedItems, commonOptions);\n    return results.map((entry) => ({\n      key: entry.key.slice(base.length),\n      value: entry.value\n    }));\n  };\n  nsStorage.setItems = async (items, commonOptions) => {\n    const prefixedItems = items.map((item) => ({\n      key: base + item.key,\n      value: item.value,\n      options: item.options\n    }));\n    return storage.setItems(prefixedItems, commonOptions);\n  };\n  return nsStorage;\n}\nfunction normalizeKey(key) {\n  if (!key) {\n    return \"\";\n  }\n  return key.split(\"?\")[0]?.replace(/[/\\\\]/g, \":\").replace(/:+/g, \":\").replace(/^:|:$/g, \"\") || \"\";\n}\nfunction joinKeys(...keys) {\n  return normalizeKey(keys.join(\":\"));\n}\nfunction normalizeBaseKey(base) {\n  base = normalizeKey(base);\n  return base ? base + \":\" : \"\";\n}\nfunction filterKeyByDepth(key, depth) {\n  if (depth === void 0) {\n    return true;\n  }\n  let substrCount = 0;\n  let index = key.indexOf(\":\");\n  while (index > -1) {\n    substrCount++;\n    index = key.indexOf(\":\", index + 1);\n  }\n  return substrCount <= depth;\n}\nfunction filterKeyByBase(key, base) {\n  if (base) {\n    return key.startsWith(base) && key[key.length - 1] !== \"$\";\n  }\n  return key[key.length - 1] !== \"$\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs\n");

/***/ })

};
;