"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cuer";
exports.ids = ["vendor-chunks/cuer"];
exports.modules = {

/***/ "(ssr)/./node_modules/cuer/_dist/Cuer.js":
/*!*****************************************!*\
  !*** ./node_modules/cuer/_dist/Cuer.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cuer: () => (/* binding */ Cuer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _QrCode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QrCode.js */ \"(ssr)/./node_modules/cuer/_dist/QrCode.js\");\n\n\n\n/**\n * Renders a QR code with a finder pattern, cells, and an `arena` (if provided).\n *\n * @params {@link Cuer.Props}\n * @returns A {@link React.ReactNode}\n */\nfunction Cuer(props) {\n    const { arena, ...rest } = props;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Cuer.Root, { ...rest, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Finder, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Cells, {}), arena && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Arena, { children: typeof arena === 'string' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", { alt: \"Arena\", src: arena, style: {\n                        borderRadius: 1,\n                        height: '100%',\n                        objectFit: 'cover',\n                        width: '100%',\n                    } })) : (arena) }))] }));\n}\n(function (Cuer) {\n    Cuer.Context = react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);\n    /**\n     * Root component for the QR code.\n     *\n     * @params {@link Root.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Root(props) {\n        const { children, size = '100%', value, version, ...rest } = props;\n        // Check if the children contain an `Arena` component.\n        const hasArena = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (react__WEBPACK_IMPORTED_MODULE_1__.Children.map(children, (child) => {\n            if (!react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(child))\n                return null;\n            if (typeof child.type === 'string')\n                return null;\n            if ('displayName' in child.type &&\n                child.type.displayName === 'Arena')\n                return true;\n            return null;\n        }) ?? []).some(Boolean), [children]);\n        // Create the QR code.\n        const qrcode = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n            let errorCorrection = props.errorCorrection;\n            // If the QR code has an arena, use a higher error correction level.\n            if (hasArena && errorCorrection === 'low')\n                errorCorrection = 'medium';\n            return _QrCode_js__WEBPACK_IMPORTED_MODULE_2__.create(value, {\n                errorCorrection,\n                version,\n            });\n        }, [value, hasArena, props.errorCorrection, version]);\n        const cellSize = 1;\n        const edgeSize = qrcode.edgeLength * cellSize;\n        const finderSize = (qrcode.finderLength * cellSize) / 2;\n        const arenaSize = hasArena ? Math.floor(edgeSize / 4) : 0;\n        const context = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => ({ arenaSize, cellSize, edgeSize, qrcode, finderSize }), [arenaSize, edgeSize, qrcode, finderSize]);\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Context.Provider, { value: context, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", { ...rest, width: size, height: size, viewBox: `0 0 ${edgeSize} ${edgeSize}`, xmlns: \"http://www.w3.org/2000/svg\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"title\", { children: \"QR Code\" }), children] }) }));\n    }\n    Cuer.Root = Root;\n    (function (Root) {\n        Root.displayName = 'Root';\n    })(Root = Cuer.Root || (Cuer.Root = {}));\n    /**\n     * Finder component for the QR code. The finder pattern is the squares\n     * on the top left, top right, and bottom left of the QR code.\n     *\n     * @params {@link Finder.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Finder(props) {\n        const { className, fill, innerClassName, radius = 0.25 } = props;\n        const { cellSize, edgeSize, finderSize } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        function Inner({ position }) {\n            let outerX = finderSize - (finderSize - cellSize) - cellSize / 2;\n            if (position === 'top-right')\n                outerX = edgeSize - finderSize - (finderSize - cellSize) - cellSize / 2;\n            let outerY = finderSize - (finderSize - cellSize) - cellSize / 2;\n            if (position === 'bottom-left')\n                outerY = edgeSize - finderSize - (finderSize - cellSize) - cellSize / 2;\n            let innerX = finderSize - cellSize * 1.5;\n            if (position === 'top-right')\n                innerX = edgeSize - finderSize - cellSize * 1.5;\n            let innerY = finderSize - cellSize * 1.5;\n            if (position === 'bottom-left')\n                innerY = edgeSize - finderSize - cellSize * 1.5;\n            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", { className: className, stroke: fill ?? 'currentColor', fill: \"transparent\", x: outerX, y: outerY, width: cellSize + (finderSize - cellSize) * 2, height: cellSize + (finderSize - cellSize) * 2, rx: 2 * radius * (finderSize - cellSize), ry: 2 * radius * (finderSize - cellSize), strokeWidth: cellSize }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", { className: innerClassName, fill: fill ?? 'currentColor', x: innerX, y: innerY, width: cellSize * 3, height: cellSize * 3, rx: 2 * radius * cellSize, ry: 2 * radius * cellSize })] }));\n        }\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"top-left\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"top-right\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"bottom-left\" })] }));\n    }\n    Cuer.Finder = Finder;\n    (function (Finder) {\n        Finder.displayName = 'Finder';\n    })(Finder = Cuer.Finder || (Cuer.Finder = {}));\n    /**\n     * Cells for the QR code.\n     *\n     * @params {@link Cells.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Cells(props) {\n        const { className, fill = 'currentColor', inset: inset_ = true, radius = 1, } = props;\n        const { arenaSize, cellSize, qrcode } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        const { edgeLength, finderLength } = qrcode;\n        const path = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n            let path = '';\n            for (let i = 0; i < qrcode.grid.length; i++) {\n                const row = qrcode.grid[i];\n                if (!row)\n                    continue;\n                for (let j = 0; j < row.length; j++) {\n                    const cell = row[j];\n                    if (!cell)\n                        continue;\n                    // Skip rendering dots in arena area.\n                    const start = edgeLength / 2 - arenaSize / 2;\n                    const end = start + arenaSize;\n                    if (i >= start && i <= end && j >= start && j <= end)\n                        continue;\n                    // Skip rendering dots in the finder pattern areas\n                    const topLeftFinder = i < finderLength && j < finderLength;\n                    const topRightFinder = i < finderLength && j >= edgeLength - finderLength;\n                    const bottomLeftFinder = i >= edgeLength - finderLength && j < finderLength;\n                    if (topLeftFinder || topRightFinder || bottomLeftFinder)\n                        continue;\n                    // Add inset for padding\n                    const inset = inset_ ? cellSize * 0.1 : 0;\n                    const innerSize = (cellSize - inset * 2) / 2;\n                    // Calculate center positions\n                    const cx = j * cellSize + cellSize / 2;\n                    const cy = i * cellSize + cellSize / 2;\n                    // Calculate edge positions\n                    const left = cx - innerSize;\n                    const right = cx + innerSize;\n                    const top = cy - innerSize;\n                    const bottom = cy + innerSize;\n                    // Apply corner radius (clamped to maximum of innerSize)\n                    const r = radius * innerSize;\n                    path += [\n                        `M ${left + r},${top}`,\n                        `L ${right - r},${top}`,\n                        `A ${r},${r} 0 0,1 ${right},${top + r}`,\n                        `L ${right},${bottom - r}`,\n                        `A ${r},${r} 0 0,1 ${right - r},${bottom}`,\n                        `L ${left + r},${bottom}`,\n                        `A ${r},${r} 0 0,1 ${left},${bottom - r}`,\n                        `L ${left},${top + r}`,\n                        `A ${r},${r} 0 0,1 ${left + r},${top}`,\n                        'z',\n                    ].join(' ');\n                }\n            }\n            return path;\n        }, [\n            arenaSize,\n            cellSize,\n            edgeLength,\n            finderLength,\n            qrcode.grid,\n            inset_,\n            radius,\n        ]);\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { className: className, d: path, fill: fill });\n    }\n    Cuer.Cells = Cells;\n    (function (Cells) {\n        Cells.displayName = 'Cells';\n    })(Cells = Cuer.Cells || (Cuer.Cells = {}));\n    /**\n     * Arena component for the QR code. The arena is the area in the center\n     * of the QR code that is not part of the finder pattern.\n     *\n     * @params {@link Arena.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Arena(props) {\n        const { children } = props;\n        const { arenaSize, cellSize, edgeSize } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        const start = Math.ceil(edgeSize / 2 - arenaSize / 2);\n        const size = arenaSize + (arenaSize % 2);\n        const padding = cellSize / 2;\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"foreignObject\", { x: start, y: start, width: size, height: size, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: {\n                    alignItems: 'center',\n                    display: 'flex',\n                    fontSize: 1,\n                    justifyContent: 'center',\n                    height: '100%',\n                    overflow: 'hidden',\n                    width: '100%',\n                    padding,\n                    boxSizing: 'border-box',\n                }, children: children }) }));\n    }\n    Cuer.Arena = Arena;\n    (function (Arena) {\n        Arena.displayName = 'Arena';\n    })(Arena = Cuer.Arena || (Cuer.Arena = {}));\n})(Cuer || (Cuer = {}));\n//# sourceMappingURL=Cuer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/cuer/_dist/Cuer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/cuer/_dist/QrCode.js":
/*!*******************************************!*\
  !*** ./node_modules/cuer/_dist/QrCode.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var qr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! qr */ \"(ssr)/./node_modules/qr/esm/index.js\");\n\nfunction create(value, options = {}) {\n    const { errorCorrection, version } = options;\n    const grid = (0,qr__WEBPACK_IMPORTED_MODULE_0__.encodeQR)(value, 'raw', {\n        border: 0,\n        ecc: errorCorrection,\n        scale: 1,\n        version: version,\n    });\n    const finderLength = 7;\n    const edgeLength = grid.length;\n    return {\n        edgeLength,\n        finderLength,\n        grid,\n        value,\n    };\n}\n//# sourceMappingURL=QrCode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3Vlci9fZGlzdC9RckNvZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEI7QUFDdkIsbUNBQW1DO0FBQzFDLFlBQVksMkJBQTJCO0FBQ3ZDLGlCQUFpQiw0Q0FBUTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxjdWVyXFxfZGlzdFxcUXJDb2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGVuY29kZVFSIH0gZnJvbSAncXInO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZSh2YWx1ZSwgb3B0aW9ucyA9IHt9KSB7XG4gICAgY29uc3QgeyBlcnJvckNvcnJlY3Rpb24sIHZlcnNpb24gfSA9IG9wdGlvbnM7XG4gICAgY29uc3QgZ3JpZCA9IGVuY29kZVFSKHZhbHVlLCAncmF3Jywge1xuICAgICAgICBib3JkZXI6IDAsXG4gICAgICAgIGVjYzogZXJyb3JDb3JyZWN0aW9uLFxuICAgICAgICBzY2FsZTogMSxcbiAgICAgICAgdmVyc2lvbjogdmVyc2lvbixcbiAgICB9KTtcbiAgICBjb25zdCBmaW5kZXJMZW5ndGggPSA3O1xuICAgIGNvbnN0IGVkZ2VMZW5ndGggPSBncmlkLmxlbmd0aDtcbiAgICByZXR1cm4ge1xuICAgICAgICBlZGdlTGVuZ3RoLFxuICAgICAgICBmaW5kZXJMZW5ndGgsXG4gICAgICAgIGdyaWQsXG4gICAgICAgIHZhbHVlLFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1RckNvZGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/cuer/_dist/QrCode.js\n");

/***/ })

};
;