'use client';

import { useState, useEffect } from 'react';
import { useAccount, useBalance, useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { parseUnits } from 'viem';
import { useAPYData } from '../blockchain/hooks/useAPYData';
import { CustomConnectButton } from './CustomConnectButton';
import { SIMPLE_LENDING_AGGREGATOR_ABI, ERC20_ABI } from '../blockchain/abi/SimpleLendingAggregator';
import { LENDING_APY_AGGREGATOR_ADDRESS, SUPPORTED_TOKENS } from '../blockchain/config/wagmi';
import { X, ArrowUpCircle, ArrowDownCircle, RotateCcw, DollarSign, AlertCircle } from 'lucide-react';

interface TradingModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedAsset: string;
  defaultAction?: 'supply' | 'borrow' | 'withdraw' | 'repay';
}

export function TradingModal({ isOpen, onClose, selectedAsset, defaultAction = 'supply' }: TradingModalProps) {
  const { address, isConnected } = useAccount();
  const { getAPYForAsset } = useAPYData();
  
  const [action, setAction] = useState<'supply' | 'borrow' | 'withdraw' | 'repay'>(defaultAction);
  const [protocol, setProtocol] = useState<'aave' | 'morpho'>('aave');
  const [amount, setAmount] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Get user's ETH balance as an example
  const { data: balance } = useBalance({
    address: address,
  });

  // Contract interactions
  const { writeContract, data: hash, isPending, error: writeError } = useWriteContract();
  
  // Wait for transaction receipt
  const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({
    hash,
  });

  // Get token address from supported tokens
  const getTokenAddress = (symbol: string) => {
    const tokens = SUPPORTED_TOKENS.fuji;
    return tokens[symbol as keyof typeof tokens] || `0x${symbol.toLowerCase().padEnd(40, '0')}`;
  };

  const tokenAddress = getTokenAddress(selectedAsset);
  
  // Mock token balance and allowance for now (you can implement real token balance checks)
  const tokenBalance = { formatted: '1000.0', symbol: selectedAsset };
  const allowance = { formatted: '0' };
  
  // Real contract operations
  const operations = {
    approveToken: async (token: string, amount: string) => {
      try {
        await writeContract({
          address: token as `0x${string}`,
          abi: ERC20_ABI,
          functionName: 'approve',
          args: [LENDING_APY_AGGREGATOR_ADDRESS, parseUnits(amount, 18)],
        });
      } catch (err) {
        console.error('Approve error:', err);
        throw err;
      }
    },
    
    supplyToAave: async (token: string, amount: string) => {
      try {
        console.log('Supply to Aave called with:', {
          contractAddress: LENDING_APY_AGGREGATOR_ADDRESS,
          tokenAddress: token,
          amount: amount,
          parsedAmount: parseUnits(amount, 18),
          userAddress: address
        });
        
        await writeContract({
          address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
          abi: SIMPLE_LENDING_AGGREGATOR_ABI,
          functionName: 'supplyToAave',
          args: [token, parseUnits(amount, 18)],
        });
      } catch (err) {
        console.error('Supply to Aave error:', err);
        throw err;
      }
    },
    
    borrowFromAave: async (token: string, amount: string) => {
      try {
        await writeContract({
          address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
          abi: SIMPLE_LENDING_AGGREGATOR_ABI,
          functionName: 'borrowFromAave',
          args: [token, parseUnits(amount, 18)],
        });
      } catch (err) {
        console.error('Borrow from Aave error:', err);
        throw err;
      }
    },
    
    withdrawFromAave: async (token: string, amount: string) => {
      try {
        await writeContract({
          address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
          abi: SIMPLE_LENDING_AGGREGATOR_ABI,
          functionName: 'withdrawFromAave',
          args: [token, parseUnits(amount, 18)],
        });
      } catch (err) {
        console.error('Withdraw from Aave error:', err);
        throw err;
      }
    },
    
    repayToAave: async (token: string, amount: string) => {
      try {
        await writeContract({
          address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
          abi: SIMPLE_LENDING_AGGREGATOR_ABI,
          functionName: 'repayToAave',
          args: [token, parseUnits(amount, 18)],
        });
      } catch (err) {
        console.error('Repay to Aave error:', err);
        throw err;
      }
    },
    
    // Note: Morpho operations removed since SimpleLendingAggregator only supports Aave
    supplyToMorpho: async (token: string, amount: string) => {
      throw new Error('Morpho operations not supported in this contract');
    },
    borrowFromMorpho: async (token: string, amount: string, user: string) => {
      throw new Error('Morpho operations not supported in this contract');
    },
    withdrawFromMorpho: async (token: string, amount: string, user: string) => {
      throw new Error('Morpho operations not supported in this contract');
    },
    repayToMorpho: async (token: string, amount: string) => {
      throw new Error('Morpho operations not supported in this contract');
    }
  };
  
  const assetData = getAPYForAsset(selectedAsset);
  
  useEffect(() => {
    setAction(defaultAction);
  }, [defaultAction]);

  useEffect(() => {
    if (assetData) {
      // Auto-select best protocol based on action
      if (action === 'supply') {
        setProtocol(assetData.bestSupplyProtocol);
      } else if (action === 'borrow') {
        setProtocol(assetData.bestBorrowProtocol);
      }
    }
  }, [action, assetData]);

  const needsApproval = () => {
    if (!amount || (action !== 'supply' && action !== 'repay')) return false;
    return parseFloat(allowance.formatted) < parseFloat(amount);
  };

  const handleApprove = async () => {
    if (!amount) return;
    
    setError('');
    
    try {
      await operations.approveToken(tokenAddress, amount);
    } catch (err) {
      setError('Failed to approve token');
      console.error('Approval error:', err);
    }
  };

  const handleTransaction = async () => {
    if (!amount || !isConnected) return;
    
    setError('');
    
    // Validation checks
    if (!LENDING_APY_AGGREGATOR_ADDRESS || LENDING_APY_AGGREGATOR_ADDRESS.includes('YOUR_ACTUAL_CONTRACT_ADDRESS_HERE')) {
      setError('Contract address not configured. Please update .env.local with your deployed contract address.');
      return;
    }
    
    try {
      console.log('Transaction initiated:', {
        action,
        protocol,
        selectedAsset,
        tokenAddress,
        amount,
        contractAddress: LENDING_APY_AGGREGATOR_ADDRESS
      });

      switch (action) {
        case 'supply':
          if (protocol === 'aave') {
            await operations.supplyToAave(tokenAddress, amount);
          } else {
            setError('Morpho operations not supported with current contract');
          }
          break;
        case 'borrow':
          if (protocol === 'aave') {
            await operations.borrowFromAave(tokenAddress, amount);
          } else {
            setError('Morpho operations not supported with current contract');
          }
          break;
        case 'withdraw':
          if (protocol === 'aave') {
            await operations.withdrawFromAave(tokenAddress, amount);
          } else {
            setError('Morpho operations not supported with current contract');
          }
          break;
        case 'repay':
          if (protocol === 'aave') {
            await operations.repayToAave(tokenAddress, amount);
          } else {
            setError('Morpho operations not supported with current contract');
          }
          break;
      }
      
      // Reset form on success (after transaction is confirmed)
      if (isConfirmed) {
        setAmount('');
        onClose();
      }
    } catch (err: any) {
      console.error('Transaction error:', err);
      
      // Parse error messages for better UX
      let errorMessage = `Failed to ${action}`;
      if (err?.message?.includes('UnsupportedAsset')) {
        errorMessage = `${selectedAsset} is not supported by the contract. Please add it as a supported asset.`;
      } else if (err?.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds in your wallet.';
      } else if (err?.message?.includes('user rejected')) {
        errorMessage = 'Transaction rejected by user.';
      } else if (err?.shortMessage) {
        errorMessage = err.shortMessage;
      } else if (err?.message) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    }
  };

  const getAPYForCurrentSelection = () => {
    if (!assetData) return 0;
    
    if (action === 'supply' || action === 'withdraw') {
      return protocol === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY;
    } else {
      return protocol === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY;
    }
  };

  const getProtocolColor = (protocolName: 'aave' | 'morpho') => {
    return protocolName === 'aave' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            {action.charAt(0).toUpperCase() + action.slice(1)} {selectedAsset}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {!isConnected && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle size={16} className="text-yellow-600" />
                <span className="text-yellow-800">Please connect your wallet to continue</span>
              </div>
            </div>
          )}

          {/* Action Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Action</label>
            <div className="grid grid-cols-2 gap-2">
              {(['supply', 'borrow', 'withdraw', 'repay'] as const).map((actionType) => (
                <button
                  key={actionType}
                  onClick={() => setAction(actionType)}
                  className={`flex items-center justify-center space-x-2 py-3 px-4 rounded-lg border transition-colors ${
                    action === actionType
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  {actionType === 'supply' && <ArrowUpCircle size={16} />}
                  {actionType === 'borrow' && <ArrowDownCircle size={16} />}
                  {actionType === 'withdraw' && <RotateCcw size={16} />}
                  {actionType === 'repay' && <DollarSign size={16} />}
                  <span className="capitalize">{actionType}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Protocol Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Protocol</label>
            <div className="grid grid-cols-2 gap-2">
              {(['aave', 'morpho'] as const).map((protocolType) => (
                <button
                  key={protocolType}
                  onClick={() => setProtocol(protocolType)}
                  className={`py-3 px-4 rounded-lg border transition-colors ${
                    protocol === protocolType
                      ? `border-${protocolType === 'aave' ? 'blue' : 'purple'}-500 ${getProtocolColor(protocolType)}`
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="text-center">
                    <div className="font-medium capitalize">{protocolType}</div>
                    {assetData && (
                      <div className="text-sm">
                        {action === 'supply' || action === 'withdraw'
                          ? `${(protocolType === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY).toFixed(2)}% APY`
                          : `${(protocolType === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY).toFixed(2)}% APY`
                        }
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Amount Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Amount</label>
            <div className="relative">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                className="w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                disabled={!isConnected}
              />
              <div className="absolute right-3 top-3 text-gray-500">
                {selectedAsset}
              </div>
            </div>
            {isConnected && (
              <div className="mt-2 text-sm text-gray-600">
                Balance: {tokenBalance.formatted} {selectedAsset}
                {balance && (
                  <span className="ml-2">• ETH: {parseFloat(balance.formatted).toFixed(4)}</span>
                )}
              </div>
            )}
          </div>

          {/* APY Information */}
          {assetData && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Current APY</span>
                <span className="text-lg font-bold text-green-600">
                  {getAPYForCurrentSelection().toFixed(2)}%
                </span>
              </div>
              {protocol === 'morpho' && (action === 'supply' || action === 'borrow') && (
                <div className="mt-2 text-xs text-gray-500">
                  * Bridge fee required for Morpho transactions (~$2-5)
                </div>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle size={16} className="text-red-600" />
                <span className="text-red-800">{error}</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            {!isConnected ? (
              <div className="text-center space-y-4">
                <p className="text-gray-600">Connect your wallet to start trading</p>
                <CustomConnectButton />
              </div>
            ) : (
              <>
                {needsApproval() && (
                  <button
                    onClick={handleApprove}
                    disabled={isPending || isConfirming}
                    className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-300 text-white py-3 rounded-lg font-medium transition-colors"
                  >
                    {isPending || isConfirming ? 'Approving...' : `Approve ${selectedAsset}`}
                  </button>
                )}
                
                <button
                  onClick={handleTransaction}
                  disabled={isPending || isConfirming || !amount || needsApproval()}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-300 text-white py-3 rounded-lg font-medium transition-colors"
                >
                  {isPending ? 'Confirming...' : 
                   isConfirming ? 'Processing...' : 
                   `${action.charAt(0).toUpperCase() + action.slice(1)} ${selectedAsset}`}
                </button>
                
                {/* Transaction Status */}
                {hash && (
                  <div className="text-sm text-gray-600 text-center">
                    {isConfirming ? 'Waiting for confirmation...' : 
                     isConfirmed ? '✅ Transaction confirmed!' : 
                     '⏳ Transaction submitted'}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
