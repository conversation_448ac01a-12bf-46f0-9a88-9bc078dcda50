"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/services/aaveAPI.ts":
/*!*********************************!*\
  !*** ./app/services/aaveAPI.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchLiveAaveRates: () => (/* binding */ fetchLiveAaveRates),\n/* harmony export */   testAaveAPIConnection: () => (/* binding */ testAaveAPIConnection)\n/* harmony export */ });\n// Aave API service for fetching live rates\n// Documentation: https://docs.aave.com/developers/deployed-contracts/v3-mainnet\n// Avalanche Fuji testnet token addresses\nconst FUJI_TOKEN_ADDRESSES = {\n    USDC: '0x5425890298aed601595a70AB815c96711a31Bc65',\n    WAVAX: '0xd00ae08403B9bbb9124bB305C09058E32C39A48c',\n    USDT: '0x1f1E7c893855525b303f99bDF5c3c05BE09ca251'\n};\n// Aave V3 Subgraph for Avalanche Fuji (correct URL)\nconst AAVE_SUBGRAPH_URL = 'https://api.thegraph.com/subgraphs/name/aave/protocol-v3-avalanche';\n// Avalanche Fuji Pool Address (correct address)\nconst AAVE_POOL_ADDRESS = '0x794a61358D6845594F94dc1DB02A252b5b4814aD';\n// Convert Aave rate format (ray) to percentage\nfunction rayToPercentage(ray) {\n    const RAY = 10 ** 27;\n    const SECONDS_PER_YEAR = 31536000;\n    const ratePerSecond = parseInt(ray) / RAY;\n    const ratePerYear = ratePerSecond * SECONDS_PER_YEAR;\n    return ratePerYear * 100;\n}\n// Fetch reserve data from Aave subgraph with better error handling\nasync function fetchFromSubgraph() {\n    const query = '\\n    query GetReserves {\\n      reserves(\\n        where: {\\n          pool: \"0x794a61358d6845594f94dc1db02a252b5b4814ad\"\\n        }\\n        first: 10\\n      ) {\\n        id\\n        underlyingAsset\\n        name\\n        symbol\\n        decimals\\n        liquidityRate\\n        variableBorrowRate\\n        stableBorrowRate\\n        liquidityIndex\\n        variableBorrowIndex\\n        lastUpdateTimestamp\\n      }\\n    }\\n  ';\n    try {\n        var _data_data;\n        console.log('Attempting to fetch from subgraph:', AAVE_SUBGRAPH_URL);\n        const response = await fetch(AAVE_SUBGRAPH_URL, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            },\n            body: JSON.stringify({\n                query\n            })\n        });\n        console.log('Subgraph response status:', response.status);\n        console.log('Subgraph response headers:', response.headers);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Subgraph error response:', errorText);\n            throw new Error(\"Subgraph request failed: \".concat(response.status, \" - \").concat(errorText.substring(0, 200)));\n        }\n        const contentType = response.headers.get('content-type');\n        if (!contentType || !contentType.includes('application/json')) {\n            const responseText = await response.text();\n            console.error('Non-JSON response from subgraph:', responseText.substring(0, 500));\n            throw new Error(\"Expected JSON response but got: \".concat(contentType));\n        }\n        const data = await response.json();\n        console.log('Subgraph response data:', data);\n        if (data.errors) {\n            throw new Error(\"Subgraph errors: \".concat(JSON.stringify(data.errors)));\n        }\n        return ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.reserves) || [];\n    } catch (error) {\n        console.error('Error fetching from subgraph:', error);\n        throw error;\n    }\n}\n// Fallback: Use Aave's official API (if available for testnet)\nasync function fetchFromAaveAPI() {\n    // Note: Aave's official API might not support testnet\n    // This is a fallback that would work for mainnet\n    const url = 'https://aave-api-v2.aave.com/data/liquidity/v3?poolId=0x794a61358d6845594f94dc1db02a252b5b4814ad';\n    try {\n        const response = await fetch(url);\n        if (!response.ok) {\n            throw new Error(\"Aave API request failed: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data.reserves || [];\n    } catch (error) {\n        console.error('Error fetching from Aave API:', error);\n        throw error;\n    }\n}\n// Get live rates for supported tokens\nasync function fetchLiveAaveRates() {\n    try {\n        console.log('Fetching live Aave rates...');\n        // Try subgraph first, then fallback to API\n        let reserves;\n        try {\n            reserves = await fetchFromSubgraph();\n            console.log('Successfully fetched from subgraph');\n        } catch (subgraphError) {\n            console.log('Subgraph failed, trying Aave API...');\n            reserves = await fetchFromAaveAPI();\n            console.log('Successfully fetched from Aave API');\n        }\n        // Process the reserves data\n        const rates = {\n            USDC: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            },\n            WAVAX: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            },\n            USDT: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            }\n        };\n        reserves.forEach((reserve)=>{\n            const address = reserve.underlyingAsset.toLowerCase();\n            // Match by address\n            if (address === FUJI_TOKEN_ADDRESSES.USDC.toLowerCase()) {\n                rates.USDC.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.USDC.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            } else if (address === FUJI_TOKEN_ADDRESSES.WAVAX.toLowerCase()) {\n                rates.WAVAX.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.WAVAX.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            } else if (address === FUJI_TOKEN_ADDRESSES.USDT.toLowerCase()) {\n                rates.USDT.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.USDT.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            }\n        });\n        console.log('Live Aave rates:', rates);\n        return rates;\n    } catch (error) {\n        console.error('Failed to fetch live rates, using fallback:', error);\n        // Fallback to reasonable default rates if API fails\n        return {\n            USDC: {\n                supplyAPY: 4.25,\n                borrowAPY: 5.15\n            },\n            WAVAX: {\n                supplyAPY: 2.85,\n                borrowAPY: 4.25\n            },\n            USDT: {\n                supplyAPY: 4.15,\n                borrowAPY: 5.25\n            }\n        };\n    }\n}\n// Test function to verify API connectivity\nasync function testAaveAPIConnection() {\n    try {\n        await fetchLiveAaveRates();\n        return true;\n    } catch (error) {\n        console.error('Aave API connection test failed:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/services/aaveAPI.ts\n"));

/***/ })

});