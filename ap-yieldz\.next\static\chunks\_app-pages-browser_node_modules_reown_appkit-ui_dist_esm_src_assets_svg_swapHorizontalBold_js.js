"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   swapHorizontalBoldSvg: () => (/* binding */ swapHorizontalBoldSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst swapHorizontalBoldSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"10\" height=\"10\" viewBox=\"0 0 10 10\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M3.77986 0.566631C4.0589 0.845577 4.0589 1.29784 3.77986 1.57678L3.08261 2.2738H6.34184C6.73647 2.2738 7.05637 2.5936 7.05637 2.98808C7.05637 3.38257 6.73647 3.70237 6.34184 3.70237H3.08261L3.77986 4.39938C4.0589 4.67833 4.0589 5.13059 3.77986 5.40954C3.50082 5.68848 3.04841 5.68848 2.76937 5.40954L0.852346 3.49316C0.573306 3.21421 0.573306 2.76195 0.852346 2.48301L2.76937 0.566631C3.04841 0.287685 3.50082 0.287685 3.77986 0.566631ZM6.22 4.59102C6.49904 4.31208 6.95145 4.31208 7.23049 4.59102L9.14751 6.5074C9.42655 6.78634 9.42655 7.23861 9.14751 7.51755L7.23049 9.43393C6.95145 9.71287 6.49904 9.71287 6.22 9.43393C5.94096 9.15498 5.94096 8.70272 6.22 8.42377L6.91725 7.72676L3.65802 7.72676C3.26339 7.72676 2.94349 7.40696 2.94349 7.01247C2.94349 6.61798 3.26339 6.29819 3.65802 6.29819L6.91725 6.29819L6.22 5.60117C5.94096 5.32223 5.94096 4.86997 6.22 4.59102Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=swapHorizontalBold.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js\n"));

/***/ })

}]);