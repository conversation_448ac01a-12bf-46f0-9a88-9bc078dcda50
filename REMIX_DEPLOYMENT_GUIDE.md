# Deploy Aave Lending Aggregator using Remix IDE

## Step 1: Prepare for Deployment

### Get Testnet Funds
1. **Get Avalanche Fuji AVAX**: Visit [Avalanche Faucet](https://faucet.avax.network/)
2. **Add Avalanche Fuji to MetaMask**:
   - Network Name: Avalanche Fuji C-Chain
   - RPC URL: `https://api.avax-test.network/ext/bc/C/rpc`
   - Chain ID: `43113`
   - Currency Symbol: `AVAX`
   - Block Explorer: `https://testnet.snowtrace.io/`

## Step 2: Deploy Contract via Remix

### Open Remix IDE
1. Go to [https://remix.ethereum.org](https://remix.ethereum.org)
2. Create a new workspace or use the default

### Upload Contract
1. **Create new file**: `SimpleLendingAggregator.sol`
2. **Copy the contract code** from `src/SimpleLendingAggregator.sol`
3. **Paste it into Remix**

### Compile Contract
1. Go to **"Solidity Compiler"** tab (📄 icon)
2. Select compiler version: **0.8.20** or higher
3. Click **"Compile SimpleLendingAggregator.sol"**
4. Ensure compilation succeeds (green checkmark)

### Deploy Contract
1. Go to **"Deploy & Run Transactions"** tab (🚀 icon)
2. **Environment**: Select "Injected Provider - MetaMask"
3. **Connect MetaMask** and switch to Avalanche Fuji network
4. **Contract**: Select "SimpleLendingAggregator"
5. **Constructor Parameters**:
   - `_aavePool`: `******************************************`
   - `_owner`: Your wallet address (should auto-fill)
6. Click **"Deploy"**
7. **Confirm transaction** in MetaMask

## Step 3: Configure the Contract

After deployment, you'll see the contract in the "Deployed Contracts" section.

### Add Supported Assets
1. **Expand the deployed contract**
2. **Call `addSupportedAsset`** with these addresses:
   - USDC Fuji: `******************************************`
   - WAVAX Fuji: `******************************************`

### Verify Configuration
1. **Call `getSupportedAssets`** to verify assets were added
2. **Call `supportedAssets`** with each token address to confirm

## Step 4: Test Basic Functionality

### Get Test Tokens
You'll need test USDC to test the functionality:
1. **Option A**: Use Avalanche Fuji faucets if available
2. **Option B**: Swap some AVAX for USDC on a Fuji DEX
3. **Option C**: Use Aave Fuji faucet if available

### Test Supply Function
1. **First, approve the contract** to spend your USDC:
   - Go to USDC contract on [Fuji Explorer](https://testnet.snowtrace.io/)
   - Call `approve(spender, amount)` where:
     - `spender`: Your deployed contract address
     - `amount`: Amount to approve (e.g., `1000000` for 1 USDC with 6 decimals)

2. **Call `supplyToAave`**:
   - `asset`: USDC address (`******************************************`)
   - `amount`: Amount to supply (e.g., `1000000` for 1 USDC)

3. **Verify the supply**:
   - Call `getUserPosition(yourAddress, usdcAddress)`
   - Should show your supplied amount

## Step 5: Update Frontend Configuration

Once deployed and tested, update your frontend:

### Update Contract Address
In `ap-yieldz/app/blockchain/config/wagmi.ts`:
```typescript
export const LENDING_APY_AGGREGATOR_ADDRESS = "0xYOUR_DEPLOYED_CONTRACT_ADDRESS";
```

### Update ABI
Copy the ABI from Remix:
1. Go to **"Solidity Compiler"** tab
2. Click on **"SimpleLendingAggregator"** in artifacts
3. Copy the **ABI** array
4. Update `ap-yieldz/app/blockchain/abi/LendingAPYAggregator.ts`

### Update Token Addresses
In your config file, ensure you're using Fuji testnet addresses:
```typescript
export const SUPPORTED_TOKENS = {
  USDC: {
    address: "******************************************",
    symbol: "USDC",
    decimals: 6
  },
  WAVAX: {
    address: "******************************************",
    symbol: "WAVAX",
    decimals: 18
  }
};
```

## Step 6: Test End-to-End

### Start Frontend
```bash
cd ap-yieldz
npm install
npm run dev
```

### Test User Flow
1. **Connect wallet** to Avalanche Fuji
2. **View supported assets** in the UI
3. **Test supply operation** through the frontend
4. **Check position** in the dashboard

## Important Addresses for Reference

### Avalanche Fuji Testnet
- **Aave V3 Pool**: `******************************************`
- **USDC**: `******************************************`
- **WAVAX**: `******************************************`
- **Block Explorer**: https://testnet.snowtrace.io/

### Useful Links
- **Avalanche Faucet**: https://faucet.avax.network/
- **Fuji Explorer**: https://testnet.snowtrace.io/
- **Aave V3 Fuji**: https://app.aave.com/?marketName=proto_avalanche_v3

## Troubleshooting

### Common Issues:
1. **"Insufficient funds"**: Make sure you have AVAX for gas
2. **"Unsupported asset"**: Ensure you called `addSupportedAsset` first
3. **"Transfer amount exceeds allowance"**: Approve the contract first
4. **MetaMask issues**: Try refreshing or reconnecting

### Verification Steps:
1. **Check contract on explorer**: Verify deployment on Snowtrace
2. **Test each function**: Use Remix to test individual functions
3. **Check events**: Look for emitted events in transaction logs

## Next Steps

Once this basic version is working:
1. **Test all functions** (supply, borrow, withdraw, repay)
2. **Integrate with frontend** completely
3. **Add more sophisticated features**
4. **Consider mainnet deployment**

This simplified approach should get you up and running quickly for testing the core Aave functionality!
