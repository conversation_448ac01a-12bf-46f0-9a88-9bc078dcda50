# Alternative Deployment Methods (Without Foundry)

Since Foundry installation is having issues on this Windows environment, here are alternative approaches to deploy and test the Aave-only lending aggregator:

## Option 1: Use Remix IDE (Recommended for Quick Testing)

1. **Open Remix IDE**: Go to [https://remix.ethereum.org](https://remix.ethereum.org)

2. **Create a new workspace** and upload these files:
   - `src/LendingAPYAggregator.sol`
   - All files from `lib/openzeppelin-contracts/contracts/` (or use npm imports)
   - Aave V3 interfaces from `lib/aave-v3-core/`

3. **Install dependencies** in Remix:
   ```solidity
   // At the top of LendingAPYAggregator.sol, replace imports with:
   import "@openzeppelin/contracts/access/Ownable.sol";
   import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
   import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
   import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
   ```

4. **Compile and Deploy**:
   - Select Solidity compiler 0.8.20+
   - Compile the contract
   - Switch to "Deploy & Run" tab
   - Select "Injected Provider - MetaMask"
   - Connect to Avalanche Fuji testnet
   - Deploy with constructor parameters:
     - `_aavePool`: `******************************************`
     - `_owner`: Your wallet address

## Option 2: Use Hardhat

1. **Initialize Hardhat project**:
```bash
cd d:\Team-9-NightOfCode-
npm init -y
npm install --save-dev hardhat @nomiclabs/hardhat-ethers ethers
npm install @openzeppelin/contracts
npx hardhat
```

2. **Create hardhat.config.js**:
```javascript
require("@nomiclabs/hardhat-ethers");

module.exports = {
  solidity: "0.8.20",
  networks: {
    fuji: {
      url: "https://api.avax-test.network/ext/bc/C/rpc",
      accounts: [process.env.PRIVATE_KEY]
    }
  }
};
```

3. **Create deployment script** in `scripts/deploy.js`

## Option 3: Use Online Tools

### Tenderly
1. Go to [Tenderly](https://tenderly.co)
2. Create account and new project
3. Use their contract deployment feature

### ThirdWeb
1. Go to [ThirdWeb](https://thirdweb.com)
2. Use their contract deployment dashboard

## Option 4: Manual Contract Verification

Since our contract is relatively simple, let's verify the key components work:

### Contract Address References:
- **Aave V3 Pool (Fuji)**: `******************************************`
- **USDC (Fuji)**: `******************************************`
- **WAVAX (Fuji)**: `******************************************`

### Key Functions to Test:
1. `addSupportedAsset(address)` - Add USDC and WAVAX
2. `supplyToAave(address,uint256)` - Supply tokens to Aave
3. `borrowFromAave(address,uint256)` - Borrow from Aave
4. `getAggregatorUserPosition(address,address)` - Check positions

## Option 5: Use the Frontend for Testing

Since the frontend is already configured, we can:

1. **Deploy using Remix** (Option 1 above)
2. **Update frontend config** with the deployed contract address
3. **Test through the UI**

### Frontend Configuration Steps:

1. **Update contract address** in `ap-yieldz/app/blockchain/config/wagmi.ts`:
```typescript
export const LENDING_APY_AGGREGATOR_ADDRESS = "0x..." // Your deployed address
```

2. **Update token addresses** for Fuji testnet:
```typescript
export const SUPPORTED_TOKENS = {
  USDC: {
    address: "******************************************",
    symbol: "USDC",
    decimals: 6
  },
  WAVAX: {
    address: "******************************************",
    symbol: "WAVAX", 
    decimals: 18
  }
};
```

3. **Start the frontend**:
```bash
cd ap-yieldz
npm install
npm run dev
```

## Recommended Approach

**For immediate testing**: Use **Remix IDE** (Option 1) as it's the fastest way to deploy and test the contract.

**For production**: Set up Hardhat (Option 2) for better development workflow.

## Next Steps After Deployment

1. **Get testnet tokens** from Avalanche faucet
2. **Test basic functions** (add assets, supply, borrow)
3. **Integrate with frontend**
4. **Test end-to-end user flow**

Would you like me to help you with any of these specific deployment methods?
