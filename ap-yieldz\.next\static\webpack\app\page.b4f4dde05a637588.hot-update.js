"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/blockchain/config/wagmi.ts":
/*!****************************************!*\
  !*** ./app/blockchain/config/wagmi.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_NETWORK: () => (/* binding */ CURRENT_NETWORK),\n/* harmony export */   LENDING_APY_AGGREGATOR_ADDRESS: () => (/* binding */ LENDING_APY_AGGREGATOR_ADDRESS),\n/* harmony export */   SUPPORTED_TOKENS: () => (/* binding */ SUPPORTED_TOKENS),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getContractInfo: () => (/* binding */ getContractInfo),\n/* harmony export */   getCurrentTokens: () => (/* binding */ getCurrentTokens)\n/* harmony export */ });\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi/chains */ \"(app-pages-browser)/./node_modules/viem/_esm/chains/definitions/avalancheFuji.js\");\n/* harmony import */ var _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @rainbow-me/rainbowkit/wallets */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-RTDGOYZC.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/viem/_esm/clients/transports/http.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/cookie.js\");\n\n\n\n\nif (false) {}\nconst { wallets } = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultWallets)();\nconst config = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultConfig)({\n    appName: 'Alligator',\n    projectId: \"b11d3071871bb2c95781af8517fd1cfe\",\n    wallets: [\n        {\n            groupName: \"Core Wallet\",\n            wallets: [\n                _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__.injectedWallet\n            ]\n        }\n    ],\n    chains: [\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji\n    ],\n    transports: {\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_3__.http)(\"https://api.avax-test.network/ext/bc/C/rpc\" || 0)\n    },\n    ssr: true,\n    storage: (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.createStorage)({\n        storage: wagmi__WEBPACK_IMPORTED_MODULE_5__.cookieStorage\n    })\n});\n// Contract address - make sure this matches your deployed contract\nconst CONTRACT_ADDRESS = \"******************************************\" || 0;\nconst LENDING_APY_AGGREGATOR_ADDRESS = CONTRACT_ADDRESS;\n// Debug function to check contract\nconst getContractInfo = ()=>{\n    console.log('Contract Address:', LENDING_APY_AGGREGATOR_ADDRESS);\n    console.log('Environment Variable:', \"******************************************\");\n    return {\n        address: LENDING_APY_AGGREGATOR_ADDRESS,\n        envVar: \"******************************************\"\n    };\n};\nconst SUPPORTED_TOKENS = {\n    // Avalanche Fuji Testnet addresses for testing\n    fuji: {\n        USDC: '******************************************',\n        WAVAX: '******************************************',\n        USDT: '******************************************'\n    },\n    // Avalanche Mainnet addresses (for future use)\n    avalanche: {\n        USDC: '******************************************',\n        USDT: '******************************************',\n        WETH: '******************************************',\n        WBTC: '******************************************',\n        WAVAX: '******************************************'\n    },\n    // Base addresses (for future Morpho integration)\n    base: {\n        USDC: '******************************************',\n        WETH: '******************************************'\n    }\n};\n// Current network configuration - change this to switch between testnet and mainnet\nconst CURRENT_NETWORK = 'fuji'; // 'fuji' for testnet, 'avalanche' for mainnet\n// Get tokens for current network\nconst getCurrentTokens = ()=>SUPPORTED_TOKENS[CURRENT_NETWORK];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blockchain/config/wagmi.ts\n"));

/***/ })

});