// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {LendingAPYAggregator} from "../../src/LendingAPYAggregator.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract AaveOnlyTest is Test {
    LendingAPYAggregator public aggregator;
    
    // Avalanche Fuji testnet addresses
    address constant AAVE_POOL = 0x7d2768dE32b0b80b7a3454c06BdAc94A69DDc7A9;
    address constant USDC = 0x5425890298aed601595a70AB815c96711a31Bc65; // Fuji USDC
    address constant WAVAX = 0xd00ae08403B9bbb9124bB305C09058E32C39A48c; // Fuji WAVAX
    
    address public owner = makeAddr("owner");
    address public user = makeAddr("user");
    
    function setUp() public {
        vm.createFork("https://api.avax-test.network/ext/bc/C/rpc");
        
        vm.startPrank(owner);
        aggregator = new LendingAPYAggregator(AAVE_POOL, owner);
        
        // Add supported assets
        aggregator.addSupportedAsset(USDC);
        aggregator.addSupportedAsset(WAVAX);
        vm.stopPrank();
    }
    
    function testDeployment() public {
        assertEq(address(aggregator.aavePool()), AAVE_POOL);
        assertEq(aggregator.owner(), owner);
        assertTrue(aggregator.supportedAssets(USDC));
        assertTrue(aggregator.supportedAssets(WAVAX));
    }
    
    function testGetSupportedAssets() public {
        address[] memory assets = aggregator.getSupportedAssets();
        assertEq(assets.length, 2);
        assertEq(assets[0], USDC);
        assertEq(assets[1], WAVAX);
    }
    
    function testUserPositionInitiallyZero() public {
        (
            uint256 aaveSupplied,
            uint256 aaveBorrowed,
            uint256 morphoSupplied,
            uint256 morphoBorrowed,
            uint256 lastUpdate
        ) = aggregator.getAggregatorUserPosition(user, USDC);
        
        assertEq(aaveSupplied, 0);
        assertEq(aaveBorrowed, 0);
        assertEq(morphoSupplied, 0);
        assertEq(morphoBorrowed, 0);
        assertEq(lastUpdate, 0);
    }
    
    function testOnlyOwnerCanAddAssets() public {
        address newAsset = makeAddr("newAsset");
        
        vm.prank(user);
        vm.expectRevert();
        aggregator.addSupportedAsset(newAsset);
        
        vm.prank(owner);
        aggregator.addSupportedAsset(newAsset);
        assertTrue(aggregator.supportedAssets(newAsset));
    }
    
    function testSupplyToAaveRevertsWithUnsupportedAsset() public {
        address unsupportedAsset = makeAddr("unsupported");
        
        vm.prank(user);
        vm.expectRevert();
        aggregator.supplyToAave(unsupportedAsset, 1000);
    }
    
    function testSupplyToAaveRevertsWithZeroAmount() public {
        vm.prank(user);
        vm.expectRevert();
        aggregator.supplyToAave(USDC, 0);
    }
}
