"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_safe-global_safe-apps-sdk_dist_esm_index_js"],{

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.RestrictedMethods),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messageFormatter.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./methods.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PostMessageCommunicator);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* binding */ MessageFormatter)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\");\n\n\nclass MessageFormatter {\n}\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0,_version_js__WEBPACK_IMPORTED_MODULE_0__.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9jb21tdW5pY2F0aW9uL21lc3NhZ2VGb3JtYXR0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBQ0M7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsZUFBZSw0REFBaUI7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QiwwREFBYTtBQUNyQyxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDMkI7QUFDNUIiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXGNvbW11bmljYXRpb25cXG1lc3NhZ2VGb3JtYXR0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U0RLVmVyc2lvbiB9IGZyb20gJy4uL3ZlcnNpb24uanMnO1xuaW1wb3J0IHsgZ2VuZXJhdGVSZXF1ZXN0SWQgfSBmcm9tICcuL3V0aWxzLmpzJztcbmNsYXNzIE1lc3NhZ2VGb3JtYXR0ZXIge1xufVxuTWVzc2FnZUZvcm1hdHRlci5tYWtlUmVxdWVzdCA9IChtZXRob2QsIHBhcmFtcykgPT4ge1xuICAgIGNvbnN0IGlkID0gZ2VuZXJhdGVSZXF1ZXN0SWQoKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBpZCxcbiAgICAgICAgbWV0aG9kLFxuICAgICAgICBwYXJhbXMsXG4gICAgICAgIGVudjoge1xuICAgICAgICAgICAgc2RrVmVyc2lvbjogZ2V0U0RLVmVyc2lvbigpLFxuICAgICAgICB9LFxuICAgIH07XG59O1xuTWVzc2FnZUZvcm1hdHRlci5tYWtlUmVzcG9uc2UgPSAoaWQsIGRhdGEsIHZlcnNpb24pID0+ICh7XG4gICAgaWQsXG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICB2ZXJzaW9uLFxuICAgIGRhdGEsXG59KTtcbk1lc3NhZ2VGb3JtYXR0ZXIubWFrZUVycm9yUmVzcG9uc2UgPSAoaWQsIGVycm9yLCB2ZXJzaW9uKSA9PiAoe1xuICAgIGlkLFxuICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgIGVycm9yLFxuICAgIHZlcnNpb24sXG59KTtcbmV4cG9ydCB7IE1lc3NhZ2VGb3JtYXR0ZXIgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2VGb3JtYXR0ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* binding */ Methods),\n/* harmony export */   RestrictedMethods: () => (/* binding */ RestrictedMethods)\n/* harmony export */ });\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateRequestId: () => (/* binding */ generateRequestId)\n/* harmony export */ });\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9jb21tdW5pY2F0aW9uL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUM2QjtBQUM3QiIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcY29tbXVuaWNhdGlvblxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gaS5lLiAwLTI1NSAtPiAnMDAnLSdmZidcbmNvbnN0IGRlYzJoZXggPSAoZGVjKSA9PiBkZWMudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyk7XG5jb25zdCBnZW5lcmF0ZUlkID0gKGxlbikgPT4ge1xuICAgIGNvbnN0IGFyciA9IG5ldyBVaW50OEFycmF5KChsZW4gfHwgNDApIC8gMik7XG4gICAgd2luZG93LmNyeXB0by5nZXRSYW5kb21WYWx1ZXMoYXJyKTtcbiAgICByZXR1cm4gQXJyYXkuZnJvbShhcnIsIGRlYzJoZXgpLmpvaW4oJycpO1xufTtcbmNvbnN0IGdlbmVyYXRlUmVxdWVzdElkID0gKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICByZXR1cm4gZ2VuZXJhdGVJZCgxMCk7XG4gICAgfVxuICAgIHJldHVybiBuZXcgRGF0ZSgpLmdldFRpbWUoKS50b1N0cmluZygzNik7XG59O1xuZXhwb3J0IHsgZ2VuZXJhdGVSZXF1ZXN0SWQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wallet/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (requirePermission);\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RPC_CALLS: () => (/* binding */ RPC_CALLS)\n/* harmony export */ });\nconst RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9ldGgvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcZXRoXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFJQQ19DQUxMUyA9IHtcbiAgICBldGhfY2FsbDogJ2V0aF9jYWxsJyxcbiAgICBldGhfZ2FzUHJpY2U6ICdldGhfZ2FzUHJpY2UnLFxuICAgIGV0aF9nZXRMb2dzOiAnZXRoX2dldExvZ3MnLFxuICAgIGV0aF9nZXRCYWxhbmNlOiAnZXRoX2dldEJhbGFuY2UnLFxuICAgIGV0aF9nZXRDb2RlOiAnZXRoX2dldENvZGUnLFxuICAgIGV0aF9nZXRCbG9ja0J5SGFzaDogJ2V0aF9nZXRCbG9ja0J5SGFzaCcsXG4gICAgZXRoX2dldEJsb2NrQnlOdW1iZXI6ICdldGhfZ2V0QmxvY2tCeU51bWJlcicsXG4gICAgZXRoX2dldFN0b3JhZ2VBdDogJ2V0aF9nZXRTdG9yYWdlQXQnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvbkJ5SGFzaDogJ2V0aF9nZXRUcmFuc2FjdGlvbkJ5SGFzaCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uUmVjZWlwdDogJ2V0aF9nZXRUcmFuc2FjdGlvblJlY2VpcHQnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvbkNvdW50OiAnZXRoX2dldFRyYW5zYWN0aW9uQ291bnQnLFxuICAgIGV0aF9lc3RpbWF0ZUdhczogJ2V0aF9lc3RpbWF0ZUdhcycsXG4gICAgc2FmZV9zZXRTZXR0aW5nczogJ3NhZmVfc2V0U2V0dGluZ3MnLFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eth: () => (/* binding */ Eth)\n/* harmony export */ });\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../eth/constants.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* reexport safe */ _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__.MessageFormatter),\n/* harmony export */   Methods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.Methods),\n/* harmony export */   Operation: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.Operation),\n/* harmony export */   RPC_CALLS: () => (/* reexport safe */ _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__.RPC_CALLS),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.RestrictedMethods),\n/* harmony export */   TokenType: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransferDirection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSDKVersion: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_4__.getSDKVersion),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./communication/methods.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communication/messageFormatter.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./eth/constants.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_sdk_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEyQjtBQUMzQixpRUFBZSwrQ0FBRyxFQUFDO0FBQ007QUFDUTtBQUNVO0FBQ1M7QUFDUDtBQUNWO0FBQ25DIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU0RLIGZyb20gJy4vc2RrLmpzJztcbmV4cG9ydCBkZWZhdWx0IFNESztcbmV4cG9ydCAqIGZyb20gJy4vc2RrLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMvaW5kZXguanMnO1xuZXhwb3J0ICogZnJvbSAnLi9jb21tdW5pY2F0aW9uL21ldGhvZHMuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9jb21tdW5pY2F0aW9uL21lc3NhZ2VGb3JtYXR0ZXIuanMnO1xuZXhwb3J0IHsgZ2V0U0RLVmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbi5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2V0aC9jb25zdGFudHMuanMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Safe: () => (/* binding */ Safe)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeFunctionData.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/signature/hashMessage.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/signature/hashTypedData.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../eth/constants.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../types/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../decorators/requirePermissions.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\");\nvar __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\n\n\n\n\n\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0,viem__WEBPACK_IMPORTED_MODULE_6__.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0,viem__WEBPACK_IMPORTED_MODULE_7__.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0,_types_index_js__WEBPACK_IMPORTED_MODULE_3__.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\n__decorate([\n    (0,_decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n], Safe.prototype, \"requestAddressBook\", null);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAGIC_VALUE: () => (/* binding */ MAGIC_VALUE),\n/* harmony export */   MAGIC_VALUE_BYTES: () => (/* binding */ MAGIC_VALUE_BYTES)\n/* harmony export */ });\nconst MAGIC_VALUE = '0x1626ba7e';\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\n\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9zYWZlL3NpZ25hdHVyZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQzBDO0FBQzFDIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFxzYWZlXFxzaWduYXR1cmVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IE1BR0lDX1ZBTFVFID0gJzB4MTYyNmJhN2UnO1xuY29uc3QgTUFHSUNfVkFMVUVfQllURVMgPSAnMHgyMGMxM2IwYic7XG5leHBvcnQgeyBNQUdJQ19WQUxVRSwgTUFHSUNfVkFMVUVfQllURVMgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpZ25hdHVyZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _communication_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./communication/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\");\n/* harmony import */ var _txs_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./txs/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\");\n/* harmony import */ var _eth_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./eth/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\");\n/* harmony import */ var _safe_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./safe/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\");\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./wallet/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n\n\n\n\n\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new _communication_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](allowedDomains, debug);\n        this.eth = new _eth_index_js__WEBPACK_IMPORTED_MODULE_2__.Eth(this.communicator);\n        this.txs = new _txs_index_js__WEBPACK_IMPORTED_MODULE_1__.TXs(this.communicator);\n        this.safe = new _safe_index_js__WEBPACK_IMPORTED_MODULE_3__.Safe(this.communicator);\n        this.wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__.Wallet(this.communicator);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeAppsSDK);\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9zZGsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBQ3hCO0FBQ0E7QUFDRTtBQUNJO0FBQzNDO0FBQ0EseUJBQXlCO0FBQ3pCLGdCQUFnQix1Q0FBdUM7QUFDdkQsZ0NBQWdDLCtEQUFxQjtBQUNyRCx1QkFBdUIsOENBQUc7QUFDMUIsdUJBQXVCLDhDQUFHO0FBQzFCLHdCQUF3QixnREFBSTtBQUM1QiwwQkFBMEIsb0RBQU07QUFDaEM7QUFDQTtBQUNBLGlFQUFlLFdBQVcsRUFBQztBQUMzQiIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcc2RrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBJbnRlcmZhY2VDb21tdW5pY2F0b3IgZnJvbSAnLi9jb21tdW5pY2F0aW9uL2luZGV4LmpzJztcbmltcG9ydCB7IFRYcyB9IGZyb20gJy4vdHhzL2luZGV4LmpzJztcbmltcG9ydCB7IEV0aCB9IGZyb20gJy4vZXRoL2luZGV4LmpzJztcbmltcG9ydCB7IFNhZmUgfSBmcm9tICcuL3NhZmUvaW5kZXguanMnO1xuaW1wb3J0IHsgV2FsbGV0IH0gZnJvbSAnLi93YWxsZXQvaW5kZXguanMnO1xuY2xhc3MgU2FmZUFwcHNTREsge1xuICAgIGNvbnN0cnVjdG9yKG9wdHMgPSB7fSkge1xuICAgICAgICBjb25zdCB7IGFsbG93ZWREb21haW5zID0gbnVsbCwgZGVidWcgPSBmYWxzZSB9ID0gb3B0cztcbiAgICAgICAgdGhpcy5jb21tdW5pY2F0b3IgPSBuZXcgSW50ZXJmYWNlQ29tbXVuaWNhdG9yKGFsbG93ZWREb21haW5zLCBkZWJ1Zyk7XG4gICAgICAgIHRoaXMuZXRoID0gbmV3IEV0aCh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgICAgIHRoaXMudHhzID0gbmV3IFRYcyh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgICAgIHRoaXMuc2FmZSA9IG5ldyBTYWZlKHRoaXMuY29tbXVuaWNhdG9yKTtcbiAgICAgICAgdGhpcy53YWxsZXQgPSBuZXcgV2FsbGV0KHRoaXMuY29tbXVuaWNhdG9yKTtcbiAgICB9XG59XG5leHBvcnQgZGVmYXVsdCBTYWZlQXBwc1NESztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNkay5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TXs: () => (/* binding */ TXs)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/index.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n\n\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0,_types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransferDirection)\n/* harmony export */ });\n/* harmony import */ var _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\n\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9nYXRld2F5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXVIO0FBQ3ZIIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx0eXBlc1xcZ2F0ZXdheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBPcGVyYXRpb24sIFRva2VuVHlwZSwgVHJhbnNhY3Rpb25TdGF0dXMsIFRyYW5zZmVyRGlyZWN0aW9uLCB9IGZyb20gJ0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2F0ZXdheS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransferDirection),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _sdk_js__WEBPACK_IMPORTED_MODULE_0__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\");\n/* harmony import */ var _rpc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rpc.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\");\n/* harmony import */ var _gateway_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gateway.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\");\n/* harmony import */ var _messaging_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messaging.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBeUI7QUFDQTtBQUNJO0FBQ0U7QUFDL0IiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHR5cGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3Nkay5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3JwYy5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2dhdGV3YXkuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9tZXNzYWdpbmcuanMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9tZXNzYWdpbmcuanMiLCJtYXBwaW5ncyI6Ijs7QUFBc0Q7QUFDdEQiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHR5cGVzXFxtZXNzYWdpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTWV0aG9kcyB9IGZyb20gJy4uL2NvbW11bmljYXRpb24vbWV0aG9kcy5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXNzYWdpbmcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS_REQUEST_REJECTED: () => (/* binding */ PERMISSIONS_REQUEST_REJECTED),\n/* harmony export */   PermissionsError: () => (/* binding */ PermissionsError)\n/* harmony export */ });\nconst PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9wZXJtaXNzaW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcdHlwZXNcXHBlcm1pc3Npb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBQRVJNSVNTSU9OU19SRVFVRVNUX1JFSkVDVEVEID0gNDAwMTtcbmV4cG9ydCBjbGFzcyBQZXJtaXNzaW9uc0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIGNvZGUsIGRhdGEpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMuY29kZSA9IGNvZGU7XG4gICAgICAgIHRoaXMuZGF0YSA9IGRhdGE7XG4gICAgICAgIC8vIFNob3VsZCBhZGp1c3QgcHJvdG90eXBlIG1hbnVhbGx5IGJlY2F1c2UgaG93IFRTIGhhbmRsZXMgdGhlIHR5cGUgZXh0ZW5zaW9uIGNvbXBpbGF0aW9uXG4gICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9NaWNyb3NvZnQvVHlwZVNjcmlwdC93aWtpL0JyZWFraW5nLUNoYW5nZXMjZXh0ZW5kaW5nLWJ1aWx0LWlucy1saWtlLWVycm9yLWFycmF5LWFuZC1tYXAtbWF5LW5vLWxvbmdlci13b3JrXG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBQZXJtaXNzaW9uc0Vycm9yLnByb3RvdHlwZSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGVybWlzc2lvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9ycGMuanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHR5cGVzXFxycGMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cnBjLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectEIP712TypedData: () => (/* binding */ isObjectEIP712TypedData)\n/* harmony export */ });\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9zZGsuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx0eXBlc1xcc2RrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc09iamVjdEVJUDcxMlR5cGVkRGF0YSA9IChvYmopID0+IHtcbiAgICByZXR1cm4gdHlwZW9mIG9iaiA9PT0gJ29iamVjdCcgJiYgb2JqICE9IG51bGwgJiYgJ2RvbWFpbicgaW4gb2JqICYmICd0eXBlcycgaW4gb2JqICYmICdtZXNzYWdlJyBpbiBvYmo7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2RrLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSDKVersion: () => (/* binding */ getSDKVersion)\n/* harmony export */ });\nconst getSDKVersion = () => '9.1.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRTREtWZXJzaW9uID0gKCkgPT4gJzkuMS4wJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Wallet: () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions request is invalid', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_BASE_URL = void 0;\nexports.DEFAULT_BASE_URL = 'https://safe-client.safe.global';\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvY29uZmlnLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHdCQUF3QjtBQUN4Qix3QkFBd0I7QUFDeEIiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcXGRpc3RcXGNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuREVGQVVMVF9CQVNFX1VSTCA9IHZvaWQgMDtcbmV4cG9ydHMuREVGQVVMVF9CQVNFX1VSTCA9ICdodHRwczovL3NhZmUtY2xpZW50LnNhZmUuZ2xvYmFsJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbmZpZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.postEndpoint = postEndpoint;\nexports.putEndpoint = putEndpoint;\nexports.deleteEndpoint = deleteEndpoint;\nexports.getEndpoint = getEndpoint;\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\");\nfunction makeUrl(baseUrl, path, pathParams, query) {\n    const pathname = (0, utils_1.insertParams)(path, pathParams);\n    const search = (0, utils_1.stringifyQuery)(query);\n    return `${baseUrl}${pathname}${search}`;\n}\nfunction postEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'POST', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction putEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'PUT', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction deleteEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'DELETE', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction getEndpoint(baseUrl, path, params, rawUrl) {\n    if (rawUrl) {\n        return (0, utils_1.getData)(rawUrl, undefined, params === null || params === void 0 ? void 0 : params.credentials);\n    }\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.getData)(url, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\n//# sourceMappingURL=endpoint.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setBaseUrl = void 0;\nexports.relayTransaction = relayTransaction;\nexports.getRelayCount = getRelayCount;\nexports.getSafeInfo = getSafeInfo;\nexports.getIncomingTransfers = getIncomingTransfers;\nexports.getModuleTransactions = getModuleTransactions;\nexports.getMultisigTransactions = getMultisigTransactions;\nexports.getBalances = getBalances;\nexports.getFiatCurrencies = getFiatCurrencies;\nexports.getOwnedSafes = getOwnedSafes;\nexports.getAllOwnedSafes = getAllOwnedSafes;\nexports.getCollectibles = getCollectibles;\nexports.getCollectiblesPage = getCollectiblesPage;\nexports.getTransactionHistory = getTransactionHistory;\nexports.getTransactionQueue = getTransactionQueue;\nexports.getTransactionDetails = getTransactionDetails;\nexports.deleteTransaction = deleteTransaction;\nexports.postSafeGasEstimation = postSafeGasEstimation;\nexports.getNonces = getNonces;\nexports.proposeTransaction = proposeTransaction;\nexports.getConfirmationView = getConfirmationView;\nexports.getTxPreview = getTxPreview;\nexports.getChainsConfig = getChainsConfig;\nexports.getChainConfig = getChainConfig;\nexports.getSafeApps = getSafeApps;\nexports.getMasterCopies = getMasterCopies;\nexports.getDecodedData = getDecodedData;\nexports.getSafeMessages = getSafeMessages;\nexports.getSafeMessage = getSafeMessage;\nexports.proposeSafeMessage = proposeSafeMessage;\nexports.confirmSafeMessage = confirmSafeMessage;\nexports.getDelegates = getDelegates;\nexports.registerDevice = registerDevice;\nexports.unregisterSafe = unregisterSafe;\nexports.unregisterDevice = unregisterDevice;\nexports.registerEmail = registerEmail;\nexports.changeEmail = changeEmail;\nexports.resendEmailVerificationCode = resendEmailVerificationCode;\nexports.verifyEmail = verifyEmail;\nexports.getRegisteredEmail = getRegisteredEmail;\nexports.deleteRegisteredEmail = deleteRegisteredEmail;\nexports.registerRecoveryModule = registerRecoveryModule;\nexports.unsubscribeSingle = unsubscribeSingle;\nexports.unsubscribeAll = unsubscribeAll;\nexports.getSafeOverviews = getSafeOverviews;\nexports.getContract = getContract;\nexports.getAuthNonce = getAuthNonce;\nexports.verifyAuth = verifyAuth;\nexports.createAccount = createAccount;\nexports.getAccount = getAccount;\nexports.deleteAccount = deleteAccount;\nexports.getAccountDataTypes = getAccountDataTypes;\nexports.getAccountDataSettings = getAccountDataSettings;\nexports.putAccountDataSettings = putAccountDataSettings;\nexports.getIndexingStatus = getIndexingStatus;\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\");\nconst config_1 = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\");\n__exportStar(__webpack_require__(/*! ./types/safe-info */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-apps */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/transactions */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/chains */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/common */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/master-copies */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/decoded-data */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-messages */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/notifications */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/relay */ \"(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\"), exports);\n// Can be set externally to a different CGW host\nlet baseUrl = config_1.DEFAULT_BASE_URL;\n/**\n * Set the base CGW URL\n */\nconst setBaseUrl = (url) => {\n    baseUrl = url;\n};\nexports.setBaseUrl = setBaseUrl;\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Relay a transaction from a Safe\n */\nfunction relayTransaction(chainId, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/relay', { path: { chainId }, body });\n}\n/**\n * Get the relay limit and number of remaining relays remaining\n */\nfunction getRelayCount(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/relay/{address}', { path: { chainId, address } });\n}\n/**\n * Get basic information about a Safe. E.g. owners, modules, version etc\n */\nfunction getSafeInfo(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}', { path: { chainId, address } });\n}\n/**\n * Get filterable list of incoming transactions\n */\nfunction getIncomingTransfers(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/incoming-transfers/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of module transactions\n */\nfunction getModuleTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/module-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of multisig transactions\n */\nfunction getMultisigTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/multisig-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get the total balance and all assets stored in a Safe\n */\nfunction getBalances(chainId, address, currency = 'usd', query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/balances/{currency}', {\n        path: { chainId, address, currency },\n        query,\n    });\n}\n/**\n * Get a list of supported fiat currencies (e.g. USD, EUR etc)\n */\nfunction getFiatCurrencies() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/balances/supported-fiat-codes');\n}\n/**\n * Get the addresses of all Safes belonging to an owner\n */\nfunction getOwnedSafes(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/owners/{address}/safes', { path: { chainId, address } });\n}\n/**\n * Get the addresses of all Safes belonging to an owner on all chains\n */\nfunction getAllOwnedSafes(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/owners/{address}/safes', { path: { address } });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectibles(chainId, address, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/collectibles', {\n        path: { chainId, address },\n        query,\n    });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectiblesPage(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{address}/collectibles', { path: { chainId, address }, query }, pageUrl);\n}\n/**\n * Get a list of past Safe transactions\n */\nfunction getTransactionHistory(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/history', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the list of pending transactions\n */\nfunction getTransactionQueue(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/queued', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the details of an individual transaction by its id\n */\nfunction getTransactionDetails(chainId, transactionId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{transactionId}', {\n        path: { chainId, transactionId },\n    });\n}\n/**\n * Delete a transaction by its safeTxHash\n */\nfunction deleteTransaction(chainId, safeTxHash, signature) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safeTxHash}', {\n        path: { chainId, safeTxHash },\n        body: { signature },\n    });\n}\n/**\n * Request a gas estimate & recommmended tx nonce for a created transaction\n */\nfunction postSafeGasEstimation(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{safe_address}/multisig-transactions/estimations', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nfunction getNonces(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/nonces', {\n        path: { chainId, safe_address: address },\n    });\n}\n/**\n * Propose a new transaction for other owners to sign/execute\n */\nfunction proposeTransaction(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/propose', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getConfirmationView(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/views/transaction-confirmation', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Get a tx preview\n */\nfunction getTxPreview(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/preview', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Returns all defined chain configs\n */\nfunction getChainsConfig(query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains', {\n        query,\n    });\n}\n/**\n * Returns a chain config\n */\nfunction getChainConfig(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns Safe Apps List\n */\nfunction getSafeApps(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safe-apps', {\n        path: { chainId: chainId },\n        query,\n    });\n}\n/**\n * Returns list of Master Copies\n */\nfunction getMasterCopies(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/master-copies', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getDecodedData(chainId, operation, encodedData, to) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/data-decoder', {\n        path: { chainId: chainId },\n        body: { operation, data: encodedData, to },\n    });\n}\n/**\n * Returns list of `SafeMessage`s\n */\nfunction getSafeMessages(chainId, address, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', { path: { chainId, safe_address: address }, query: {} }, pageUrl);\n}\n/**\n * Returns a `SafeMessage`\n */\nfunction getSafeMessage(chainId, messageHash) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}', {\n        path: { chainId, message_hash: messageHash },\n    });\n}\n/**\n * Propose a new `SafeMessage` for other owners to sign\n */\nfunction proposeSafeMessage(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Add a confirmation to a `SafeMessage`\n */\nfunction confirmSafeMessage(chainId, messageHash, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}/signatures', {\n        path: { chainId, message_hash: messageHash },\n        body,\n    });\n}\n/**\n * Returns a list of delegates\n */\nfunction getDelegates(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/delegates', {\n        path: { chainId },\n        query,\n    });\n}\n/**\n * Registers a device/Safe for notifications\n */\nfunction registerDevice(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/register/notifications', {\n        body,\n    });\n}\n/**\n * Unregisters a Safe from notifications\n */\nfunction unregisterSafe(chainId, address, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}/safes/{safe_address}', {\n        path: { chainId, safe_address: address, uuid },\n    });\n}\n/**\n * Unregisters a device from notifications\n */\nfunction unregisterDevice(chainId, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}', {\n        path: { chainId, uuid },\n    });\n}\n/**\n * Registers a email address for a safe signer.\n *\n * The signer wallet has to sign a message of format: `email-register-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param body Signer address and email address\n * @param headers Signature and Signature timestamp\n * @returns 200 if signature matches the data\n */\nfunction registerEmail(chainId, safeAddress, body, headers) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Changes an already registered email address for a safe signer. The new email address still needs to be verified.\n *\n * The signer wallet has to sign a message of format: `email-edit-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param body New email address\n * @param headers Signature and Signature timestamp\n * @returns 202 if signature matches the data\n */\nfunction changeEmail(chainId, safeAddress, signerAddress, body, headers) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Resends an email verification code.\n */\nfunction resendEmailVerificationCode(chainId, safeAddress, signerAddress) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify-resend', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body: '',\n    });\n}\n/**\n * Verifies a pending email address registration.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address who signed the email registration\n * @param body Verification code\n */\nfunction verifyEmail(chainId, safeAddress, signerAddress, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n    });\n}\n/**\n * Gets the registered email address of the signer\n *\n * The signer wallet will have to sign a message of format: `email-retrieval-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address of the owner of the Safe\n *\n * @returns email address and verified flag\n */\nfunction getRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Delete a registered email address for the signer\n *\n * The signer wallet will have to sign a message of format: `email-delete-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param headers\n */\nfunction deleteRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Register a recovery module for receiving alerts\n * @param chainId\n * @param safeAddress\n * @param body - { moduleAddress: string }\n */\nfunction registerRecoveryModule(chainId, safeAddress, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/recovery', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n    });\n}\n/**\n * Delete email subscription for a single category\n * @param query\n */\nfunction unsubscribeSingle(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions', { query });\n}\n/**\n * Delete email subscription for all categories\n * @param query\n */\nfunction unsubscribeAll(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions/all', { query });\n}\n/**\n * Get Safe overviews per address\n */\nfunction getSafeOverviews(safes, query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/safes', {\n        query: Object.assign(Object.assign({}, query), { safes: safes.join(',') }),\n    });\n}\nfunction getContract(chainId, contractAddress) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/contracts/{contractAddress}', {\n        path: {\n            chainId: chainId,\n            contractAddress: contractAddress,\n        },\n    });\n}\nfunction getAuthNonce() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/auth/nonce', { credentials: 'include' });\n}\nfunction verifyAuth(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/auth/verify', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction createAccount(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/accounts', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction getAccount(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction deleteAccount(address) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction getAccountDataTypes() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/data-types');\n}\nfunction getAccountDataSettings(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction putAccountDataSettings(address, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        body,\n        credentials: 'include',\n    });\n}\nfunction getIndexingStatus(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/indexing', {\n        path: { chainId },\n    });\n}\n/* eslint-enable @typescript-eslint/explicit-module-boundary-types */\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FEATURES = exports.GAS_PRICE_TYPE = exports.RPC_AUTHENTICATION = void 0;\nvar RPC_AUTHENTICATION;\n(function (RPC_AUTHENTICATION) {\n    RPC_AUTHENTICATION[\"API_KEY_PATH\"] = \"API_KEY_PATH\";\n    RPC_AUTHENTICATION[\"NO_AUTHENTICATION\"] = \"NO_AUTHENTICATION\";\n    RPC_AUTHENTICATION[\"UNKNOWN\"] = \"UNKNOWN\";\n})(RPC_AUTHENTICATION || (exports.RPC_AUTHENTICATION = RPC_AUTHENTICATION = {}));\nvar GAS_PRICE_TYPE;\n(function (GAS_PRICE_TYPE) {\n    GAS_PRICE_TYPE[\"ORACLE\"] = \"ORACLE\";\n    GAS_PRICE_TYPE[\"FIXED\"] = \"FIXED\";\n    GAS_PRICE_TYPE[\"FIXED_1559\"] = \"FIXED1559\";\n    GAS_PRICE_TYPE[\"UNKNOWN\"] = \"UNKNOWN\";\n})(GAS_PRICE_TYPE || (exports.GAS_PRICE_TYPE = GAS_PRICE_TYPE = {}));\nvar FEATURES;\n(function (FEATURES) {\n    FEATURES[\"ERC721\"] = \"ERC721\";\n    FEATURES[\"SAFE_APPS\"] = \"SAFE_APPS\";\n    FEATURES[\"CONTRACT_INTERACTION\"] = \"CONTRACT_INTERACTION\";\n    FEATURES[\"DOMAIN_LOOKUP\"] = \"DOMAIN_LOOKUP\";\n    FEATURES[\"SPENDING_LIMIT\"] = \"SPENDING_LIMIT\";\n    FEATURES[\"EIP1559\"] = \"EIP1559\";\n    FEATURES[\"SAFE_TX_GAS_OPTIONAL\"] = \"SAFE_TX_GAS_OPTIONAL\";\n    FEATURES[\"TX_SIMULATION\"] = \"TX_SIMULATION\";\n    FEATURES[\"EIP1271\"] = \"EIP1271\";\n})(FEATURES || (exports.FEATURES = FEATURES = {}));\n//# sourceMappingURL=chains.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvY2hhaW5zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdCQUFnQixHQUFHLHNCQUFzQixHQUFHLDBCQUEwQjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyx5QkFBeUIsMEJBQTBCLDBCQUEwQjtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHFCQUFxQixzQkFBc0Isc0JBQXNCO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGVBQWUsZ0JBQWdCLGdCQUFnQjtBQUNoRCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcdHlwZXNcXGNoYWlucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuRkVBVFVSRVMgPSBleHBvcnRzLkdBU19QUklDRV9UWVBFID0gZXhwb3J0cy5SUENfQVVUSEVOVElDQVRJT04gPSB2b2lkIDA7XG52YXIgUlBDX0FVVEhFTlRJQ0FUSU9OO1xuKGZ1bmN0aW9uIChSUENfQVVUSEVOVElDQVRJT04pIHtcbiAgICBSUENfQVVUSEVOVElDQVRJT05bXCJBUElfS0VZX1BBVEhcIl0gPSBcIkFQSV9LRVlfUEFUSFwiO1xuICAgIFJQQ19BVVRIRU5USUNBVElPTltcIk5PX0FVVEhFTlRJQ0FUSU9OXCJdID0gXCJOT19BVVRIRU5USUNBVElPTlwiO1xuICAgIFJQQ19BVVRIRU5USUNBVElPTltcIlVOS05PV05cIl0gPSBcIlVOS05PV05cIjtcbn0pKFJQQ19BVVRIRU5USUNBVElPTiB8fCAoZXhwb3J0cy5SUENfQVVUSEVOVElDQVRJT04gPSBSUENfQVVUSEVOVElDQVRJT04gPSB7fSkpO1xudmFyIEdBU19QUklDRV9UWVBFO1xuKGZ1bmN0aW9uIChHQVNfUFJJQ0VfVFlQRSkge1xuICAgIEdBU19QUklDRV9UWVBFW1wiT1JBQ0xFXCJdID0gXCJPUkFDTEVcIjtcbiAgICBHQVNfUFJJQ0VfVFlQRVtcIkZJWEVEXCJdID0gXCJGSVhFRFwiO1xuICAgIEdBU19QUklDRV9UWVBFW1wiRklYRURfMTU1OVwiXSA9IFwiRklYRUQxNTU5XCI7XG4gICAgR0FTX1BSSUNFX1RZUEVbXCJVTktOT1dOXCJdID0gXCJVTktOT1dOXCI7XG59KShHQVNfUFJJQ0VfVFlQRSB8fCAoZXhwb3J0cy5HQVNfUFJJQ0VfVFlQRSA9IEdBU19QUklDRV9UWVBFID0ge30pKTtcbnZhciBGRUFUVVJFUztcbihmdW5jdGlvbiAoRkVBVFVSRVMpIHtcbiAgICBGRUFUVVJFU1tcIkVSQzcyMVwiXSA9IFwiRVJDNzIxXCI7XG4gICAgRkVBVFVSRVNbXCJTQUZFX0FQUFNcIl0gPSBcIlNBRkVfQVBQU1wiO1xuICAgIEZFQVRVUkVTW1wiQ09OVFJBQ1RfSU5URVJBQ1RJT05cIl0gPSBcIkNPTlRSQUNUX0lOVEVSQUNUSU9OXCI7XG4gICAgRkVBVFVSRVNbXCJET01BSU5fTE9PS1VQXCJdID0gXCJET01BSU5fTE9PS1VQXCI7XG4gICAgRkVBVFVSRVNbXCJTUEVORElOR19MSU1JVFwiXSA9IFwiU1BFTkRJTkdfTElNSVRcIjtcbiAgICBGRUFUVVJFU1tcIkVJUDE1NTlcIl0gPSBcIkVJUDE1NTlcIjtcbiAgICBGRUFUVVJFU1tcIlNBRkVfVFhfR0FTX09QVElPTkFMXCJdID0gXCJTQUZFX1RYX0dBU19PUFRJT05BTFwiO1xuICAgIEZFQVRVUkVTW1wiVFhfU0lNVUxBVElPTlwiXSA9IFwiVFhfU0lNVUxBVElPTlwiO1xuICAgIEZFQVRVUkVTW1wiRUlQMTI3MVwiXSA9IFwiRUlQMTI3MVwiO1xufSkoRkVBVFVSRVMgfHwgKGV4cG9ydHMuRkVBVFVSRVMgPSBGRUFUVVJFUyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaGFpbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TokenType = void 0;\nvar TokenType;\n(function (TokenType) {\n    TokenType[\"ERC20\"] = \"ERC20\";\n    TokenType[\"ERC721\"] = \"ERC721\";\n    TokenType[\"NATIVE_TOKEN\"] = \"NATIVE_TOKEN\";\n    TokenType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TokenType || (exports.TokenType = TokenType = {}));\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvY29tbW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGdCQUFnQixpQkFBaUIsaUJBQWlCO0FBQ25EIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFx0eXBlc1xcY29tbW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Ub2tlblR5cGUgPSB2b2lkIDA7XG52YXIgVG9rZW5UeXBlO1xuKGZ1bmN0aW9uIChUb2tlblR5cGUpIHtcbiAgICBUb2tlblR5cGVbXCJFUkMyMFwiXSA9IFwiRVJDMjBcIjtcbiAgICBUb2tlblR5cGVbXCJFUkM3MjFcIl0gPSBcIkVSQzcyMVwiO1xuICAgIFRva2VuVHlwZVtcIk5BVElWRV9UT0tFTlwiXSA9IFwiTkFUSVZFX1RPS0VOXCI7XG4gICAgVG9rZW5UeXBlW1wiVU5LTk9XTlwiXSA9IFwiVU5LTk9XTlwiO1xufSkoVG9rZW5UeXBlIHx8IChleHBvcnRzLlRva2VuVHlwZSA9IFRva2VuVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb21tb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NativeStakingStatus = exports.ConfirmationViewTypes = void 0;\nvar ConfirmationViewTypes;\n(function (ConfirmationViewTypes) {\n    ConfirmationViewTypes[\"GENERIC\"] = \"GENERIC\";\n    ConfirmationViewTypes[\"COW_SWAP_ORDER\"] = \"COW_SWAP_ORDER\";\n    ConfirmationViewTypes[\"COW_SWAP_TWAP_ORDER\"] = \"COW_SWAP_TWAP_ORDER\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_DEPOSIT\"] = \"KILN_NATIVE_STAKING_DEPOSIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_VALIDATORS_EXIT\"] = \"KILN_NATIVE_STAKING_VALIDATORS_EXIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_WITHDRAW\"] = \"KILN_NATIVE_STAKING_WITHDRAW\";\n})(ConfirmationViewTypes || (exports.ConfirmationViewTypes = ConfirmationViewTypes = {}));\nvar NativeStakingStatus;\n(function (NativeStakingStatus) {\n    NativeStakingStatus[\"NOT_STAKED\"] = \"NOT_STAKED\";\n    NativeStakingStatus[\"ACTIVATING\"] = \"ACTIVATING\";\n    NativeStakingStatus[\"DEPOSIT_IN_PROGRESS\"] = \"DEPOSIT_IN_PROGRESS\";\n    NativeStakingStatus[\"ACTIVE\"] = \"ACTIVE\";\n    NativeStakingStatus[\"EXIT_REQUESTED\"] = \"EXIT_REQUESTED\";\n    NativeStakingStatus[\"EXITING\"] = \"EXITING\";\n    NativeStakingStatus[\"EXITED\"] = \"EXITED\";\n    NativeStakingStatus[\"SLASHED\"] = \"SLASHED\";\n})(NativeStakingStatus || (exports.NativeStakingStatus = NativeStakingStatus = {}));\n//# sourceMappingURL=decoded-data.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=master-copies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvbWFzdGVyLWNvcGllcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcdHlwZXNcXG1hc3Rlci1jb3BpZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYXN0ZXItY29waWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeviceType = void 0;\nvar DeviceType;\n(function (DeviceType) {\n    DeviceType[\"ANDROID\"] = \"ANDROID\";\n    DeviceType[\"IOS\"] = \"IOS\";\n    DeviceType[\"WEB\"] = \"WEB\";\n})(DeviceType || (exports.DeviceType = DeviceType = {}));\n//# sourceMappingURL=notifications.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvbm90aWZpY2F0aW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsaUJBQWlCLGtCQUFrQixrQkFBa0I7QUFDdEQiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcXGRpc3RcXHR5cGVzXFxub3RpZmljYXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5EZXZpY2VUeXBlID0gdm9pZCAwO1xudmFyIERldmljZVR5cGU7XG4oZnVuY3Rpb24gKERldmljZVR5cGUpIHtcbiAgICBEZXZpY2VUeXBlW1wiQU5EUk9JRFwiXSA9IFwiQU5EUk9JRFwiO1xuICAgIERldmljZVR5cGVbXCJJT1NcIl0gPSBcIklPU1wiO1xuICAgIERldmljZVR5cGVbXCJXRUJcIl0gPSBcIldFQlwiO1xufSkoRGV2aWNlVHlwZSB8fCAoZXhwb3J0cy5EZXZpY2VUeXBlID0gRGV2aWNlVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ub3RpZmljYXRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=relay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvcmVsYXkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcXGRpc3RcXHR5cGVzXFxyZWxheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlbGF5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppSocialPlatforms = exports.SafeAppFeatures = exports.SafeAppAccessPolicyTypes = void 0;\nvar SafeAppAccessPolicyTypes;\n(function (SafeAppAccessPolicyTypes) {\n    SafeAppAccessPolicyTypes[\"NoRestrictions\"] = \"NO_RESTRICTIONS\";\n    SafeAppAccessPolicyTypes[\"DomainAllowlist\"] = \"DOMAIN_ALLOWLIST\";\n})(SafeAppAccessPolicyTypes || (exports.SafeAppAccessPolicyTypes = SafeAppAccessPolicyTypes = {}));\nvar SafeAppFeatures;\n(function (SafeAppFeatures) {\n    SafeAppFeatures[\"BATCHED_TRANSACTIONS\"] = \"BATCHED_TRANSACTIONS\";\n})(SafeAppFeatures || (exports.SafeAppFeatures = SafeAppFeatures = {}));\nvar SafeAppSocialPlatforms;\n(function (SafeAppSocialPlatforms) {\n    SafeAppSocialPlatforms[\"TWITTER\"] = \"TWITTER\";\n    SafeAppSocialPlatforms[\"GITHUB\"] = \"GITHUB\";\n    SafeAppSocialPlatforms[\"DISCORD\"] = \"DISCORD\";\n    SafeAppSocialPlatforms[\"TELEGRAM\"] = \"TELEGRAM\";\n})(SafeAppSocialPlatforms || (exports.SafeAppSocialPlatforms = SafeAppSocialPlatforms = {}));\n//# sourceMappingURL=safe-apps.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ImplementationVersionState = void 0;\nvar ImplementationVersionState;\n(function (ImplementationVersionState) {\n    ImplementationVersionState[\"UP_TO_DATE\"] = \"UP_TO_DATE\";\n    ImplementationVersionState[\"OUTDATED\"] = \"OUTDATED\";\n    ImplementationVersionState[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ImplementationVersionState || (exports.ImplementationVersionState = ImplementationVersionState = {}));\n//# sourceMappingURL=safe-info.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvc2FmZS1pbmZvLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxpQ0FBaUMsa0NBQWtDLGtDQUFrQztBQUN0RyIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcdHlwZXNcXHNhZmUtaW5mby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGUgPSB2b2lkIDA7XG52YXIgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGU7XG4oZnVuY3Rpb24gKEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlKSB7XG4gICAgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGVbXCJVUF9UT19EQVRFXCJdID0gXCJVUF9UT19EQVRFXCI7XG4gICAgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGVbXCJPVVREQVRFRFwiXSA9IFwiT1VUREFURURcIjtcbiAgICBJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZVtcIlVOS05PV05cIl0gPSBcIlVOS05PV05cIjtcbn0pKEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlIHx8IChleHBvcnRzLkltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlID0gSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGUgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2FmZS1pbmZvLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeMessageStatus = exports.SafeMessageListItemType = void 0;\nvar SafeMessageListItemType;\n(function (SafeMessageListItemType) {\n    SafeMessageListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n    SafeMessageListItemType[\"MESSAGE\"] = \"MESSAGE\";\n})(SafeMessageListItemType || (exports.SafeMessageListItemType = SafeMessageListItemType = {}));\nvar SafeMessageStatus;\n(function (SafeMessageStatus) {\n    SafeMessageStatus[\"NEEDS_CONFIRMATION\"] = \"NEEDS_CONFIRMATION\";\n    SafeMessageStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n})(SafeMessageStatus || (exports.SafeMessageStatus = SafeMessageStatus = {}));\n//# sourceMappingURL=safe-messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvc2FmZS1tZXNzYWdlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUIsR0FBRywrQkFBK0I7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhCQUE4QiwrQkFBK0IsK0JBQStCO0FBQzdGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyx3QkFBd0IseUJBQXlCLHlCQUF5QjtBQUMzRSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcdHlwZXNcXHNhZmUtbWVzc2FnZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlNhZmVNZXNzYWdlU3RhdHVzID0gZXhwb3J0cy5TYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSA9IHZvaWQgMDtcbnZhciBTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZTtcbihmdW5jdGlvbiAoU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUpIHtcbiAgICBTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZVtcIkRBVEVfTEFCRUxcIl0gPSBcIkRBVEVfTEFCRUxcIjtcbiAgICBTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZVtcIk1FU1NBR0VcIl0gPSBcIk1FU1NBR0VcIjtcbn0pKFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlIHx8IChleHBvcnRzLlNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlID0gU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUgPSB7fSkpO1xudmFyIFNhZmVNZXNzYWdlU3RhdHVzO1xuKGZ1bmN0aW9uIChTYWZlTWVzc2FnZVN0YXR1cykge1xuICAgIFNhZmVNZXNzYWdlU3RhdHVzW1wiTkVFRFNfQ09ORklSTUFUSU9OXCJdID0gXCJORUVEU19DT05GSVJNQVRJT05cIjtcbiAgICBTYWZlTWVzc2FnZVN0YXR1c1tcIkNPTkZJUk1FRFwiXSA9IFwiQ09ORklSTUVEXCI7XG59KShTYWZlTWVzc2FnZVN0YXR1cyB8fCAoZXhwb3J0cy5TYWZlTWVzc2FnZVN0YXR1cyA9IFNhZmVNZXNzYWdlU3RhdHVzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNhZmUtbWVzc2FnZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LabelValue = exports.StartTimeValue = exports.DurationType = exports.DetailedExecutionInfoType = exports.TransactionListItemType = exports.ConflictType = exports.TransactionInfoType = exports.SettingsInfoType = exports.TransactionTokenType = exports.TransferDirection = exports.TransactionStatus = exports.Operation = void 0;\nvar Operation;\n(function (Operation) {\n    Operation[Operation[\"CALL\"] = 0] = \"CALL\";\n    Operation[Operation[\"DELEGATE\"] = 1] = \"DELEGATE\";\n})(Operation || (exports.Operation = Operation = {}));\nvar TransactionStatus;\n(function (TransactionStatus) {\n    TransactionStatus[\"AWAITING_CONFIRMATIONS\"] = \"AWAITING_CONFIRMATIONS\";\n    TransactionStatus[\"AWAITING_EXECUTION\"] = \"AWAITING_EXECUTION\";\n    TransactionStatus[\"CANCELLED\"] = \"CANCELLED\";\n    TransactionStatus[\"FAILED\"] = \"FAILED\";\n    TransactionStatus[\"SUCCESS\"] = \"SUCCESS\";\n})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));\nvar TransferDirection;\n(function (TransferDirection) {\n    TransferDirection[\"INCOMING\"] = \"INCOMING\";\n    TransferDirection[\"OUTGOING\"] = \"OUTGOING\";\n    TransferDirection[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TransferDirection || (exports.TransferDirection = TransferDirection = {}));\nvar TransactionTokenType;\n(function (TransactionTokenType) {\n    TransactionTokenType[\"ERC20\"] = \"ERC20\";\n    TransactionTokenType[\"ERC721\"] = \"ERC721\";\n    TransactionTokenType[\"NATIVE_COIN\"] = \"NATIVE_COIN\";\n})(TransactionTokenType || (exports.TransactionTokenType = TransactionTokenType = {}));\nvar SettingsInfoType;\n(function (SettingsInfoType) {\n    SettingsInfoType[\"SET_FALLBACK_HANDLER\"] = \"SET_FALLBACK_HANDLER\";\n    SettingsInfoType[\"ADD_OWNER\"] = \"ADD_OWNER\";\n    SettingsInfoType[\"REMOVE_OWNER\"] = \"REMOVE_OWNER\";\n    SettingsInfoType[\"SWAP_OWNER\"] = \"SWAP_OWNER\";\n    SettingsInfoType[\"CHANGE_THRESHOLD\"] = \"CHANGE_THRESHOLD\";\n    SettingsInfoType[\"CHANGE_IMPLEMENTATION\"] = \"CHANGE_IMPLEMENTATION\";\n    SettingsInfoType[\"ENABLE_MODULE\"] = \"ENABLE_MODULE\";\n    SettingsInfoType[\"DISABLE_MODULE\"] = \"DISABLE_MODULE\";\n    SettingsInfoType[\"SET_GUARD\"] = \"SET_GUARD\";\n    SettingsInfoType[\"DELETE_GUARD\"] = \"DELETE_GUARD\";\n})(SettingsInfoType || (exports.SettingsInfoType = SettingsInfoType = {}));\nvar TransactionInfoType;\n(function (TransactionInfoType) {\n    TransactionInfoType[\"TRANSFER\"] = \"Transfer\";\n    TransactionInfoType[\"SETTINGS_CHANGE\"] = \"SettingsChange\";\n    TransactionInfoType[\"CUSTOM\"] = \"Custom\";\n    TransactionInfoType[\"CREATION\"] = \"Creation\";\n    TransactionInfoType[\"SWAP_ORDER\"] = \"SwapOrder\";\n    TransactionInfoType[\"TWAP_ORDER\"] = \"TwapOrder\";\n    TransactionInfoType[\"SWAP_TRANSFER\"] = \"SwapTransfer\";\n    TransactionInfoType[\"NATIVE_STAKING_DEPOSIT\"] = \"NativeStakingDeposit\";\n    TransactionInfoType[\"NATIVE_STAKING_VALIDATORS_EXIT\"] = \"NativeStakingValidatorsExit\";\n    TransactionInfoType[\"NATIVE_STAKING_WITHDRAW\"] = \"NativeStakingWithdraw\";\n})(TransactionInfoType || (exports.TransactionInfoType = TransactionInfoType = {}));\nvar ConflictType;\n(function (ConflictType) {\n    ConflictType[\"NONE\"] = \"None\";\n    ConflictType[\"HAS_NEXT\"] = \"HasNext\";\n    ConflictType[\"END\"] = \"End\";\n})(ConflictType || (exports.ConflictType = ConflictType = {}));\nvar TransactionListItemType;\n(function (TransactionListItemType) {\n    TransactionListItemType[\"TRANSACTION\"] = \"TRANSACTION\";\n    TransactionListItemType[\"LABEL\"] = \"LABEL\";\n    TransactionListItemType[\"CONFLICT_HEADER\"] = \"CONFLICT_HEADER\";\n    TransactionListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n})(TransactionListItemType || (exports.TransactionListItemType = TransactionListItemType = {}));\nvar DetailedExecutionInfoType;\n(function (DetailedExecutionInfoType) {\n    DetailedExecutionInfoType[\"MULTISIG\"] = \"MULTISIG\";\n    DetailedExecutionInfoType[\"MODULE\"] = \"MODULE\";\n})(DetailedExecutionInfoType || (exports.DetailedExecutionInfoType = DetailedExecutionInfoType = {}));\nvar DurationType;\n(function (DurationType) {\n    DurationType[\"AUTO\"] = \"AUTO\";\n    DurationType[\"LIMIT_DURATION\"] = \"LIMIT_DURATION\";\n})(DurationType || (exports.DurationType = DurationType = {}));\nvar StartTimeValue;\n(function (StartTimeValue) {\n    StartTimeValue[\"AT_MINING_TIME\"] = \"AT_MINING_TIME\";\n    StartTimeValue[\"AT_EPOCH\"] = \"AT_EPOCH\";\n})(StartTimeValue || (exports.StartTimeValue = StartTimeValue = {}));\nvar LabelValue;\n(function (LabelValue) {\n    LabelValue[\"Queued\"] = \"Queued\";\n    LabelValue[\"Next\"] = \"Next\";\n})(LabelValue || (exports.LabelValue = LabelValue = {}));\n//# sourceMappingURL=transactions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.insertParams = insertParams;\nexports.stringifyQuery = stringifyQuery;\nexports.fetchData = fetchData;\nexports.getData = getData;\nconst isErrorResponse = (data) => {\n    const isObject = typeof data === 'object' && data !== null;\n    return isObject && ('code' in data || 'statusCode' in data) && 'message' in data;\n};\nfunction replaceParam(str, key, value) {\n    return str.replace(new RegExp(`\\\\{${key}\\\\}`, 'g'), value);\n}\nfunction insertParams(template, params) {\n    return params\n        ? Object.keys(params).reduce((result, key) => {\n            return replaceParam(result, key, String(params[key]));\n        }, template)\n        : template;\n}\nfunction stringifyQuery(query) {\n    if (!query) {\n        return '';\n    }\n    const searchParams = new URLSearchParams();\n    Object.keys(query).forEach((key) => {\n        if (query[key] != null) {\n            searchParams.append(key, String(query[key]));\n        }\n    });\n    const searchString = searchParams.toString();\n    return searchString ? `?${searchString}` : '';\n}\nfunction parseResponse(resp) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        let json;\n        try {\n            json = yield resp.json();\n        }\n        catch (_b) {\n            json = {};\n        }\n        if (!resp.ok) {\n            const errTxt = isErrorResponse(json)\n                ? `CGW error - ${(_a = json.code) !== null && _a !== void 0 ? _a : json.statusCode}: ${json.message}`\n                : `CGW error - status ${resp.statusText}`;\n            throw new Error(errTxt);\n        }\n        return json;\n    });\n}\nfunction fetchData(url, method, body, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const requestHeaders = Object.assign({ 'Content-Type': 'application/json' }, headers);\n        const options = {\n            method: method !== null && method !== void 0 ? method : 'POST',\n            headers: requestHeaders,\n        };\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        if (body != null) {\n            options.body = typeof body === 'string' ? body : JSON.stringify(body);\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\nfunction getData(url, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const options = {\n            method: 'GET',\n        };\n        if (headers) {\n            options['headers'] = Object.assign(Object.assign({}, headers), { 'Content-Type': 'application/json' });\n        }\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\n"));

/***/ })

}]);