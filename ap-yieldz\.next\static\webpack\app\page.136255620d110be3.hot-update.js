"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/blockchain/config/wagmi.ts":
/*!****************************************!*\
  !*** ./app/blockchain/config/wagmi.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_NETWORK: () => (/* binding */ CURRENT_NETWORK),\n/* harmony export */   LENDING_APY_AGGREGATOR_ADDRESS: () => (/* binding */ LENDING_APY_AGGREGATOR_ADDRESS),\n/* harmony export */   SUPPORTED_TOKENS: () => (/* binding */ SUPPORTED_TOKENS),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getCurrentTokens: () => (/* binding */ getCurrentTokens)\n/* harmony export */ });\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi/chains */ \"(app-pages-browser)/./node_modules/viem/_esm/chains/definitions/avalancheFuji.js\");\n/* harmony import */ var _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @rainbow-me/rainbowkit/wallets */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-RTDGOYZC.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/viem/_esm/clients/transports/http.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/cookie.js\");\n\n\n\n\nif (false) {}\nconst { wallets } = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultWallets)();\nconst config = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultConfig)({\n    appName: 'Alligator',\n    projectId: \"b11d3071871bb2c95781af8517fd1cfe\",\n    wallets: [\n        {\n            groupName: \"Core Wallet\",\n            wallets: [\n                _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__.injectedWallet\n            ]\n        }\n    ],\n    chains: [\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji\n    ],\n    transports: {\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_3__.http)(\"https://api.avax-test.network/ext/bc/C/rpc\" || 0)\n    },\n    ssr: true,\n    storage: (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.createStorage)({\n        storage: wagmi__WEBPACK_IMPORTED_MODULE_5__.cookieStorage\n    })\n});\n// Ensure proper address formatting\nconst CONTRACT_ADDRESS = \"******************************************\" || 0;\nconst LENDING_APY_AGGREGATOR_ADDRESS = CONTRACT_ADDRESS.toLowerCase();\nconst SUPPORTED_TOKENS = {\n    // Avalanche Fuji Testnet addresses for testing\n    fuji: {\n        USDC: '******************************************',\n        WAVAX: '******************************************',\n        USDT: '******************************************'\n    },\n    // Avalanche Mainnet addresses (for future use)\n    avalanche: {\n        USDC: '******************************************',\n        USDT: '******************************************',\n        WETH: '******************************************',\n        WBTC: '******************************************',\n        WAVAX: '******************************************'\n    },\n    // Base addresses (for future Morpho integration)\n    base: {\n        USDC: '******************************************',\n        WETH: '******************************************'\n    }\n};\n// Current network configuration - change this to switch between testnet and mainnet\nconst CURRENT_NETWORK = 'fuji'; // 'fuji' for testnet, 'avalanche' for mainnet\n// Get tokens for current network\nconst getCurrentTokens = ()=>SUPPORTED_TOKENS[CURRENT_NETWORK];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blockchain/config/wagmi.ts\n"));

/***/ })

});