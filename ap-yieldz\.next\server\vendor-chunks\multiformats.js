"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/multiformats";
exports.ids = ["vendor-chunks/multiformats"];
exports.modules = {

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base.js":
/*!*********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Codec: () => (/* binding */ Codec),\n/* harmony export */   baseX: () => (/* binding */ baseX),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   or: () => (/* binding */ or),\n/* harmony export */   rfc4648: () => (/* binding */ rfc4648)\n/* harmony export */ });\n/* harmony import */ var _vendor_base_x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../vendor/base-x.js */ \"(ssr)/./node_modules/multiformats/esm/vendor/base-x.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\nclass Encoder {\n  constructor(name, prefix, baseEncode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n  }\n  encode(bytes) {\n    if (bytes instanceof Uint8Array) {\n      return `${ this.prefix }${ this.baseEncode(bytes) }`;\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}\nclass Decoder {\n  constructor(name, prefix, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    if (prefix.codePointAt(0) === undefined) {\n      throw new Error('Invalid prefix character');\n    }\n    this.prefixCodePoint = prefix.codePointAt(0);\n    this.baseDecode = baseDecode;\n  }\n  decode(text) {\n    if (typeof text === 'string') {\n      if (text.codePointAt(0) !== this.prefixCodePoint) {\n        throw Error(`Unable to decode multibase string ${ JSON.stringify(text) }, ${ this.name } decoder only supports inputs prefixed with ${ this.prefix }`);\n      }\n      return this.baseDecode(text.slice(this.prefix.length));\n    } else {\n      throw Error('Can only multibase decode strings');\n    }\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n}\nclass ComposedDecoder {\n  constructor(decoders) {\n    this.decoders = decoders;\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n  decode(input) {\n    const prefix = input[0];\n    const decoder = this.decoders[prefix];\n    if (decoder) {\n      return decoder.decode(input);\n    } else {\n      throw RangeError(`Unable to decode multibase string ${ JSON.stringify(input) }, only inputs prefixed with ${ Object.keys(this.decoders) } are supported`);\n    }\n  }\n}\nconst or = (left, right) => new ComposedDecoder({\n  ...left.decoders || { [left.prefix]: left },\n  ...right.decoders || { [right.prefix]: right }\n});\nclass Codec {\n  constructor(name, prefix, baseEncode, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n    this.baseDecode = baseDecode;\n    this.encoder = new Encoder(name, prefix, baseEncode);\n    this.decoder = new Decoder(name, prefix, baseDecode);\n  }\n  encode(input) {\n    return this.encoder.encode(input);\n  }\n  decode(input) {\n    return this.decoder.decode(input);\n  }\n}\nconst from = ({name, prefix, encode, decode}) => new Codec(name, prefix, encode, decode);\nconst baseX = ({prefix, name, alphabet}) => {\n  const {encode, decode} = (0,_vendor_base_x_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alphabet, name);\n  return from({\n    prefix,\n    name,\n    encode,\n    decode: text => (0,_bytes_js__WEBPACK_IMPORTED_MODULE_1__.coerce)(decode(text))\n  });\n};\nconst decode = (string, alphabet, bitsPerChar, name) => {\n  const codes = {};\n  for (let i = 0; i < alphabet.length; ++i) {\n    codes[alphabet[i]] = i;\n  }\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n  }\n  const out = new Uint8Array(end * bitsPerChar / 8 | 0);\n  let bits = 0;\n  let buffer = 0;\n  let written = 0;\n  for (let i = 0; i < end; ++i) {\n    const value = codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError(`Non-${ name } character`);\n    }\n    buffer = buffer << bitsPerChar | value;\n    bits += bitsPerChar;\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 255 & buffer >> bits;\n    }\n  }\n  if (bits >= bitsPerChar || 255 & buffer << 8 - bits) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n  return out;\n};\nconst encode = (data, alphabet, bitsPerChar) => {\n  const pad = alphabet[alphabet.length - 1] === '=';\n  const mask = (1 << bitsPerChar) - 1;\n  let out = '';\n  let bits = 0;\n  let buffer = 0;\n  for (let i = 0; i < data.length; ++i) {\n    buffer = buffer << 8 | data[i];\n    bits += 8;\n    while (bits > bitsPerChar) {\n      bits -= bitsPerChar;\n      out += alphabet[mask & buffer >> bits];\n    }\n  }\n  if (bits) {\n    out += alphabet[mask & buffer << bitsPerChar - bits];\n  }\n  if (pad) {\n    while (out.length * bitsPerChar & 7) {\n      out += '=';\n    }\n  }\n  return out;\n};\nconst rfc4648 = ({name, prefix, bitsPerChar, alphabet}) => {\n  return from({\n    prefix,\n    name,\n    encode(input) {\n      return encode(input, alphabet, bitsPerChar);\n    },\n    decode(input) {\n      return decode(input, alphabet, bitsPerChar, name);\n    }\n  });\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base10.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base10.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base10: () => (/* binding */ base10)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base10 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n  prefix: '9',\n  name: 'base10',\n  alphabet: '0123456789'\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTEwLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQzNCLGVBQWUsK0NBQUs7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxtdWx0aWZvcm1hdHNcXGVzbVxcc3JjXFxiYXNlc1xcYmFzZTEwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJhc2VYIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjb25zdCBiYXNlMTAgPSBiYXNlWCh7XG4gIHByZWZpeDogJzknLFxuICBuYW1lOiAnYmFzZTEwJyxcbiAgYWxwaGFiZXQ6ICcwMTIzNDU2Nzg5J1xufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base10.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base16.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base16.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base16: () => (/* binding */ base16),\n/* harmony export */   base16upper: () => (/* binding */ base16upper)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base16 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'f',\n  name: 'base16',\n  alphabet: '0123456789abcdef',\n  bitsPerChar: 4\n});\nconst base16upper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'F',\n  name: 'base16upper',\n  alphabet: '0123456789ABCDEF',\n  bitsPerChar: 4\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTE2LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvQztBQUM3QixlQUFlLGlEQUFPO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNLG9CQUFvQixpREFBTztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcbXVsdGlmb3JtYXRzXFxlc21cXHNyY1xcYmFzZXNcXGJhc2UxNi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZmM0NjQ4IH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjb25zdCBiYXNlMTYgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnZicsXG4gIG5hbWU6ICdiYXNlMTYnLFxuICBhbHBoYWJldDogJzAxMjM0NTY3ODlhYmNkZWYnLFxuICBiaXRzUGVyQ2hhcjogNFxufSk7XG5leHBvcnQgY29uc3QgYmFzZTE2dXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnRicsXG4gIG5hbWU6ICdiYXNlMTZ1cHBlcicsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OUFCQ0RFRicsXG4gIGJpdHNQZXJDaGFyOiA0XG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base16.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base2.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base2.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base2: () => (/* binding */ base2)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base2 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: '0',\n  name: 'base2',\n  alphabet: '01',\n  bitsPerChar: 1\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFDN0IsY0FBYyxpREFBTztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcbXVsdGlmb3JtYXRzXFxlc21cXHNyY1xcYmFzZXNcXGJhc2UyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJmYzQ2NDggfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNvbnN0IGJhc2UyID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJzAnLFxuICBuYW1lOiAnYmFzZTInLFxuICBhbHBoYWJldDogJzAxJyxcbiAgYml0c1BlckNoYXI6IDFcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base256emoji.js":
/*!*****************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base256emoji.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base256emoji: () => (/* binding */ base256emoji)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst alphabet = Array.from('\\uD83D\\uDE80\\uD83E\\uDE90\\u2604\\uD83D\\uDEF0\\uD83C\\uDF0C\\uD83C\\uDF11\\uD83C\\uDF12\\uD83C\\uDF13\\uD83C\\uDF14\\uD83C\\uDF15\\uD83C\\uDF16\\uD83C\\uDF17\\uD83C\\uDF18\\uD83C\\uDF0D\\uD83C\\uDF0F\\uD83C\\uDF0E\\uD83D\\uDC09\\u2600\\uD83D\\uDCBB\\uD83D\\uDDA5\\uD83D\\uDCBE\\uD83D\\uDCBF\\uD83D\\uDE02\\u2764\\uD83D\\uDE0D\\uD83E\\uDD23\\uD83D\\uDE0A\\uD83D\\uDE4F\\uD83D\\uDC95\\uD83D\\uDE2D\\uD83D\\uDE18\\uD83D\\uDC4D\\uD83D\\uDE05\\uD83D\\uDC4F\\uD83D\\uDE01\\uD83D\\uDD25\\uD83E\\uDD70\\uD83D\\uDC94\\uD83D\\uDC96\\uD83D\\uDC99\\uD83D\\uDE22\\uD83E\\uDD14\\uD83D\\uDE06\\uD83D\\uDE44\\uD83D\\uDCAA\\uD83D\\uDE09\\u263A\\uD83D\\uDC4C\\uD83E\\uDD17\\uD83D\\uDC9C\\uD83D\\uDE14\\uD83D\\uDE0E\\uD83D\\uDE07\\uD83C\\uDF39\\uD83E\\uDD26\\uD83C\\uDF89\\uD83D\\uDC9E\\u270C\\u2728\\uD83E\\uDD37\\uD83D\\uDE31\\uD83D\\uDE0C\\uD83C\\uDF38\\uD83D\\uDE4C\\uD83D\\uDE0B\\uD83D\\uDC97\\uD83D\\uDC9A\\uD83D\\uDE0F\\uD83D\\uDC9B\\uD83D\\uDE42\\uD83D\\uDC93\\uD83E\\uDD29\\uD83D\\uDE04\\uD83D\\uDE00\\uD83D\\uDDA4\\uD83D\\uDE03\\uD83D\\uDCAF\\uD83D\\uDE48\\uD83D\\uDC47\\uD83C\\uDFB6\\uD83D\\uDE12\\uD83E\\uDD2D\\u2763\\uD83D\\uDE1C\\uD83D\\uDC8B\\uD83D\\uDC40\\uD83D\\uDE2A\\uD83D\\uDE11\\uD83D\\uDCA5\\uD83D\\uDE4B\\uD83D\\uDE1E\\uD83D\\uDE29\\uD83D\\uDE21\\uD83E\\uDD2A\\uD83D\\uDC4A\\uD83E\\uDD73\\uD83D\\uDE25\\uD83E\\uDD24\\uD83D\\uDC49\\uD83D\\uDC83\\uD83D\\uDE33\\u270B\\uD83D\\uDE1A\\uD83D\\uDE1D\\uD83D\\uDE34\\uD83C\\uDF1F\\uD83D\\uDE2C\\uD83D\\uDE43\\uD83C\\uDF40\\uD83C\\uDF37\\uD83D\\uDE3B\\uD83D\\uDE13\\u2B50\\u2705\\uD83E\\uDD7A\\uD83C\\uDF08\\uD83D\\uDE08\\uD83E\\uDD18\\uD83D\\uDCA6\\u2714\\uD83D\\uDE23\\uD83C\\uDFC3\\uD83D\\uDC90\\u2639\\uD83C\\uDF8A\\uD83D\\uDC98\\uD83D\\uDE20\\u261D\\uD83D\\uDE15\\uD83C\\uDF3A\\uD83C\\uDF82\\uD83C\\uDF3B\\uD83D\\uDE10\\uD83D\\uDD95\\uD83D\\uDC9D\\uD83D\\uDE4A\\uD83D\\uDE39\\uD83D\\uDDE3\\uD83D\\uDCAB\\uD83D\\uDC80\\uD83D\\uDC51\\uD83C\\uDFB5\\uD83E\\uDD1E\\uD83D\\uDE1B\\uD83D\\uDD34\\uD83D\\uDE24\\uD83C\\uDF3C\\uD83D\\uDE2B\\u26BD\\uD83E\\uDD19\\u2615\\uD83C\\uDFC6\\uD83E\\uDD2B\\uD83D\\uDC48\\uD83D\\uDE2E\\uD83D\\uDE46\\uD83C\\uDF7B\\uD83C\\uDF43\\uD83D\\uDC36\\uD83D\\uDC81\\uD83D\\uDE32\\uD83C\\uDF3F\\uD83E\\uDDE1\\uD83C\\uDF81\\u26A1\\uD83C\\uDF1E\\uD83C\\uDF88\\u274C\\u270A\\uD83D\\uDC4B\\uD83D\\uDE30\\uD83E\\uDD28\\uD83D\\uDE36\\uD83E\\uDD1D\\uD83D\\uDEB6\\uD83D\\uDCB0\\uD83C\\uDF53\\uD83D\\uDCA2\\uD83E\\uDD1F\\uD83D\\uDE41\\uD83D\\uDEA8\\uD83D\\uDCA8\\uD83E\\uDD2C\\u2708\\uD83C\\uDF80\\uD83C\\uDF7A\\uD83E\\uDD13\\uD83D\\uDE19\\uD83D\\uDC9F\\uD83C\\uDF31\\uD83D\\uDE16\\uD83D\\uDC76\\uD83E\\uDD74\\u25B6\\u27A1\\u2753\\uD83D\\uDC8E\\uD83D\\uDCB8\\u2B07\\uD83D\\uDE28\\uD83C\\uDF1A\\uD83E\\uDD8B\\uD83D\\uDE37\\uD83D\\uDD7A\\u26A0\\uD83D\\uDE45\\uD83D\\uDE1F\\uD83D\\uDE35\\uD83D\\uDC4E\\uD83E\\uDD32\\uD83E\\uDD20\\uD83E\\uDD27\\uD83D\\uDCCC\\uD83D\\uDD35\\uD83D\\uDC85\\uD83E\\uDDD0\\uD83D\\uDC3E\\uD83C\\uDF52\\uD83D\\uDE17\\uD83E\\uDD11\\uD83C\\uDF0A\\uD83E\\uDD2F\\uD83D\\uDC37\\u260E\\uD83D\\uDCA7\\uD83D\\uDE2F\\uD83D\\uDC86\\uD83D\\uDC46\\uD83C\\uDFA4\\uD83D\\uDE47\\uD83C\\uDF51\\u2744\\uD83C\\uDF34\\uD83D\\uDCA3\\uD83D\\uDC38\\uD83D\\uDC8C\\uD83D\\uDCCD\\uD83E\\uDD40\\uD83E\\uDD22\\uD83D\\uDC45\\uD83D\\uDCA1\\uD83D\\uDCA9\\uD83D\\uDC50\\uD83D\\uDCF8\\uD83D\\uDC7B\\uD83E\\uDD10\\uD83E\\uDD2E\\uD83C\\uDFBC\\uD83E\\uDD75\\uD83D\\uDEA9\\uD83C\\uDF4E\\uD83C\\uDF4A\\uD83D\\uDC7C\\uD83D\\uDC8D\\uD83D\\uDCE3\\uD83E\\uDD42');\nconst alphabetBytesToChars = alphabet.reduce((p, c, i) => {\n  p[i] = c;\n  return p;\n}, []);\nconst alphabetCharsToBytes = alphabet.reduce((p, c, i) => {\n  p[c.codePointAt(0)] = i;\n  return p;\n}, []);\nfunction encode(data) {\n  return data.reduce((p, c) => {\n    p += alphabetBytesToChars[c];\n    return p;\n  }, '');\n}\nfunction decode(str) {\n  const byts = [];\n  for (const char of str) {\n    const byt = alphabetCharsToBytes[char.codePointAt(0)];\n    if (byt === undefined) {\n      throw new Error(`Non-base256emoji character: ${ char }`);\n    }\n    byts.push(byt);\n  }\n  return new Uint8Array(byts);\n}\nconst base256emoji = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.from)({\n  prefix: '\\uD83D\\uDE80',\n  name: 'base256emoji',\n  encode,\n  decode\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base256emoji.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base32.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base32.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base32: () => (/* binding */ base32),\n/* harmony export */   base32hex: () => (/* binding */ base32hex),\n/* harmony export */   base32hexpad: () => (/* binding */ base32hexpad),\n/* harmony export */   base32hexpadupper: () => (/* binding */ base32hexpadupper),\n/* harmony export */   base32hexupper: () => (/* binding */ base32hexupper),\n/* harmony export */   base32pad: () => (/* binding */ base32pad),\n/* harmony export */   base32padupper: () => (/* binding */ base32padupper),\n/* harmony export */   base32upper: () => (/* binding */ base32upper),\n/* harmony export */   base32z: () => (/* binding */ base32z)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base32 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'b',\n  name: 'base32',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567',\n  bitsPerChar: 5\n});\nconst base32upper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'B',\n  name: 'base32upper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',\n  bitsPerChar: 5\n});\nconst base32pad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'c',\n  name: 'base32pad',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567=',\n  bitsPerChar: 5\n});\nconst base32padupper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'C',\n  name: 'base32padupper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=',\n  bitsPerChar: 5\n});\nconst base32hex = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'v',\n  name: 'base32hex',\n  alphabet: '0123456789abcdefghijklmnopqrstuv',\n  bitsPerChar: 5\n});\nconst base32hexupper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'V',\n  name: 'base32hexupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV',\n  bitsPerChar: 5\n});\nconst base32hexpad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 't',\n  name: 'base32hexpad',\n  alphabet: '0123456789abcdefghijklmnopqrstuv=',\n  bitsPerChar: 5\n});\nconst base32hexpadupper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'T',\n  name: 'base32hexpadupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV=',\n  bitsPerChar: 5\n});\nconst base32z = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'h',\n  name: 'base32z',\n  alphabet: 'ybndrfg8ejkmcpqxot1uwisza345h769',\n  bitsPerChar: 5\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTMyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBb0M7QUFDN0IsZUFBZSxpREFBTztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDTSxvQkFBb0IsaURBQU87QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ00sa0JBQWtCLGlEQUFPO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNLHVCQUF1QixpREFBTztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDTSxrQkFBa0IsaURBQU87QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ00sdUJBQXVCLGlEQUFPO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNLHFCQUFxQixpREFBTztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDTSwwQkFBMEIsaURBQU87QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ00sZ0JBQWdCLGlEQUFPO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxtdWx0aWZvcm1hdHNcXGVzbVxcc3JjXFxiYXNlc1xcYmFzZTMyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJmYzQ2NDggfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNvbnN0IGJhc2UzMiA9IHJmYzQ2NDgoe1xuICBwcmVmaXg6ICdiJyxcbiAgbmFtZTogJ2Jhc2UzMicsXG4gIGFscGhhYmV0OiAnYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoyMzQ1NjcnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMydXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnQicsXG4gIG5hbWU6ICdiYXNlMzJ1cHBlcicsXG4gIGFscGhhYmV0OiAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVoyMzQ1NjcnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMycGFkID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ2MnLFxuICBuYW1lOiAnYmFzZTMycGFkJyxcbiAgYWxwaGFiZXQ6ICdhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejIzNDU2Nz0nLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMycGFkdXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnQycsXG4gIG5hbWU6ICdiYXNlMzJwYWR1cHBlcicsXG4gIGFscGhhYmV0OiAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVoyMzQ1Njc9JyxcbiAgYml0c1BlckNoYXI6IDVcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2UzMmhleCA9IHJmYzQ2NDgoe1xuICBwcmVmaXg6ICd2JyxcbiAgbmFtZTogJ2Jhc2UzMmhleCcsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OWFiY2RlZmdoaWprbG1ub3BxcnN0dXYnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMyaGV4dXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnVicsXG4gIG5hbWU6ICdiYXNlMzJoZXh1cHBlcicsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OUFCQ0RFRkdISUpLTE1OT1BRUlNUVVYnLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMyaGV4cGFkID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ3QnLFxuICBuYW1lOiAnYmFzZTMyaGV4cGFkJyxcbiAgYWxwaGFiZXQ6ICcwMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dj0nLFxuICBiaXRzUGVyQ2hhcjogNVxufSk7XG5leHBvcnQgY29uc3QgYmFzZTMyaGV4cGFkdXBwZXIgPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnVCcsXG4gIG5hbWU6ICdiYXNlMzJoZXhwYWR1cHBlcicsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OUFCQ0RFRkdISUpLTE1OT1BRUlNUVVY9JyxcbiAgYml0c1BlckNoYXI6IDVcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2UzMnogPSByZmM0NjQ4KHtcbiAgcHJlZml4OiAnaCcsXG4gIG5hbWU6ICdiYXNlMzJ6JyxcbiAgYWxwaGFiZXQ6ICd5Ym5kcmZnOGVqa21jcHF4b3QxdXdpc3phMzQ1aDc2OScsXG4gIGJpdHNQZXJDaGFyOiA1XG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base32.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base36.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base36.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base36: () => (/* binding */ base36),\n/* harmony export */   base36upper: () => (/* binding */ base36upper)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base36 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n  prefix: 'k',\n  name: 'base36',\n  alphabet: '0123456789abcdefghijklmnopqrstuvwxyz'\n});\nconst base36upper = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n  prefix: 'K',\n  name: 'base36upper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTM2LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUMzQixlQUFlLCtDQUFLO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDTSxvQkFBb0IsK0NBQUs7QUFDaEM7QUFDQTtBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxtdWx0aWZvcm1hdHNcXGVzbVxcc3JjXFxiYXNlc1xcYmFzZTM2LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJhc2VYIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjb25zdCBiYXNlMzYgPSBiYXNlWCh7XG4gIHByZWZpeDogJ2snLFxuICBuYW1lOiAnYmFzZTM2JyxcbiAgYWxwaGFiZXQ6ICcwMTIzNDU2Nzg5YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXonXG59KTtcbmV4cG9ydCBjb25zdCBiYXNlMzZ1cHBlciA9IGJhc2VYKHtcbiAgcHJlZml4OiAnSycsXG4gIG5hbWU6ICdiYXNlMzZ1cHBlcicsXG4gIGFscGhhYmV0OiAnMDEyMzQ1Njc4OUFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaJ1xufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base36.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base58.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base58.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base58btc: () => (/* binding */ base58btc),\n/* harmony export */   base58flickr: () => (/* binding */ base58flickr)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base58btc = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n  name: 'base58btc',\n  prefix: 'z',\n  alphabet: '**********************************************************'\n});\nconst base58flickr = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.baseX)({\n  name: 'base58flickr',\n  prefix: 'Z',\n  alphabet: '**********************************************************'\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base58.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base64.js":
/*!***********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base64.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64: () => (/* binding */ base64),\n/* harmony export */   base64pad: () => (/* binding */ base64pad),\n/* harmony export */   base64url: () => (/* binding */ base64url),\n/* harmony export */   base64urlpad: () => (/* binding */ base64urlpad)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base64 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'm',\n  name: 'base64',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n  bitsPerChar: 6\n});\nconst base64pad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'M',\n  name: 'base64pad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',\n  bitsPerChar: 6\n});\nconst base64url = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'u',\n  name: 'base64url',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bitsPerChar: 6\n});\nconst base64urlpad = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: 'U',\n  name: 'base64urlpad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=',\n  bitsPerChar: 6\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTY0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW9DO0FBQzdCLGVBQWUsaURBQU87QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ00sa0JBQWtCLGlEQUFPO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNLGtCQUFrQixpREFBTztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDTSxxQkFBcUIsaURBQU87QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXG11bHRpZm9ybWF0c1xcZXNtXFxzcmNcXGJhc2VzXFxiYXNlNjQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmZjNDY0OCB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY29uc3QgYmFzZTY0ID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ20nLFxuICBuYW1lOiAnYmFzZTY0JyxcbiAgYWxwaGFiZXQ6ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSsvJyxcbiAgYml0c1BlckNoYXI6IDZcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2U2NHBhZCA9IHJmYzQ2NDgoe1xuICBwcmVmaXg6ICdNJyxcbiAgbmFtZTogJ2Jhc2U2NHBhZCcsXG4gIGFscGhhYmV0OiAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkrLz0nLFxuICBiaXRzUGVyQ2hhcjogNlxufSk7XG5leHBvcnQgY29uc3QgYmFzZTY0dXJsID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJ3UnLFxuICBuYW1lOiAnYmFzZTY0dXJsJyxcbiAgYWxwaGFiZXQ6ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OS1fJyxcbiAgYml0c1BlckNoYXI6IDZcbn0pO1xuZXhwb3J0IGNvbnN0IGJhc2U2NHVybHBhZCA9IHJmYzQ2NDgoe1xuICBwcmVmaXg6ICdVJyxcbiAgbmFtZTogJ2Jhc2U2NHVybHBhZCcsXG4gIGFscGhhYmV0OiAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODktXz0nLFxuICBiaXRzUGVyQ2hhcjogNlxufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/base8.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/base8.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base8: () => (/* binding */ base8)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n\nconst base8 = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.rfc4648)({\n  prefix: '7',\n  name: 'base8',\n  alphabet: '01234567',\n  bitsPerChar: 3\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvYmFzZTguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFDN0IsY0FBYyxpREFBTztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcbXVsdGlmb3JtYXRzXFxlc21cXHNyY1xcYmFzZXNcXGJhc2U4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJmYzQ2NDggfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNvbnN0IGJhc2U4ID0gcmZjNDY0OCh7XG4gIHByZWZpeDogJzcnLFxuICBuYW1lOiAnYmFzZTgnLFxuICBhbHBoYWJldDogJzAxMjM0NTY3JyxcbiAgYml0c1BlckNoYXI6IDNcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/base8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bases/identity.js":
/*!*************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bases/identity.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   identity: () => (/* binding */ identity)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\nconst identity = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.from)({\n  prefix: '\\0',\n  name: 'identity',\n  encode: buf => (0,_bytes_js__WEBPACK_IMPORTED_MODULE_1__.toString)(buf),\n  decode: str => (0,_bytes_js__WEBPACK_IMPORTED_MODULE_1__.fromString)(str)\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvYmFzZXMvaWRlbnRpdHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDO0FBSVo7QUFDZCxpQkFBaUIsOENBQUk7QUFDNUI7QUFDQTtBQUNBLGlCQUFpQixtREFBUTtBQUN6QixpQkFBaUIscURBQVU7QUFDM0IsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxtdWx0aWZvcm1hdHNcXGVzbVxcc3JjXFxiYXNlc1xcaWRlbnRpdHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZnJvbSB9IGZyb20gJy4vYmFzZS5qcyc7XG5pbXBvcnQge1xuICBmcm9tU3RyaW5nLFxuICB0b1N0cmluZ1xufSBmcm9tICcuLi9ieXRlcy5qcyc7XG5leHBvcnQgY29uc3QgaWRlbnRpdHkgPSBmcm9tKHtcbiAgcHJlZml4OiAnXFwwJyxcbiAgbmFtZTogJ2lkZW50aXR5JyxcbiAgZW5jb2RlOiBidWYgPT4gdG9TdHJpbmcoYnVmKSxcbiAgZGVjb2RlOiBzdHIgPT4gZnJvbVN0cmluZyhzdHIpXG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bases/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/basics.js":
/*!*****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/basics.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CID: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.CID),\n/* harmony export */   bases: () => (/* binding */ bases),\n/* harmony export */   bytes: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.bytes),\n/* harmony export */   codecs: () => (/* binding */ codecs),\n/* harmony export */   digest: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.digest),\n/* harmony export */   hasher: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.hasher),\n/* harmony export */   hashes: () => (/* binding */ hashes),\n/* harmony export */   varint: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_14__.varint)\n/* harmony export */ });\n/* harmony import */ var _bases_identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bases/identity.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/identity.js\");\n/* harmony import */ var _bases_base2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bases/base2.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base2.js\");\n/* harmony import */ var _bases_base8_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bases/base8.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base8.js\");\n/* harmony import */ var _bases_base10_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bases/base10.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base10.js\");\n/* harmony import */ var _bases_base16_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bases/base16.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base16.js\");\n/* harmony import */ var _bases_base32_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./bases/base32.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base32.js\");\n/* harmony import */ var _bases_base36_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./bases/base36.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base36.js\");\n/* harmony import */ var _bases_base58_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./bases/base58.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base58.js\");\n/* harmony import */ var _bases_base64_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./bases/base64.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base64.js\");\n/* harmony import */ var _bases_base256emoji_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./bases/base256emoji.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base256emoji.js\");\n/* harmony import */ var _hashes_sha2_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hashes/sha2.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/sha2.js\");\n/* harmony import */ var _hashes_identity_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hashes/identity.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/identity.js\");\n/* harmony import */ var _codecs_raw_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./codecs/raw.js */ \"(ssr)/./node_modules/multiformats/esm/src/codecs/raw.js\");\n/* harmony import */ var _codecs_json_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./codecs/json.js */ \"(ssr)/./node_modules/multiformats/esm/src/codecs/json.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/multiformats/esm/src/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst bases = {\n  ..._bases_identity_js__WEBPACK_IMPORTED_MODULE_0__,\n  ..._bases_base2_js__WEBPACK_IMPORTED_MODULE_1__,\n  ..._bases_base8_js__WEBPACK_IMPORTED_MODULE_2__,\n  ..._bases_base10_js__WEBPACK_IMPORTED_MODULE_3__,\n  ..._bases_base16_js__WEBPACK_IMPORTED_MODULE_4__,\n  ..._bases_base32_js__WEBPACK_IMPORTED_MODULE_5__,\n  ..._bases_base36_js__WEBPACK_IMPORTED_MODULE_6__,\n  ..._bases_base58_js__WEBPACK_IMPORTED_MODULE_7__,\n  ..._bases_base64_js__WEBPACK_IMPORTED_MODULE_8__,\n  ..._bases_base256emoji_js__WEBPACK_IMPORTED_MODULE_9__\n};\nconst hashes = {\n  ..._hashes_sha2_js__WEBPACK_IMPORTED_MODULE_10__,\n  ..._hashes_identity_js__WEBPACK_IMPORTED_MODULE_11__\n};\nconst codecs = {\n  raw: _codecs_raw_js__WEBPACK_IMPORTED_MODULE_12__,\n  json: _codecs_json_js__WEBPACK_IMPORTED_MODULE_13__\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/basics.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   equals: () => (/* binding */ equals),\n/* harmony export */   fromHex: () => (/* binding */ fromHex),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isBinary: () => (/* binding */ isBinary),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toString: () => (/* binding */ toString)\n/* harmony export */ });\nconst empty = new Uint8Array(0);\nconst toHex = d => d.reduce((hex, byte) => hex + byte.toString(16).padStart(2, '0'), '');\nconst fromHex = hex => {\n  const hexes = hex.match(/../g);\n  return hexes ? new Uint8Array(hexes.map(b => parseInt(b, 16))) : empty;\n};\nconst equals = (aa, bb) => {\n  if (aa === bb)\n    return true;\n  if (aa.byteLength !== bb.byteLength) {\n    return false;\n  }\n  for (let ii = 0; ii < aa.byteLength; ii++) {\n    if (aa[ii] !== bb[ii]) {\n      return false;\n    }\n  }\n  return true;\n};\nconst coerce = o => {\n  if (o instanceof Uint8Array && o.constructor.name === 'Uint8Array')\n    return o;\n  if (o instanceof ArrayBuffer)\n    return new Uint8Array(o);\n  if (ArrayBuffer.isView(o)) {\n    return new Uint8Array(o.buffer, o.byteOffset, o.byteLength);\n  }\n  throw new Error('Unknown type, must be binary type');\n};\nconst isBinary = o => o instanceof ArrayBuffer || ArrayBuffer.isView(o);\nconst fromString = str => new TextEncoder().encode(str);\nconst toString = b => new TextDecoder().decode(b);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/cid.js":
/*!**************************************************!*\
  !*** ./node_modules/multiformats/esm/src/cid.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CID: () => (/* binding */ CID)\n/* harmony export */ });\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./varint.js */ \"(ssr)/./node_modules/multiformats/esm/src/varint.js\");\n/* harmony import */ var _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hashes/digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n/* harmony import */ var _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bases/base58.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base58.js\");\n/* harmony import */ var _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bases/base32.js */ \"(ssr)/./node_modules/multiformats/esm/src/bases/base32.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\n\n\n\nclass CID {\n  constructor(version, code, multihash, bytes) {\n    this.code = code;\n    this.version = version;\n    this.multihash = multihash;\n    this.bytes = bytes;\n    this.byteOffset = bytes.byteOffset;\n    this.byteLength = bytes.byteLength;\n    this.asCID = this;\n    this._baseCache = new Map();\n    Object.defineProperties(this, {\n      byteOffset: hidden,\n      byteLength: hidden,\n      code: readonly,\n      version: readonly,\n      multihash: readonly,\n      bytes: readonly,\n      _baseCache: hidden,\n      asCID: hidden\n    });\n  }\n  toV0() {\n    switch (this.version) {\n    case 0: {\n        return this;\n      }\n    default: {\n        const {code, multihash} = this;\n        if (code !== DAG_PB_CODE) {\n          throw new Error('Cannot convert a non dag-pb CID to CIDv0');\n        }\n        if (multihash.code !== SHA_256_CODE) {\n          throw new Error('Cannot convert non sha2-256 multihash CID to CIDv0');\n        }\n        return CID.createV0(multihash);\n      }\n    }\n  }\n  toV1() {\n    switch (this.version) {\n    case 0: {\n        const {code, digest} = this.multihash;\n        const multihash = _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.create(code, digest);\n        return CID.createV1(this.code, multihash);\n      }\n    case 1: {\n        return this;\n      }\n    default: {\n        throw Error(`Can not convert CID version ${ this.version } to version 0. This is a bug please report`);\n      }\n    }\n  }\n  equals(other) {\n    return other && this.code === other.code && this.version === other.version && _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.equals(this.multihash, other.multihash);\n  }\n  toString(base) {\n    const {bytes, version, _baseCache} = this;\n    switch (version) {\n    case 0:\n      return toStringV0(bytes, _baseCache, base || _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.encoder);\n    default:\n      return toStringV1(bytes, _baseCache, base || _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32.encoder);\n    }\n  }\n  toJSON() {\n    return {\n      code: this.code,\n      version: this.version,\n      hash: this.multihash.bytes\n    };\n  }\n  get [Symbol.toStringTag]() {\n    return 'CID';\n  }\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    return 'CID(' + this.toString() + ')';\n  }\n  static isCID(value) {\n    deprecate(/^0\\.0/, IS_CID_DEPRECATION);\n    return !!(value && (value[cidSymbol] || value.asCID === value));\n  }\n  get toBaseEncodedString() {\n    throw new Error('Deprecated, use .toString()');\n  }\n  get codec() {\n    throw new Error('\"codec\" property is deprecated, use integer \"code\" property instead');\n  }\n  get buffer() {\n    throw new Error('Deprecated .buffer property, use .bytes to get Uint8Array instead');\n  }\n  get multibaseName() {\n    throw new Error('\"multibaseName\" property is deprecated');\n  }\n  get prefix() {\n    throw new Error('\"prefix\" property is deprecated');\n  }\n  static asCID(value) {\n    if (value instanceof CID) {\n      return value;\n    } else if (value != null && value.asCID === value) {\n      const {version, code, multihash, bytes} = value;\n      return new CID(version, code, multihash, bytes || encodeCID(version, code, multihash.bytes));\n    } else if (value != null && value[cidSymbol] === true) {\n      const {version, multihash, code} = value;\n      const digest = _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.decode(multihash);\n      return CID.create(version, code, digest);\n    } else {\n      return null;\n    }\n  }\n  static create(version, code, digest) {\n    if (typeof code !== 'number') {\n      throw new Error('String codecs are no longer supported');\n    }\n    switch (version) {\n    case 0: {\n        if (code !== DAG_PB_CODE) {\n          throw new Error(`Version 0 CID must use dag-pb (code: ${ DAG_PB_CODE }) block encoding`);\n        } else {\n          return new CID(version, code, digest, digest.bytes);\n        }\n      }\n    case 1: {\n        const bytes = encodeCID(version, code, digest.bytes);\n        return new CID(version, code, digest, bytes);\n      }\n    default: {\n        throw new Error('Invalid version');\n      }\n    }\n  }\n  static createV0(digest) {\n    return CID.create(0, DAG_PB_CODE, digest);\n  }\n  static createV1(code, digest) {\n    return CID.create(1, code, digest);\n  }\n  static decode(bytes) {\n    const [cid, remainder] = CID.decodeFirst(bytes);\n    if (remainder.length) {\n      throw new Error('Incorrect length');\n    }\n    return cid;\n  }\n  static decodeFirst(bytes) {\n    const specs = CID.inspectBytes(bytes);\n    const prefixSize = specs.size - specs.multihashSize;\n    const multihashBytes = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_4__.coerce)(bytes.subarray(prefixSize, prefixSize + specs.multihashSize));\n    if (multihashBytes.byteLength !== specs.multihashSize) {\n      throw new Error('Incorrect length');\n    }\n    const digestBytes = multihashBytes.subarray(specs.multihashSize - specs.digestSize);\n    const digest = new _hashes_digest_js__WEBPACK_IMPORTED_MODULE_1__.Digest(specs.multihashCode, specs.digestSize, digestBytes, multihashBytes);\n    const cid = specs.version === 0 ? CID.createV0(digest) : CID.createV1(specs.codec, digest);\n    return [\n      cid,\n      bytes.subarray(specs.size)\n    ];\n  }\n  static inspectBytes(initialBytes) {\n    let offset = 0;\n    const next = () => {\n      const [i, length] = _varint_js__WEBPACK_IMPORTED_MODULE_0__.decode(initialBytes.subarray(offset));\n      offset += length;\n      return i;\n    };\n    let version = next();\n    let codec = DAG_PB_CODE;\n    if (version === 18) {\n      version = 0;\n      offset = 0;\n    } else if (version === 1) {\n      codec = next();\n    }\n    if (version !== 0 && version !== 1) {\n      throw new RangeError(`Invalid CID version ${ version }`);\n    }\n    const prefixSize = offset;\n    const multihashCode = next();\n    const digestSize = next();\n    const size = offset + digestSize;\n    const multihashSize = size - prefixSize;\n    return {\n      version,\n      codec,\n      multihashCode,\n      digestSize,\n      multihashSize,\n      size\n    };\n  }\n  static parse(source, base) {\n    const [prefix, bytes] = parseCIDtoBytes(source, base);\n    const cid = CID.decode(bytes);\n    cid._baseCache.set(prefix, source);\n    return cid;\n  }\n}\nconst parseCIDtoBytes = (source, base) => {\n  switch (source[0]) {\n  case 'Q': {\n      const decoder = base || _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc;\n      return [\n        _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix,\n        decoder.decode(`${ _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix }${ source }`)\n      ];\n    }\n  case _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix: {\n      const decoder = base || _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc;\n      return [\n        _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix,\n        decoder.decode(source)\n      ];\n    }\n  case _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32.prefix: {\n      const decoder = base || _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32;\n      return [\n        _bases_base32_js__WEBPACK_IMPORTED_MODULE_3__.base32.prefix,\n        decoder.decode(source)\n      ];\n    }\n  default: {\n      if (base == null) {\n        throw Error('To parse non base32 or base58btc encoded CID multibase decoder must be provided');\n      }\n      return [\n        source[0],\n        base.decode(source)\n      ];\n    }\n  }\n};\nconst toStringV0 = (bytes, cache, base) => {\n  const {prefix} = base;\n  if (prefix !== _bases_base58_js__WEBPACK_IMPORTED_MODULE_2__.base58btc.prefix) {\n    throw Error(`Cannot string encode V0 in ${ base.name } encoding`);\n  }\n  const cid = cache.get(prefix);\n  if (cid == null) {\n    const cid = base.encode(bytes).slice(1);\n    cache.set(prefix, cid);\n    return cid;\n  } else {\n    return cid;\n  }\n};\nconst toStringV1 = (bytes, cache, base) => {\n  const {prefix} = base;\n  const cid = cache.get(prefix);\n  if (cid == null) {\n    const cid = base.encode(bytes);\n    cache.set(prefix, cid);\n    return cid;\n  } else {\n    return cid;\n  }\n};\nconst DAG_PB_CODE = 112;\nconst SHA_256_CODE = 18;\nconst encodeCID = (version, code, multihash) => {\n  const codeOffset = _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodingLength(version);\n  const hashOffset = codeOffset + _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodingLength(code);\n  const bytes = new Uint8Array(hashOffset + multihash.byteLength);\n  _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodeTo(version, bytes, 0);\n  _varint_js__WEBPACK_IMPORTED_MODULE_0__.encodeTo(code, bytes, codeOffset);\n  bytes.set(multihash, hashOffset);\n  return bytes;\n};\nconst cidSymbol = Symbol.for('@ipld/js-cid/CID');\nconst readonly = {\n  writable: false,\n  configurable: false,\n  enumerable: true\n};\nconst hidden = {\n  writable: false,\n  enumerable: false,\n  configurable: false\n};\nconst version = '0.0.0-dev';\nconst deprecate = (range, message) => {\n  if (range.test(version)) {\n    console.warn(message);\n  } else {\n    throw new Error(message);\n  }\n};\nconst IS_CID_DEPRECATION = `CID.isCID(v) is deprecated and will be removed in the next major release.\nFollowing code pattern:\n\nif (CID.isCID(value)) {\n  doSomethingWithCID(value)\n}\n\nIs replaced with:\n\nconst cid = CID.asCID(value)\nif (cid) {\n  // Make sure to use cid instead of value\n  doSomethingWithCID(cid)\n}\n`;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/cid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/codecs/json.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/codecs/json.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   name: () => (/* binding */ name)\n/* harmony export */ });\nconst textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nconst name = 'json';\nconst code = 512;\nconst encode = node => textEncoder.encode(JSON.stringify(node));\nconst decode = data => JSON.parse(textDecoder.decode(data));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvY29kZWNzL2pzb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDTztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxtdWx0aWZvcm1hdHNcXGVzbVxcc3JjXFxjb2RlY3NcXGpzb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdGV4dEVuY29kZXIgPSBuZXcgVGV4dEVuY29kZXIoKTtcbmNvbnN0IHRleHREZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7XG5leHBvcnQgY29uc3QgbmFtZSA9ICdqc29uJztcbmV4cG9ydCBjb25zdCBjb2RlID0gNTEyO1xuZXhwb3J0IGNvbnN0IGVuY29kZSA9IG5vZGUgPT4gdGV4dEVuY29kZXIuZW5jb2RlKEpTT04uc3RyaW5naWZ5KG5vZGUpKTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSBkYXRhID0+IEpTT04ucGFyc2UodGV4dERlY29kZXIuZGVjb2RlKGRhdGEpKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/codecs/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/codecs/raw.js":
/*!*********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/codecs/raw.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   name: () => (/* binding */ name)\n/* harmony export */ });\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\nconst name = 'raw';\nconst code = 85;\nconst encode = node => (0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce)(node);\nconst decode = data => (0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce)(data);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvY29kZWNzL3Jhdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFxQztBQUM5QjtBQUNBO0FBQ0EsdUJBQXVCLGlEQUFNO0FBQzdCLHVCQUF1QixpREFBTSIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxtdWx0aWZvcm1hdHNcXGVzbVxcc3JjXFxjb2RlY3NcXHJhdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2VyY2UgfSBmcm9tICcuLi9ieXRlcy5qcyc7XG5leHBvcnQgY29uc3QgbmFtZSA9ICdyYXcnO1xuZXhwb3J0IGNvbnN0IGNvZGUgPSA4NTtcbmV4cG9ydCBjb25zdCBlbmNvZGUgPSBub2RlID0+IGNvZXJjZShub2RlKTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSBkYXRhID0+IGNvZXJjZShkYXRhKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/codecs/raw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js":
/*!************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/digest.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Digest: () => (/* binding */ Digest),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   equals: () => (/* binding */ equals)\n/* harmony export */ });\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../varint.js */ \"(ssr)/./node_modules/multiformats/esm/src/varint.js\");\n\n\nconst create = (code, digest) => {\n  const size = digest.byteLength;\n  const sizeOffset = _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodingLength(code);\n  const digestOffset = sizeOffset + _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodingLength(size);\n  const bytes = new Uint8Array(digestOffset + size);\n  _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodeTo(code, bytes, 0);\n  _varint_js__WEBPACK_IMPORTED_MODULE_1__.encodeTo(size, bytes, sizeOffset);\n  bytes.set(digest, digestOffset);\n  return new Digest(code, size, digest, bytes);\n};\nconst decode = multihash => {\n  const bytes = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce)(multihash);\n  const [code, sizeOffset] = _varint_js__WEBPACK_IMPORTED_MODULE_1__.decode(bytes);\n  const [size, digestOffset] = _varint_js__WEBPACK_IMPORTED_MODULE_1__.decode(bytes.subarray(sizeOffset));\n  const digest = bytes.subarray(sizeOffset + digestOffset);\n  if (digest.byteLength !== size) {\n    throw new Error('Incorrect length');\n  }\n  return new Digest(code, size, digest, bytes);\n};\nconst equals = (a, b) => {\n  if (a === b) {\n    return true;\n  } else {\n    return a.code === b.code && a.size === b.size && (0,_bytes_js__WEBPACK_IMPORTED_MODULE_0__.equals)(a.bytes, b.bytes);\n  }\n};\nclass Digest {\n  constructor(code, size, digest, bytes) {\n    this.code = code;\n    this.size = size;\n    this.digest = digest;\n    this.bytes = bytes;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js":
/*!************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/hasher.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hasher: () => (/* binding */ Hasher),\n/* harmony export */   from: () => (/* binding */ from)\n/* harmony export */ });\n/* harmony import */ var _digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n\nconst from = ({name, code, encode}) => new Hasher(name, code, encode);\nclass Hasher {\n  constructor(name, code, encode) {\n    this.name = name;\n    this.code = code;\n    this.encode = encode;\n  }\n  digest(input) {\n    if (input instanceof Uint8Array) {\n      const result = this.encode(input);\n      return result instanceof Uint8Array ? _digest_js__WEBPACK_IMPORTED_MODULE_0__.create(this.code, result) : result.then(digest => _digest_js__WEBPACK_IMPORTED_MODULE_0__.create(this.code, digest));\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL2hhc2hlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0IsZUFBZSxtQkFBbUI7QUFDbEM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLDhDQUFhLDRDQUE0Qyw4Q0FBYTtBQUNsSCxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcbXVsdGlmb3JtYXRzXFxlc21cXHNyY1xcaGFzaGVzXFxoYXNoZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgRGlnZXN0IGZyb20gJy4vZGlnZXN0LmpzJztcbmV4cG9ydCBjb25zdCBmcm9tID0gKHtuYW1lLCBjb2RlLCBlbmNvZGV9KSA9PiBuZXcgSGFzaGVyKG5hbWUsIGNvZGUsIGVuY29kZSk7XG5leHBvcnQgY2xhc3MgSGFzaGVyIHtcbiAgY29uc3RydWN0b3IobmFtZSwgY29kZSwgZW5jb2RlKSB7XG4gICAgdGhpcy5uYW1lID0gbmFtZTtcbiAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICAgIHRoaXMuZW5jb2RlID0gZW5jb2RlO1xuICB9XG4gIGRpZ2VzdChpbnB1dCkge1xuICAgIGlmIChpbnB1dCBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpIHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuZW5jb2RlKGlucHV0KTtcbiAgICAgIHJldHVybiByZXN1bHQgaW5zdGFuY2VvZiBVaW50OEFycmF5ID8gRGlnZXN0LmNyZWF0ZSh0aGlzLmNvZGUsIHJlc3VsdCkgOiByZXN1bHQudGhlbihkaWdlc3QgPT4gRGlnZXN0LmNyZWF0ZSh0aGlzLmNvZGUsIGRpZ2VzdCkpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aHJvdyBFcnJvcignVW5rbm93biB0eXBlLCBtdXN0IGJlIGJpbmFyeSB0eXBlJyk7XG4gICAgfVxuICB9XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/identity.js":
/*!**************************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/identity.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   identity: () => (/* binding */ identity)\n/* harmony export */ });\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n/* harmony import */ var _digest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n\n\nconst code = 0;\nconst name = 'identity';\nconst encode = _bytes_js__WEBPACK_IMPORTED_MODULE_0__.coerce;\nconst digest = input => _digest_js__WEBPACK_IMPORTED_MODULE_1__.create(code, encode(input));\nconst identity = {\n  code,\n  name,\n  encode,\n  digest\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL2lkZW50aXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQztBQUNDO0FBQ3RDO0FBQ0E7QUFDQSxlQUFlLDZDQUFNO0FBQ3JCLHdCQUF3Qiw4Q0FBYTtBQUM5QjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcbXVsdGlmb3JtYXRzXFxlc21cXHNyY1xcaGFzaGVzXFxpZGVudGl0eS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2VyY2UgfSBmcm9tICcuLi9ieXRlcy5qcyc7XG5pbXBvcnQgKiBhcyBEaWdlc3QgZnJvbSAnLi9kaWdlc3QuanMnO1xuY29uc3QgY29kZSA9IDA7XG5jb25zdCBuYW1lID0gJ2lkZW50aXR5JztcbmNvbnN0IGVuY29kZSA9IGNvZXJjZTtcbmNvbnN0IGRpZ2VzdCA9IGlucHV0ID0+IERpZ2VzdC5jcmVhdGUoY29kZSwgZW5jb2RlKGlucHV0KSk7XG5leHBvcnQgY29uc3QgaWRlbnRpdHkgPSB7XG4gIGNvZGUsXG4gIG5hbWUsXG4gIGVuY29kZSxcbiAgZGlnZXN0XG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/hashes/sha2.js":
/*!**********************************************************!*\
  !*** ./node_modules/multiformats/esm/src/hashes/sha2.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sha256: () => (/* binding */ sha256),\n/* harmony export */   sha512: () => (/* binding */ sha512)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var _hasher_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hasher.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n\n\n\nconst sha256 = (0,_hasher_js__WEBPACK_IMPORTED_MODULE_1__.from)({\n  name: 'sha2-256',\n  code: 18,\n  encode: input => (0,_bytes_js__WEBPACK_IMPORTED_MODULE_2__.coerce)(crypto__WEBPACK_IMPORTED_MODULE_0__.createHash('sha256').update(input).digest())\n});\nconst sha512 = (0,_hasher_js__WEBPACK_IMPORTED_MODULE_1__.from)({\n  name: 'sha2-512',\n  code: 19,\n  encode: input => (0,_bytes_js__WEBPACK_IMPORTED_MODULE_2__.coerce)(crypto__WEBPACK_IMPORTED_MODULE_0__.createHash('sha512').update(input).digest())\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaGFzaGVzL3NoYTIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEI7QUFDTztBQUNFO0FBQzlCLGVBQWUsZ0RBQUk7QUFDMUI7QUFDQTtBQUNBLG1CQUFtQixpREFBTSxDQUFDLDhDQUFpQjtBQUMzQyxDQUFDO0FBQ00sZUFBZSxnREFBSTtBQUMxQjtBQUNBO0FBQ0EsbUJBQW1CLGlEQUFNLENBQUMsOENBQWlCO0FBQzNDLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxUZWFtLTktTmlnaHRPZkNvZGUtXFxhcC15aWVsZHpcXG5vZGVfbW9kdWxlc1xcbXVsdGlmb3JtYXRzXFxlc21cXHNyY1xcaGFzaGVzXFxzaGEyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcnlwdG8gZnJvbSAnY3J5cHRvJztcbmltcG9ydCB7IGZyb20gfSBmcm9tICcuL2hhc2hlci5qcyc7XG5pbXBvcnQgeyBjb2VyY2UgfSBmcm9tICcuLi9ieXRlcy5qcyc7XG5leHBvcnQgY29uc3Qgc2hhMjU2ID0gZnJvbSh7XG4gIG5hbWU6ICdzaGEyLTI1NicsXG4gIGNvZGU6IDE4LFxuICBlbmNvZGU6IGlucHV0ID0+IGNvZXJjZShjcnlwdG8uY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKGlucHV0KS5kaWdlc3QoKSlcbn0pO1xuZXhwb3J0IGNvbnN0IHNoYTUxMiA9IGZyb20oe1xuICBuYW1lOiAnc2hhMi01MTInLFxuICBjb2RlOiAxOSxcbiAgZW5jb2RlOiBpbnB1dCA9PiBjb2VyY2UoY3J5cHRvLmNyZWF0ZUhhc2goJ3NoYTUxMicpLnVwZGF0ZShpbnB1dCkuZGlnZXN0KCkpXG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/hashes/sha2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/index.js":
/*!****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CID: () => (/* reexport safe */ _cid_js__WEBPACK_IMPORTED_MODULE_0__.CID),\n/* harmony export */   bytes: () => (/* reexport module object */ _bytes_js__WEBPACK_IMPORTED_MODULE_2__),\n/* harmony export */   digest: () => (/* reexport module object */ _hashes_digest_js__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   hasher: () => (/* reexport module object */ _hashes_hasher_js__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   varint: () => (/* reexport module object */ _varint_js__WEBPACK_IMPORTED_MODULE_1__)\n/* harmony export */ });\n/* harmony import */ var _cid_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cid.js */ \"(ssr)/./node_modules/multiformats/esm/src/cid.js\");\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./varint.js */ \"(ssr)/./node_modules/multiformats/esm/src/varint.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bytes.js */ \"(ssr)/./node_modules/multiformats/esm/src/bytes.js\");\n/* harmony import */ var _hashes_hasher_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hashes/hasher.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/hasher.js\");\n/* harmony import */ var _hashes_digest_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hashes/digest.js */ \"(ssr)/./node_modules/multiformats/esm/src/hashes/digest.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNPO0FBQ0Y7QUFDUztBQUNBIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxub2RlX21vZHVsZXNcXG11bHRpZm9ybWF0c1xcZXNtXFxzcmNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENJRCB9IGZyb20gJy4vY2lkLmpzJztcbmltcG9ydCAqIGFzIHZhcmludCBmcm9tICcuL3ZhcmludC5qcyc7XG5pbXBvcnQgKiBhcyBieXRlcyBmcm9tICcuL2J5dGVzLmpzJztcbmltcG9ydCAqIGFzIGhhc2hlciBmcm9tICcuL2hhc2hlcy9oYXNoZXIuanMnO1xuaW1wb3J0ICogYXMgZGlnZXN0IGZyb20gJy4vaGFzaGVzL2RpZ2VzdC5qcyc7XG5leHBvcnQge1xuICBDSUQsXG4gIGhhc2hlcixcbiAgZGlnZXN0LFxuICB2YXJpbnQsXG4gIGJ5dGVzXG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/src/varint.js":
/*!*****************************************************!*\
  !*** ./node_modules/multiformats/esm/src/varint.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encodeTo: () => (/* binding */ encodeTo),\n/* harmony export */   encodingLength: () => (/* binding */ encodingLength)\n/* harmony export */ });\n/* harmony import */ var _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../vendor/varint.js */ \"(ssr)/./node_modules/multiformats/esm/vendor/varint.js\");\n\nconst decode = (data, offset = 0) => {\n  const code = _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].decode(data, offset);\n  return [\n    code,\n    _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].decode.bytes\n  ];\n};\nconst encodeTo = (int, target, offset = 0) => {\n  _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].encode(int, target, offset);\n  return target;\n};\nconst encodingLength = int => {\n  return _vendor_varint_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].encodingLength(int);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXVsdGlmb3JtYXRzL2VzbS9zcmMvdmFyaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBeUM7QUFDbEM7QUFDUCxlQUFlLHlEQUFNO0FBQ3JCO0FBQ0E7QUFDQSxJQUFJLHlEQUFNO0FBQ1Y7QUFDQTtBQUNPO0FBQ1AsRUFBRSx5REFBTTtBQUNSO0FBQ0E7QUFDTztBQUNQLFNBQVMseURBQU07QUFDZiIsInNvdXJjZXMiOlsiRDpcXFRlYW0tOS1OaWdodE9mQ29kZS1cXGFwLXlpZWxkelxcbm9kZV9tb2R1bGVzXFxtdWx0aWZvcm1hdHNcXGVzbVxcc3JjXFx2YXJpbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHZhcmludCBmcm9tICcuLi92ZW5kb3IvdmFyaW50LmpzJztcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSAoZGF0YSwgb2Zmc2V0ID0gMCkgPT4ge1xuICBjb25zdCBjb2RlID0gdmFyaW50LmRlY29kZShkYXRhLCBvZmZzZXQpO1xuICByZXR1cm4gW1xuICAgIGNvZGUsXG4gICAgdmFyaW50LmRlY29kZS5ieXRlc1xuICBdO1xufTtcbmV4cG9ydCBjb25zdCBlbmNvZGVUbyA9IChpbnQsIHRhcmdldCwgb2Zmc2V0ID0gMCkgPT4ge1xuICB2YXJpbnQuZW5jb2RlKGludCwgdGFyZ2V0LCBvZmZzZXQpO1xuICByZXR1cm4gdGFyZ2V0O1xufTtcbmV4cG9ydCBjb25zdCBlbmNvZGluZ0xlbmd0aCA9IGludCA9PiB7XG4gIHJldHVybiB2YXJpbnQuZW5jb2RpbmdMZW5ndGgoaW50KTtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/src/varint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/vendor/base-x.js":
/*!********************************************************!*\
  !*** ./node_modules/multiformats/esm/vendor/base-x.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction base(ALPHABET, name) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n  var BASE_MAP = new Uint8Array(256);\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n    BASE_MAP[xc] = i;\n  }\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256);\n  var iFACTOR = Math.log(256) / Math.log(BASE);\n  function encode(source) {\n    if (source instanceof Uint8Array);\n    else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength);\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source);\n    }\n    if (!(source instanceof Uint8Array)) {\n      throw new TypeError('Expected Uint8Array');\n    }\n    if (source.length === 0) {\n      return '';\n    }\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    }\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size);\n    while (pbegin !== pend) {\n      var carry = source[pbegin];\n      var i = 0;\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      pbegin++;\n    }\n    var it2 = size - length;\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    }\n    var str = LEADER.repeat(zeroes);\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n    return str;\n  }\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n    if (source.length === 0) {\n      return new Uint8Array();\n    }\n    var psz = 0;\n    if (source[psz] === ' ') {\n      return;\n    }\n    var zeroes = 0;\n    var length = 0;\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    }\n    var size = (source.length - psz) * FACTOR + 1 >>> 0;\n    var b256 = new Uint8Array(size);\n    while (source[psz]) {\n      var carry = BASE_MAP[source.charCodeAt(psz)];\n      if (carry === 255) {\n        return;\n      }\n      var i = 0;\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      psz++;\n    }\n    if (source[psz] === ' ') {\n      return;\n    }\n    var it4 = size - length;\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n    var vch = new Uint8Array(zeroes + (size - it4));\n    var j = zeroes;\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n    return vch;\n  }\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) {\n      return buffer;\n    }\n    throw new Error(`Non-${ name } character`);\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\nvar src = base;\nvar _brrp__multiformats_scope_baseX = src;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_brrp__multiformats_scope_baseX);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/vendor/base-x.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/multiformats/esm/vendor/varint.js":
/*!********************************************************!*\
  !*** ./node_modules/multiformats/esm/vendor/varint.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar encode_1 = encode;\nvar MSB = 128, REST = 127, MSBALL = ~REST, INT = Math.pow(2, 31);\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n  while (num >= INT) {\n    out[offset++] = num & 255 | MSB;\n    num /= 128;\n  }\n  while (num & MSBALL) {\n    out[offset++] = num & 255 | MSB;\n    num >>>= 7;\n  }\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}\nvar decode = read;\nvar MSB$1 = 128, REST$1 = 127;\nfunction read(buf, offset) {\n  var res = 0, offset = offset || 0, shift = 0, counter = offset, b, l = buf.length;\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST$1) << shift : (b & REST$1) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB$1);\n  read.bytes = counter - offset;\n  return res;\n}\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\nvar length = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};\nvar varint = {\n  encode: encode_1,\n  decode: decode,\n  encodingLength: length\n};\nvar _brrp_varint = varint;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_brrp_varint);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/multiformats/esm/vendor/varint.js\n");

/***/ })

};
;