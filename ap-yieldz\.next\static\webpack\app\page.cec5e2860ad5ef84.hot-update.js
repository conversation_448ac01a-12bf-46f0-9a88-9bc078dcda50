"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/APYComparisonTable.tsx":
/*!***********************************************!*\
  !*** ./app/components/APYComparisonTable.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APYComparisonTable: () => (/* binding */ APYComparisonTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../blockchain/hooks/useAPYData */ \"(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Alert */ \"(app-pages-browser)/./app/components/Alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ APYComparisonTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction APYComparisonTable(param) {\n    let { onAssetSelect } = param;\n    _s();\n    const { apyData, loading, error, refresh } = (0,_blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData)();\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asset');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    // Debug logging\n    console.log('APYComparisonTable - APY Data:', apyData);\n    console.log('APYComparisonTable - Loading:', loading);\n    console.log('APYComparisonTable - Error:', error);\n    console.log('APYComparisonTable - Data length:', apyData.length);\n    // Log individual asset data\n    apyData.forEach((asset, index)=>{\n        console.log(\"Asset \".concat(index, \":\"), {\n            symbol: asset.symbol,\n            aaveSupplyAPY: asset.aaveSupplyAPY,\n            aaveBorrowAPY: asset.aaveBorrowAPY,\n            morphoSupplyAPY: asset.morphoSupplyAPY,\n            morphoBorrowAPY: asset.morphoBorrowAPY\n        });\n    });\n    const sortedData = [\n        ...apyData\n    ].sort((a, b)=>{\n        let valueA;\n        let valueB;\n        switch(sortBy){\n            case 'asset':\n                valueA = a.symbol;\n                valueB = b.symbol;\n                break;\n            case 'aaveSupply':\n                valueA = a.aaveSupplyAPY;\n                valueB = b.aaveSupplyAPY;\n                break;\n            case 'morphoSupply':\n                valueA = a.morphoSupplyAPY;\n                valueB = b.morphoSupplyAPY;\n                break;\n            case 'aaveBorrow':\n                valueA = a.aaveBorrowAPY;\n                valueB = b.aaveBorrowAPY;\n                break;\n            case 'morphoBorrow':\n                valueA = a.morphoBorrowAPY;\n                valueB = b.morphoBorrowAPY;\n                break;\n            default:\n                valueA = a.symbol;\n                valueB = b.symbol;\n        }\n        if (typeof valueA === 'string' && typeof valueB === 'string') {\n            return sortOrder === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);\n        }\n        const numA = Number(valueA);\n        const numB = Number(valueB);\n        return sortOrder === 'asc' ? numA - numB : numB - numA;\n    });\n    const handleSort = (column)=>{\n        if (sortBy === column) {\n            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortBy(column);\n            setSortOrder('desc'); // Default to desc for APY columns\n        }\n    };\n    const getBestRate = (aaveRate, morphoRate, type)=>{\n        if (type === 'supply') {\n            return aaveRate > morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        } else {\n            return aaveRate < morphoRate ? {\n                protocol: 'aave',\n                rate: aaveRate\n            } : {\n                protocol: 'morpho',\n                rate: morphoRate\n            };\n        }\n    };\n    // Loading state\n    if (loading && apyData.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"APY Comparison\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingState, {\n                    message: \"Loading APY data...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Error loading APY data\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refresh,\n                        className: \"mt-4 bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    // Check if APY data is empty or all zeros\n    const hasValidData = apyData.length > 0 && apyData.some((asset)=>asset.aaveSupplyAPY > 0 || asset.aaveBorrowAPY > 0 || asset.morphoSupplyAPY > 0 || asset.morphoBorrowAPY > 0);\n    if (!hasValidData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"APY Comparison\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-800 font-medium mb-2\",\n                                children: \"⚠️ APY Data Issue Detected\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-700 text-sm\",\n                                children: \"Assets are configured but APY rates are showing 0.00%. This usually means:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-left text-gray-600 space-y-2 max-w-md mx-auto mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• API is temporarily unavailable (common on testnets)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Network connection issues\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Aave subgraph is down\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refresh,\n                                disabled: loading,\n                                className: \"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 disabled:opacity-50 font-medium\",\n                                children: loading ? 'Loading...' : 'Force Refresh APY Data'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Note: Contract functionality works regardless of APY display issues\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Alert__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    type: \"warning\",\n                    message: error,\n                    dismissible: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"APY Comparison\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refresh,\n                                className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16,\n                                        className: loading ? 'animate-spin' : ''\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Compare lending and borrowing rates across protocols\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('asset'),\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveSupply'),\n                                        children: \"Aave Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoSupply'),\n                                        children: \"Morpho Supply APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('aaveBorrow'),\n                                        children: \"Aave Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('morphoBorrow'),\n                                        children: \"Morpho Borrow APY\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Best Protocol\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: sortedData.map((asset)=>{\n                                const bestSupply = getBestRate(asset.aaveSupplyAPY, asset.morphoSupplyAPY, 'supply');\n                                const bestBorrow = getBestRate(asset.aaveBorrowAPY, asset.morphoBorrowAPY, 'borrow');\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: asset.symbol\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestSupply.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoSupplyAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestSupply.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'aave' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.aaveBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'aave' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium flex items-center space-x-1 \".concat(bestBorrow.protocol === 'morpho' ? 'text-green-600' : 'text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            asset.morphoBorrowAPY.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    bestBorrow.protocol === 'morpho' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 60\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Supply:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestSupply.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestSupply.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Borrow:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(bestBorrow.protocol === 'aave' ? 'text-blue-600' : 'text-purple-600'),\n                                                        children: bestBorrow.protocol === 'aave' ? 'Aave' : 'Morpho'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onAssetSelect === null || onAssetSelect === void 0 ? void 0 : onAssetSelect(asset.symbol),\n                                                className: \"text-green-600 hover:text-green-900 flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Trade\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\APYComparisonTable.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(APYComparisonTable, \"buV6zq4itXyq2gJ/L7TMuIIDt7Q=\", false, function() {\n    return [\n        _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData\n    ];\n});\n_c = APYComparisonTable;\nvar _c;\n$RefreshReg$(_c, \"APYComparisonTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/APYComparisonTable.tsx\n"));

/***/ })

});