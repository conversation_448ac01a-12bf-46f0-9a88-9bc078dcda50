"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Dashboard */ \"(app-pages-browser)/./app/components/Dashboard.tsx\");\n/* harmony import */ var _components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/APYComparisonTable */ \"(app-pages-browser)/./app/components/APYComparisonTable.tsx\");\n/* harmony import */ var _components_Portfolio__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Portfolio */ \"(app-pages-browser)/./app/components/Portfolio.tsx\");\n/* harmony import */ var _components_TradingModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/TradingModal */ \"(app-pages-browser)/./app/components/TradingModal.tsx\");\n/* harmony import */ var _components_ContractTest__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/ContractTest */ \"(app-pages-browser)/./app/components/ContractTest.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [tradingModal, setTradingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        asset: '',\n        action: 'supply'\n    });\n    const handleAssetSelect = (asset)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action: 'supply'\n        });\n    };\n    const handlePortfolioAction = (asset, action)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action\n        });\n    };\n    const closeTradingModal = ()=>{\n        setTradingModal((prev)=>({\n                ...prev,\n                isOpen: false\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-green-50 to-green-100 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n                                children: [\n                                    \"Find the Best \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-700\",\n                                        children: \"APY Rates\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Compare lending rates across Aave and Morpho protocols. Optimize your DeFi strategy with real-time APY data and seamless cross-chain bridging.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('dashboard'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'dashboard' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('markets'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'markets' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Markets\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('portfolio'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'portfolio' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractTest__WEBPACK_IMPORTED_MODULE_6__.ContractTest, {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_2__.Dashboard, {}, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 39\n                    }, this),\n                    activeTab === 'markets' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__.APYComparisonTable, {\n                        onAssetSelect: handleAssetSelect\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 37\n                    }, this),\n                    activeTab === 'portfolio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Portfolio__WEBPACK_IMPORTED_MODULE_4__.Portfolio, {\n                        onActionClick: handlePortfolioAction\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 39\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Alligator?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: \"The most comprehensive DeFi APY aggregator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Real-time APY Comparison\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Compare rates across Aave and Morpho protocols in real-time to maximize your yields.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Cross-chain Bridging\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Seamlessly bridge assets between Avalanche and Base networks for optimal positioning.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Portfolio Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Track your positions across protocols and optimize your DeFi portfolio in one place.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"\\uD83D\\uDC0A Alligator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Your DeFi yield optimization companion\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Docs\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"GitHub\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradingModal__WEBPACK_IMPORTED_MODULE_5__.TradingModal, {\n                isOpen: tradingModal.isOpen,\n                onClose: closeTradingModal,\n                selectedAsset: tradingModal.asset,\n                defaultAction: tradingModal.action\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"3Dqkv54y2l/XSZczm1G4HWmloIc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});