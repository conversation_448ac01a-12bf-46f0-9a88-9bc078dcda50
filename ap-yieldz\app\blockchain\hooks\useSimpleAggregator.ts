import { useReadContract, useWrite<PERSON>ontract, useAccount } from 'wagmi';
import { parseUnits, formatUnits } from 'viem';
import { SIMPLE_LENDING_AGGREGATOR_ABI, ERC20_ABI } from '../abi/SimpleLendingAggregator';
import { LENDING_APY_AGGREGATOR_ADDRESS, getCurrentTokens } from '../config/wagmi';

export interface TokenInfo {
  symbol: string;
  address: string;
  decimals: number;
  balance?: string;
  allowance?: string;
}

export interface SimpleUserPosition {
  aaveSupplied: string;
  aaveBorrowed: string;
  lastUpdate: number;
}

// Hook to get supported assets
export function useSupportedAssets() {
  const { data: assets } = useReadContract({
    address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
    abi: SIMPLE_LENDING_AGGREGATOR_ABI,
    functionName: 'getSupportedAssets',
  });

  return assets || [];
}

// Hook to get user position for a specific asset
export function useUserPosition(asset: string) {
  const { address } = useAccount();
  
  const { data: position } = useReadContract({
    address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
    abi: SIMPLE_LENDING_AGGREGATOR_ABI,
    functionName: 'getUserPosition',
    args: [address as `0x${string}`, asset as `0x${string}`],
    query: {
      enabled: !!address && !!asset,
    },
  });

  if (!position) return null;

  return {
    aaveSupplied: formatUnits(position[0], 18),
    aaveBorrowed: formatUnits(position[1], 18),
    lastUpdate: Number(position[2]),
  } as SimpleUserPosition;
}

// Hook to get token balance
export function useTokenBalance(tokenAddress: string, decimals: number = 18) {
  const { address } = useAccount();
  
  const { data: balance } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'balanceOf',
    args: [address as `0x${string}`],
    query: {
      enabled: !!address && !!tokenAddress,
    },
  });

  return balance ? formatUnits(balance, decimals) : '0';
}

// Hook to get token allowance
export function useTokenAllowance(tokenAddress: string, decimals: number = 18) {
  const { address } = useAccount();
  
  const { data: allowance } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'allowance',
    args: [address as `0x${string}`, LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`],
    query: {
      enabled: !!address && !!tokenAddress,
    },
  });

  return allowance ? formatUnits(allowance, decimals) : '0';
}

// Hook for contract write operations
export function useSimpleAggregatorOperations() {
  const { writeContract } = useWriteContract();

  const supplyToAave = async (asset: string, amount: string, decimals: number = 18) => {
    return writeContract({
      address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
      abi: SIMPLE_LENDING_AGGREGATOR_ABI,
      functionName: 'supplyToAave',
      args: [asset as `0x${string}`, parseUnits(amount, decimals)],
    });
  };

  const borrowFromAave = async (asset: string, amount: string, decimals: number = 18) => {
    return writeContract({
      address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
      abi: SIMPLE_LENDING_AGGREGATOR_ABI,
      functionName: 'borrowFromAave',
      args: [asset as `0x${string}`, parseUnits(amount, decimals)],
    });
  };

  const withdrawFromAave = async (asset: string, amount: string, decimals: number = 18) => {
    return writeContract({
      address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
      abi: SIMPLE_LENDING_AGGREGATOR_ABI,
      functionName: 'withdrawFromAave',
      args: [asset as `0x${string}`, parseUnits(amount, decimals)],
    });
  };

  const repayToAave = async (asset: string, amount: string, decimals: number = 18) => {
    return writeContract({
      address: LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`,
      abi: SIMPLE_LENDING_AGGREGATOR_ABI,
      functionName: 'repayToAave',
      args: [asset as `0x${string}`, parseUnits(amount, decimals)],
    });
  };

  const approveToken = async (tokenAddress: string, amount: string, decimals: number = 18) => {
    return writeContract({
      address: tokenAddress as `0x${string}`,
      abi: ERC20_ABI,
      functionName: 'approve',
      args: [LENDING_APY_AGGREGATOR_ADDRESS as `0x${string}`, parseUnits(amount, decimals)],
    });
  };

  return {
    supplyToAave,
    borrowFromAave,
    withdrawFromAave,
    repayToAave,
    approveToken,
  };
}

// Hook to get token info with current network tokens
export function useTokenInfo() {
  const tokens = getCurrentTokens();
  
  const getTokenInfo = (symbol: string): TokenInfo | null => {
    const tokenAddress = tokens[symbol as keyof typeof tokens];
    if (!tokenAddress) return null;
    
    // Get decimals based on token type
    const decimals = symbol === 'USDC' || symbol === 'USDT' ? 6 : 18;
    
    return {
      symbol,
      address: tokenAddress,
      decimals,
    };
  };

  const getAllTokens = (): TokenInfo[] => {
    return Object.keys(tokens).map(symbol => getTokenInfo(symbol)).filter(Boolean) as TokenInfo[];
  };

  return {
    getTokenInfo,
    getAllTokens,
    tokens,
  };
}
