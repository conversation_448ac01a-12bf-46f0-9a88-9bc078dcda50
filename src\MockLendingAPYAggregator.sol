// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title MockLendingAPYAggregator
 * @dev Mock contract for demo purposes - simulates lending operations without real protocol integration
 */
contract MockLendingAPYAggregator is Ownable {
    
    // Position tracking for users
    struct UserPosition {
        uint256 aaveSupplied;
        uint256 aaveBorrowed;
        uint256 morphoSupplied;
        uint256 morphoBorrowed;
        uint256 lastUpdate;
    }
    
    // Supported assets
    mapping(address => bool) public supportedAssets;
    address[] public assetList;
    
    // User positions
    mapping(address => mapping(address => UserPosition)) public userPositions;
    
    // Mock balances for demo
    mapping(address => mapping(address => uint256)) public mockBalances;
    
    // Events
    event SupplyExecuted(address indexed user, address indexed asset, uint256 amount, bool useAave);
    event BorrowExecuted(address indexed user, address indexed asset, uint256 amount, bool useAave);
    event WithdrawExecuted(address indexed user, address indexed asset, uint256 amount, bool useAave);
    event RepayExecuted(address indexed user, address indexed asset, uint256 amount, bool useAave);
    
    constructor(address _owner) Ownable(_owner) {}
    
    function addSupportedAsset(address asset) external onlyOwner {
        supportedAssets[asset] = true;
        assetList.push(asset);
    }
    
    function supplyToAave(address asset, uint256 amount) external {
        require(supportedAssets[asset], "Unsupported asset");
        userPositions[msg.sender][asset].aaveSupplied += amount;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        emit SupplyExecuted(msg.sender, asset, amount, true);
    }
    
    function supplyToMorpho(address asset, uint256 amount) external payable {
        require(supportedAssets[asset], "Unsupported asset");
        userPositions[msg.sender][asset].morphoSupplied += amount;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        emit SupplyExecuted(msg.sender, asset, amount, false);
    }
    
    function borrowFromAave(address asset, uint256 amount) external {
        require(supportedAssets[asset], "Unsupported asset");
        userPositions[msg.sender][asset].aaveBorrowed += amount;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        emit BorrowExecuted(msg.sender, asset, amount, true);
    }
    
    function borrowFromMorpho(address asset, uint256 amount, address receiver) external payable {
        require(supportedAssets[asset], "Unsupported asset");
        userPositions[msg.sender][asset].morphoBorrowed += amount;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        emit BorrowExecuted(msg.sender, asset, amount, false);
    }
    
    function withdrawFromAave(address asset, uint256 amount) external returns (uint256) {
        require(supportedAssets[asset], "Unsupported asset");
        require(userPositions[msg.sender][asset].aaveSupplied >= amount, "Insufficient balance");
        userPositions[msg.sender][asset].aaveSupplied -= amount;
        emit WithdrawExecuted(msg.sender, asset, amount, true);
        return amount;
    }
    
    function withdrawFromMorpho(address asset, uint256 amount, address receiver) external payable {
        require(supportedAssets[asset], "Unsupported asset");
        require(userPositions[msg.sender][asset].morphoSupplied >= amount, "Insufficient balance");
        userPositions[msg.sender][asset].morphoSupplied -= amount;
        emit WithdrawExecuted(msg.sender, asset, amount, false);
    }
    
    function repayToAave(address asset, uint256 amount) external returns (uint256 repaid) {
        require(supportedAssets[asset], "Unsupported asset");
        uint256 debt = userPositions[msg.sender][asset].aaveBorrowed;
        repaid = amount > debt ? debt : amount;
        userPositions[msg.sender][asset].aaveBorrowed -= repaid;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        emit RepayExecuted(msg.sender, asset, repaid, true);
        return repaid;
    }
    
    function repayToMorpho(address asset, uint256 amount) external payable returns (uint256 repaid) {
        require(supportedAssets[asset], "Unsupported asset");
        uint256 debt = userPositions[msg.sender][asset].morphoBorrowed;
        repaid = amount > debt ? debt : amount;
        userPositions[msg.sender][asset].morphoBorrowed -= repaid;
        userPositions[msg.sender][asset].lastUpdate = block.timestamp;
        emit RepayExecuted(msg.sender, asset, repaid, false);
        return repaid;
    }
    
    function getAggregatorUserPosition(address user, address asset)
        external
        view
        returns (
            uint256 aaveSupplied,
            uint256 aaveBorrowed,
            uint256 morphoSupplied,
            uint256 morphoBorrowed,
            uint256 lastUpdate
        )
    {
        UserPosition memory pos = userPositions[user][asset];
        return (pos.aaveSupplied, pos.aaveBorrowed, pos.morphoSupplied, pos.morphoBorrowed, pos.lastUpdate);
    }
    
    function getSupportedAssets() external view returns (address[] memory) {
        return assetList;
    }
}
