"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/services/aaveAPI.ts":
/*!*********************************!*\
  !*** ./app/services/aaveAPI.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchLiveAaveRates: () => (/* binding */ fetchLiveAaveRates),\n/* harmony export */   testAaveAPIConnection: () => (/* binding */ testAaveAPIConnection)\n/* harmony export */ });\n// Aave API service for fetching live rates\n// Documentation: https://docs.aave.com/developers/deployed-contracts/v3-mainnet\n// Avalanche Fuji testnet token addresses\nconst FUJI_TOKEN_ADDRESSES = {\n    USDC: '0x5425890298aed601595a70AB815c96711a31Bc65',\n    WAVAX: '0xd00ae08403B9bbb9124bB305C09058E32C39A48c',\n    USDT: '0x1f1E7c893855525b303f99bDF5c3c05BE09ca251'\n};\n// Aave V3 Subgraph for Avalanche Fuji (correct URL)\nconst AAVE_SUBGRAPH_URL = 'https://api.thegraph.com/subgraphs/name/aave/protocol-v3-avalanche';\n// Avalanche Fuji Pool Address (correct address)\nconst AAVE_POOL_ADDRESS = '0x794a61358D6845594F94dc1DB02A252b5b4814aD';\n// Convert Aave rate format (ray) to percentage\nfunction rayToPercentage(ray) {\n    const RAY = 10 ** 27;\n    const SECONDS_PER_YEAR = 31536000;\n    const ratePerSecond = parseInt(ray) / RAY;\n    const ratePerYear = ratePerSecond * SECONDS_PER_YEAR;\n    return ratePerYear * 100;\n}\n// Fetch reserve data from Aave subgraph\nasync function fetchFromSubgraph() {\n    const query = '\\n    query GetReserves {\\n      reserves(\\n        where: {\\n          pool: \"******************************************\"\\n        }\\n      ) {\\n        id\\n        underlyingAsset\\n        name\\n        symbol\\n        decimals\\n        liquidityRate\\n        variableBorrowRate\\n        stableBorrowRate\\n        liquidityIndex\\n        variableBorrowIndex\\n        lastUpdateTimestamp\\n      }\\n    }\\n  ';\n    try {\n        const response = await fetch(AAVE_SUBGRAPH_URL, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                query\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Subgraph request failed: \".concat(response.status));\n        }\n        const data = await response.json();\n        if (data.errors) {\n            throw new Error(\"Subgraph errors: \".concat(JSON.stringify(data.errors)));\n        }\n        return data.data.reserves || [];\n    } catch (error) {\n        console.error('Error fetching from subgraph:', error);\n        throw error;\n    }\n}\n// Fallback: Use Aave's official API (if available for testnet)\nasync function fetchFromAaveAPI() {\n    // Note: Aave's official API might not support testnet\n    // This is a fallback that would work for mainnet\n    const url = 'https://aave-api-v2.aave.com/data/liquidity/v3?poolId=******************************************';\n    try {\n        const response = await fetch(url);\n        if (!response.ok) {\n            throw new Error(\"Aave API request failed: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data.reserves || [];\n    } catch (error) {\n        console.error('Error fetching from Aave API:', error);\n        throw error;\n    }\n}\n// Get live rates for supported tokens\nasync function fetchLiveAaveRates() {\n    try {\n        console.log('Fetching live Aave rates...');\n        // Try subgraph first, then fallback to API\n        let reserves;\n        try {\n            reserves = await fetchFromSubgraph();\n            console.log('Successfully fetched from subgraph');\n        } catch (subgraphError) {\n            console.log('Subgraph failed, trying Aave API...');\n            reserves = await fetchFromAaveAPI();\n            console.log('Successfully fetched from Aave API');\n        }\n        // Process the reserves data\n        const rates = {\n            USDC: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            },\n            WAVAX: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            },\n            USDT: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            }\n        };\n        reserves.forEach((reserve)=>{\n            const address = reserve.underlyingAsset.toLowerCase();\n            // Match by address\n            if (address === FUJI_TOKEN_ADDRESSES.USDC.toLowerCase()) {\n                rates.USDC.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.USDC.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            } else if (address === FUJI_TOKEN_ADDRESSES.WAVAX.toLowerCase()) {\n                rates.WAVAX.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.WAVAX.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            } else if (address === FUJI_TOKEN_ADDRESSES.USDT.toLowerCase()) {\n                rates.USDT.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.USDT.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            }\n        });\n        console.log('Live Aave rates:', rates);\n        return rates;\n    } catch (error) {\n        console.error('Failed to fetch live rates, using fallback:', error);\n        // Fallback to reasonable default rates if API fails\n        return {\n            USDC: {\n                supplyAPY: 4.25,\n                borrowAPY: 5.15\n            },\n            WAVAX: {\n                supplyAPY: 2.85,\n                borrowAPY: 4.25\n            },\n            USDT: {\n                supplyAPY: 4.15,\n                borrowAPY: 5.25\n            }\n        };\n    }\n}\n// Test function to verify API connectivity\nasync function testAaveAPIConnection() {\n    try {\n        await fetchLiveAaveRates();\n        return true;\n    } catch (error) {\n        console.error('Aave API connection test failed:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/services/aaveAPI.ts\n"));

/***/ })

});