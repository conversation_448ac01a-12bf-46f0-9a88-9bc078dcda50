// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Script} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {LendingAPYAggregator} from "../src/LendingAPYAggregator.sol";
import {MarketParams} from "lib/morpho-blue/src/interfaces/IMorpho.sol";

contract ConfigureAssets is Script {
    
    // Avalanche C-Chain token addresses
    address constant USDC = ******************************************;
    address constant USDT = ******************************************;
    address constant WETH = ******************************************;
    address constant WBTC = ******************************************;
    
    function run() public {
        address aggregatorAddress = vm.envAddress("AGGREGATOR_ADDRESS");
        LendingAPYAggregator aggregator = LendingAPYAggregator(aggregatorAddress);
        
        vm.startBroadcast();
        
        // Add supported assets
        aggregator.addSupportedAsset(USDC);
        aggregator.addSupportedAsset(USDT);
        aggregator.addSupportedAsset(WETH);
        aggregator.addSupportedAsset(WBTC);
        
        // Configure Morpho market parameters for each asset
        // These would be the actual Morpho Blue market parameters
        MarketParams memory usdcMarket = MarketParams({
            loanToken: USDC,
            collateralToken: WETH,
            oracle: 0x..., // Chainlink oracle address
            irm: 0x...,    // Interest rate model address
            lltv: 860000000000000000 // 86% LTV
        });
        
        aggregator.setMorphoMarketParams(USDC, usdcMarket);
        
        console.log("Assets configured successfully");
        
        vm.stopBroadcast();
    }
}
