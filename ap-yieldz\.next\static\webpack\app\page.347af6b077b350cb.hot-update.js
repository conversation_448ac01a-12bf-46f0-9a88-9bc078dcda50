"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/WalletInfo.tsx":
/*!***************************************!*\
  !*** ./app/components/WalletInfo.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useAccount.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useConnect.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,Info,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,Info,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,Info,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,Info,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,Info,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WalletInfo() {\n    _s();\n    const { address, isConnected, connector } = (0,wagmi__WEBPACK_IMPORTED_MODULE_2__.useAccount)();\n    const { connectors } = (0,wagmi__WEBPACK_IMPORTED_MODULE_3__.useConnect)();\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const walletInfo = [\n        {\n            name: 'Core Wallet',\n            description: 'Native Avalanche wallet - Best for AVAX ecosystem',\n            icon: '🔥',\n            recommended: true,\n            downloadUrl: 'https://core.app/',\n            features: [\n                'Native AVAX support',\n                'Built-in staking',\n                'Cross-chain bridge'\n            ]\n        },\n        {\n            name: 'MetaMask',\n            description: 'Most popular Ethereum wallet',\n            icon: '🦊',\n            recommended: false,\n            downloadUrl: 'https://metamask.io/',\n            features: [\n                'Wide compatibility',\n                'Browser extension',\n                'Mobile app'\n            ]\n        },\n        {\n            name: 'WalletConnect',\n            description: 'Connect mobile wallets via QR code',\n            icon: '📱',\n            recommended: false,\n            downloadUrl: 'https://walletconnect.com/',\n            features: [\n                'Mobile wallets',\n                'QR code connection',\n                '100+ supported wallets'\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 20,\n                                className: \"text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800\",\n                                children: \"Wallet Information\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowDetails(!showDetails),\n                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                        children: showDetails ? 'Hide Details' : 'Show Details'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 16,\n                                className: \"text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-green-800\",\n                                children: \"Wallet Connected\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-green-700 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Wallet:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    (connector === null || connector === void 0 ? void 0 : connector.name) || 'Unknown'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    address\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Network:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Avalanche Fuji Testnet\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 16,\n                                className: \"text-yellow-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-yellow-800\",\n                                children: \"No Wallet Connected\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-yellow-700\",\n                        children: \"Connect a wallet to start using the DeFi features\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-800 mb-2\",\n                        children: \"Available Wallets:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: connectors.map((connector)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: connector.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: connector.id === 'injected' ? 'Browser Extension' : 'Available'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, connector.id, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-800 mb-3\",\n                                children: \"Supported Wallets:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: walletInfo.map((wallet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: wallet.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                lineNumber: 104,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-gray-800 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: wallet.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                                lineNumber: 107,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            wallet.recommended && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded\",\n                                                                                children: \"Recommended\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                                lineNumber: 109,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                        lineNumber: 106,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: wallet.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                        lineNumber: 114,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: wallet.downloadUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Download\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                size: 12\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Features:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: wallet.features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded\",\n                                                                children: feature\n                                                            }, feature, false, {\n                                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, wallet.name, true, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-800 mb-3\",\n                                children: \"Network Configuration:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"Network Name:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Avalanche Fuji C-Chain\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"Chain ID:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"43113\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"RPC URL:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 break-all\",\n                                                    children: \"https://api.avax-test.network/ext/bc/C/rpc\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"Currency:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"AVAX\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_Info_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 16,\n                                            className: \"text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-blue-800\",\n                                            children: \"Need Help?\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"• Make sure you're on Avalanche Fuji testnet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"• Get test AVAX from the \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://faucet.avax.network/\",\n                                                    target: \"_blank\",\n                                                    className: \"underline\",\n                                                    children: \"Avalanche Faucet\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"• Core Wallet is recommended for the best Avalanche experience\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"• Refresh the page if wallets don't appear\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\WalletInfo.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletInfo, \"8KLtYjlKc9uFOEylhNmEYq7ONU8=\", false, function() {\n    return [\n        wagmi__WEBPACK_IMPORTED_MODULE_2__.useAccount,\n        wagmi__WEBPACK_IMPORTED_MODULE_3__.useConnect\n    ];\n});\n_c = WalletInfo;\nvar _c;\n$RefreshReg$(_c, \"WalletInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/WalletInfo.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Dashboard */ \"(app-pages-browser)/./app/components/Dashboard.tsx\");\n/* harmony import */ var _components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/APYComparisonTable */ \"(app-pages-browser)/./app/components/APYComparisonTable.tsx\");\n/* harmony import */ var _components_Portfolio__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Portfolio */ \"(app-pages-browser)/./app/components/Portfolio.tsx\");\n/* harmony import */ var _components_TradingModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/TradingModal */ \"(app-pages-browser)/./app/components/TradingModal.tsx\");\n/* harmony import */ var _components_ContractTest__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/ContractTest */ \"(app-pages-browser)/./app/components/ContractTest.tsx\");\n/* harmony import */ var _components_ContractSetup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ContractSetup */ \"(app-pages-browser)/./app/components/ContractSetup.tsx\");\n/* harmony import */ var _components_WalletInfo__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/WalletInfo */ \"(app-pages-browser)/./app/components/WalletInfo.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,PieChart,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [tradingModal, setTradingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        asset: '',\n        action: 'supply'\n    });\n    const handleAssetSelect = (asset)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action: 'supply'\n        });\n    };\n    const handlePortfolioAction = (asset, action)=>{\n        setTradingModal({\n            isOpen: true,\n            asset,\n            action\n        });\n    };\n    const closeTradingModal = ()=>{\n        setTradingModal((prev)=>({\n                ...prev,\n                isOpen: false\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-green-50 to-green-100 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n                                children: [\n                                    \"Find the Best \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-700\",\n                                        children: \"APY Rates\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Compare lending rates across Aave and Morpho protocols. Optimize your DeFi strategy with real-time APY data and seamless cross-chain bridging.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('dashboard'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'dashboard' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('markets'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'markets' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Markets\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('portfolio'),\n                                className: \"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'portfolio' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 rounded-full p-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-yellow-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-yellow-800\",\n                                        children: \"Setup Required\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-700 mb-4\",\n                                children: \"Before you can see APY rates and use the trading features, you need to add supported assets to your contract.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-700 text-sm\",\n                                children: 'Scroll down to the \"Contract Setup\" section and click \"Add All Assets\" to get started.'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractTest__WEBPACK_IMPORTED_MODULE_6__.ContractTest, {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractSetup__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WalletInfo__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_2__.Dashboard, {}, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 39\n                    }, this),\n                    activeTab === 'markets' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_APYComparisonTable__WEBPACK_IMPORTED_MODULE_3__.APYComparisonTable, {\n                        onAssetSelect: handleAssetSelect\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 37\n                    }, this),\n                    activeTab === 'portfolio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Portfolio__WEBPACK_IMPORTED_MODULE_4__.Portfolio, {\n                        onActionClick: handlePortfolioAction\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 39\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Alligator?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: \"The most comprehensive DeFi APY aggregator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Real-time APY Comparison\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Compare rates across Aave and Morpho protocols in real-time to maximize your yields.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Cross-chain Bridging\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Seamlessly bridge assets between Avalanche and Base networks for optimal positioning.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_PieChart_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium text-gray-900 mb-2\",\n                                            children: \"Portfolio Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Track your positions across protocols and optimize your DeFi portfolio in one place.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"\\uD83D\\uDC0A Alligator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Your DeFi yield optimization companion\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Docs\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"GitHub\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradingModal__WEBPACK_IMPORTED_MODULE_5__.TradingModal, {\n                isOpen: tradingModal.isOpen,\n                onClose: closeTradingModal,\n                selectedAsset: tradingModal.asset,\n                defaultAction: tradingModal.action\n            }, void 0, false, {\n                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"3Dqkv54y2l/XSZczm1G4HWmloIc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV3QztBQUNXO0FBQ2tCO0FBQ2xCO0FBQ007QUFDQTtBQUNGO0FBQ047QUFDc0I7QUFFeEQsU0FBU2E7O0lBQ3RCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHZCwrQ0FBUUEsQ0FBd0M7SUFDbEYsTUFBTSxDQUFDZSxjQUFjQyxnQkFBZ0IsR0FBR2hCLCtDQUFRQSxDQUk3QztRQUNEaUIsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFFBQVE7SUFDVjtJQUVBLE1BQU1DLG9CQUFvQixDQUFDRjtRQUN6QkYsZ0JBQWdCO1lBQ2RDLFFBQVE7WUFDUkM7WUFDQUMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxNQUFNRSx3QkFBd0IsQ0FBQ0gsT0FBZUM7UUFDNUNILGdCQUFnQjtZQUNkQyxRQUFRO1lBQ1JDO1lBQ0FDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1HLG9CQUFvQjtRQUN4Qk4sZ0JBQWdCTyxDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUVOLFFBQVE7WUFBTTtJQUNwRDtJQUVBLHFCQUNFOzswQkFFRSw4REFBQ087Z0JBQVFDLFdBQVU7MEJBQ2pCLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBR0YsV0FBVTs7b0NBQW9EO2tEQUNsRCw4REFBQ0c7d0NBQUtILFdBQVU7a0RBQWlCOzs7Ozs7Ozs7Ozs7MENBRWpELDhEQUFDSTtnQ0FBRUosV0FBVTswQ0FBK0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWxFLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNLO3dCQUFJTCxXQUFVO3dCQUF3Qk0sY0FBVzs7MENBQ2hELDhEQUFDQztnQ0FDQ0MsU0FBUyxJQUFNbkIsYUFBYTtnQ0FDNUJXLFdBQVcsd0VBSVYsT0FIQ1osY0FBYyxjQUNWLG9DQUNBOztrREFHTiw4REFBQ0wsZ0hBQVNBO3dDQUFDMEIsTUFBTTs7Ozs7O2tEQUNqQiw4REFBQ047a0RBQUs7Ozs7Ozs7Ozs7OzswQ0FFUiw4REFBQ0k7Z0NBQ0NDLFNBQVMsSUFBTW5CLGFBQWE7Z0NBQzVCVyxXQUFXLHdFQUlWLE9BSENaLGNBQWMsWUFDVixvQ0FDQTs7a0RBR04sOERBQUNGLGlIQUFVQTt3Q0FBQ3VCLE1BQU07Ozs7OztrREFDbEIsOERBQUNOO2tEQUFLOzs7Ozs7Ozs7Ozs7MENBRVIsOERBQUNJO2dDQUNDQyxTQUFTLElBQU1uQixhQUFhO2dDQUM1QlcsV0FBVyx3RUFJVixPQUhDWixjQUFjLGNBQ1Ysb0NBQ0E7O2tEQUdOLDhEQUFDSCxpSEFBTUE7d0NBQUN3QixNQUFNOzs7Ozs7a0RBQ2QsOERBQUNOO2tEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9kLDhEQUFDTztnQkFBS1YsV0FBVTs7a0NBRWQsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ1c7NENBQUlYLFdBQVU7NENBQTBCWSxNQUFLOzRDQUFPQyxRQUFPOzRDQUFlQyxTQUFRO3NEQUNqRiw0RUFBQ0M7Z0RBQUtDLGVBQWM7Z0RBQVFDLGdCQUFlO2dEQUFRQyxhQUFhO2dEQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUd6RSw4REFBQ0M7d0NBQUdwQixXQUFVO2tEQUF3Qzs7Ozs7Ozs7Ozs7OzBDQUV4RCw4REFBQ0k7Z0NBQUVKLFdBQVU7MENBQXVCOzs7Ozs7MENBR3BDLDhEQUFDSTtnQ0FBRUosV0FBVTswQ0FBMEI7Ozs7Ozs7Ozs7OztrQ0FNekMsOERBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDcEIsa0VBQVlBOzs7Ozs7Ozs7O2tDQUlmLDhEQUFDcUI7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNuQixpRUFBYUE7Ozs7Ozs7Ozs7a0NBSWhCLDhEQUFDb0I7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNsQiw4REFBVUE7Ozs7Ozs7Ozs7b0JBR1pNLGNBQWMsNkJBQWUsOERBQUNaLDREQUFTQTs7Ozs7b0JBQ3ZDWSxjQUFjLDJCQUFhLDhEQUFDWCw4RUFBa0JBO3dCQUFDNEMsZUFBZTFCOzs7Ozs7b0JBQzlEUCxjQUFjLDZCQUFlLDhEQUFDViw0REFBU0E7d0JBQUM0QyxlQUFlMUI7Ozs7Ozs7Ozs7OzswQkFJMUQsOERBQUNHO2dCQUFRQyxXQUFVOzBCQUNqQiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUN1QjtvQ0FBR3ZCLFdBQVU7OENBQXdDOzs7Ozs7OENBQ3RELDhEQUFDSTtvQ0FBRUosV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OztzQ0FHdkMsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ2pCLGdIQUFTQTtnREFBQ2lCLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV2Qiw4REFBQ29COzRDQUFHcEIsV0FBVTtzREFBeUM7Ozs7OztzREFDdkQsOERBQUNJOzRDQUFFSixXQUFVO3NEQUFnQjs7Ozs7Ozs7Ozs7OzhDQUsvQiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ2hCLGlIQUFRQTtnREFBQ2dCLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV0Qiw4REFBQ29COzRDQUFHcEIsV0FBVTtzREFBeUM7Ozs7OztzREFDdkQsOERBQUNJOzRDQUFFSixXQUFVO3NEQUFnQjs7Ozs7Ozs7Ozs7OzhDQUsvQiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ2YsaUhBQU1BO2dEQUFDZSxXQUFVOzs7Ozs7Ozs7OztzREFFcEIsOERBQUNvQjs0Q0FBR3BCLFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDSTs0Q0FBRUosV0FBVTtzREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNyQyw4REFBQ3dCO2dCQUFPeEIsV0FBVTswQkFDaEIsNEVBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNvQjtnQ0FBR3BCLFdBQVU7MENBQTJCOzs7Ozs7MENBQ3pDLDhEQUFDSTtnQ0FBRUosV0FBVTswQ0FBZ0I7Ozs7OzswQ0FDN0IsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ3lCO3dDQUFFQyxNQUFLO3dDQUFJMUIsV0FBVTtrREFBaUM7Ozs7OztrREFDdkQsOERBQUN5Qjt3Q0FBRUMsTUFBSzt3Q0FBSTFCLFdBQVU7a0RBQWlDOzs7Ozs7a0RBQ3ZELDhEQUFDeUI7d0NBQUVDLE1BQUs7d0NBQUkxQixXQUFVO2tEQUFpQzs7Ozs7O2tEQUN2RCw4REFBQ3lCO3dDQUFFQyxNQUFLO3dDQUFJMUIsV0FBVTtrREFBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTy9ELDhEQUFDckIsa0VBQVlBO2dCQUNYYSxRQUFRRixhQUFhRSxNQUFNO2dCQUMzQm1DLFNBQVM5QjtnQkFDVCtCLGVBQWV0QyxhQUFhRyxLQUFLO2dCQUNqQ29DLGVBQWV2QyxhQUFhSSxNQUFNOzs7Ozs7OztBQUkxQztHQXBNd0JQO0tBQUFBIiwic291cmNlcyI6WyJEOlxcVGVhbS05LU5pZ2h0T2ZDb2RlLVxcYXAteWllbGR6XFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgRGFzaGJvYXJkIH0gZnJvbSAnLi9jb21wb25lbnRzL0Rhc2hib2FyZCc7XHJcbmltcG9ydCB7IEFQWUNvbXBhcmlzb25UYWJsZSB9IGZyb20gJy4vY29tcG9uZW50cy9BUFlDb21wYXJpc29uVGFibGUnO1xyXG5pbXBvcnQgeyBQb3J0Zm9saW8gfSBmcm9tICcuL2NvbXBvbmVudHMvUG9ydGZvbGlvJztcclxuaW1wb3J0IHsgVHJhZGluZ01vZGFsIH0gZnJvbSAnLi9jb21wb25lbnRzL1RyYWRpbmdNb2RhbCc7XHJcbmltcG9ydCB7IENvbnRyYWN0VGVzdCB9IGZyb20gJy4vY29tcG9uZW50cy9Db250cmFjdFRlc3QnO1xyXG5pbXBvcnQgQ29udHJhY3RTZXR1cCBmcm9tICcuL2NvbXBvbmVudHMvQ29udHJhY3RTZXR1cCc7XHJcbmltcG9ydCBXYWxsZXRJbmZvIGZyb20gJy4vY29tcG9uZW50cy9XYWxsZXRJbmZvJztcclxuaW1wb3J0IHsgQmFyQ2hhcnQzLCBQaWVDaGFydCwgV2FsbGV0LCBUcmVuZGluZ1VwIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XHJcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPCdkYXNoYm9hcmQnIHwgJ21hcmtldHMnIHwgJ3BvcnRmb2xpbyc+KCdkYXNoYm9hcmQnKTtcclxuICBjb25zdCBbdHJhZGluZ01vZGFsLCBzZXRUcmFkaW5nTW9kYWxdID0gdXNlU3RhdGU8e1xyXG4gICAgaXNPcGVuOiBib29sZWFuO1xyXG4gICAgYXNzZXQ6IHN0cmluZztcclxuICAgIGFjdGlvbjogJ3N1cHBseScgfCAnYm9ycm93JyB8ICd3aXRoZHJhdycgfCAncmVwYXknO1xyXG4gIH0+KHtcclxuICAgIGlzT3BlbjogZmFsc2UsXHJcbiAgICBhc3NldDogJycsXHJcbiAgICBhY3Rpb246ICdzdXBwbHknLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVBc3NldFNlbGVjdCA9IChhc3NldDogc3RyaW5nKSA9PiB7XHJcbiAgICBzZXRUcmFkaW5nTW9kYWwoe1xyXG4gICAgICBpc09wZW46IHRydWUsXHJcbiAgICAgIGFzc2V0LFxyXG4gICAgICBhY3Rpb246ICdzdXBwbHknLFxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlUG9ydGZvbGlvQWN0aW9uID0gKGFzc2V0OiBzdHJpbmcsIGFjdGlvbjogJ3N1cHBseScgfCAnYm9ycm93JyB8ICd3aXRoZHJhdycgfCAncmVwYXknKSA9PiB7XHJcbiAgICBzZXRUcmFkaW5nTW9kYWwoe1xyXG4gICAgICBpc09wZW46IHRydWUsXHJcbiAgICAgIGFzc2V0LFxyXG4gICAgICBhY3Rpb24sXHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBjbG9zZVRyYWRpbmdNb2RhbCA9ICgpID0+IHtcclxuICAgIHNldFRyYWRpbmdNb2RhbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzT3BlbjogZmFsc2UgfSkpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxyXG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTUwIHRvLWdyZWVuLTEwMCBweS0xMlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICBGaW5kIHRoZSBCZXN0IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNzAwXCI+QVBZIFJhdGVzPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWItOCBtYXgtdy0zeGwgbXgtYXV0b1wiPlxyXG4gICAgICAgICAgICAgIENvbXBhcmUgbGVuZGluZyByYXRlcyBhY3Jvc3MgQWF2ZSBhbmQgTW9ycGhvIHByb3RvY29scy4gT3B0aW1pemUgeW91ciBEZUZpIHN0cmF0ZWd5IHdpdGggcmVhbC10aW1lIEFQWSBkYXRhIGFuZCBzZWFtbGVzcyBjcm9zcy1jaGFpbiBicmlkZ2luZy5cclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvc2VjdGlvbj5cclxuXHJcbiAgICAgIHsvKiBUYWIgTmF2aWdhdGlvbiAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS02XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiLW1iLXB4IGZsZXggc3BhY2UteC04XCIgYXJpYS1sYWJlbD1cIlRhYnNcIj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignZGFzaGJvYXJkJyl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB5LTIgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gJHtcclxuICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2Rhc2hib2FyZCdcclxuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWdyZWVuLTUwMCB0ZXh0LWdyZWVuLTYwMCdcclxuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8QmFyQ2hhcnQzIHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuPkRhc2hib2FyZDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ21hcmtldHMnKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xyXG4gICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnbWFya2V0cydcclxuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWdyZWVuLTUwMCB0ZXh0LWdyZWVuLTYwMCdcclxuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgICA8c3Bhbj5NYXJrZXRzPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYigncG9ydGZvbGlvJyl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB5LTIgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gJHtcclxuICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ3BvcnRmb2xpbydcclxuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWdyZWVuLTUwMCB0ZXh0LWdyZWVuLTYwMCdcclxuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8V2FsbGV0IHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuPlBvcnRmb2xpbzwvc3Bhbj5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L25hdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxyXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBwYi0xMlwiPlxyXG4gICAgICAgIHsvKiBJbXBvcnRhbnQgU2V0dXAgTm90aWNlICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOCBiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIHJvdW5kZWQtbGcgcC02XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTEwMCByb3VuZGVkLWZ1bGwgcC0yXCI+XHJcbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQteWVsbG93LTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxyXG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDl2Mm0wIDRoLjAxbS02LjkzOCA0aDEzLjg1NmMxLjU0IDAgMi41MDItMS42NjcgMS43MzItMi41TDEzLjczMiA0Yy0uNzctLjgzMy0xLjk2NC0uODMzLTIuNzMyIDBMMy43MzIgMTYuNWMtLjc3LjgzMy4xOTIgMi41IDEuNzMyIDIuNXpcIiAvPlxyXG4gICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXllbGxvdy04MDBcIj5TZXR1cCBSZXF1aXJlZDwvaDM+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTcwMCBtYi00XCI+XHJcbiAgICAgICAgICAgIEJlZm9yZSB5b3UgY2FuIHNlZSBBUFkgcmF0ZXMgYW5kIHVzZSB0aGUgdHJhZGluZyBmZWF0dXJlcywgeW91IG5lZWQgdG8gYWRkIHN1cHBvcnRlZCBhc3NldHMgdG8geW91ciBjb250cmFjdC5cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTcwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgIFNjcm9sbCBkb3duIHRvIHRoZSBcIkNvbnRyYWN0IFNldHVwXCIgc2VjdGlvbiBhbmQgY2xpY2sgXCJBZGQgQWxsIEFzc2V0c1wiIHRvIGdldCBzdGFydGVkLlxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogQ29udHJhY3QgQ29ubmVjdGlvbiBUZXN0ICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxyXG4gICAgICAgICAgPENvbnRyYWN0VGVzdCAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogQ29udHJhY3QgU2V0dXAgZm9yIGFkZGluZyBhc3NldHMgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XHJcbiAgICAgICAgICA8Q29udHJhY3RTZXR1cCAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogV2FsbGV0IEluZm9ybWF0aW9uICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxyXG4gICAgICAgICAgPFdhbGxldEluZm8gLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICBcclxuICAgICAgICB7YWN0aXZlVGFiID09PSAnZGFzaGJvYXJkJyAmJiA8RGFzaGJvYXJkIC8+fVxyXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdtYXJrZXRzJyAmJiA8QVBZQ29tcGFyaXNvblRhYmxlIG9uQXNzZXRTZWxlY3Q9e2hhbmRsZUFzc2V0U2VsZWN0fSAvPn1cclxuICAgICAgICB7YWN0aXZlVGFiID09PSAncG9ydGZvbGlvJyAmJiA8UG9ydGZvbGlvIG9uQWN0aW9uQ2xpY2s9e2hhbmRsZVBvcnRmb2xpb0FjdGlvbn0gLz59XHJcbiAgICAgIDwvbWFpbj5cclxuXHJcbiAgICAgIHsvKiBGZWF0dXJlcyBTZWN0aW9uICovfVxyXG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy13aGl0ZVwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIj5cclxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5XaHkgQ2hvb3NlIEFsbGlnYXRvcj88L2gyPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDBcIj5UaGUgbW9zdCBjb21wcmVoZW5zaXZlIERlRmkgQVBZIGFnZ3JlZ2F0b3I8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tMTAwIHJvdW5kZWQtZnVsbCB3LTE2IGgtMTYgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ncmVlbi02MDBcIiAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlJlYWwtdGltZSBBUFkgQ29tcGFyaXNvbjwvaDM+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgQ29tcGFyZSByYXRlcyBhY3Jvc3MgQWF2ZSBhbmQgTW9ycGhvIHByb3RvY29scyBpbiByZWFsLXRpbWUgdG8gbWF4aW1pemUgeW91ciB5aWVsZHMuXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTEwMCByb3VuZGVkLWZ1bGwgdy0xNiBoLTE2IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgPFBpZUNoYXJ0IGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ncmVlbi02MDBcIiAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPkNyb3NzLWNoYWluIEJyaWRnaW5nPC9oMz5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgICAgICBTZWFtbGVzc2x5IGJyaWRnZSBhc3NldHMgYmV0d2VlbiBBdmFsYW5jaGUgYW5kIEJhc2UgbmV0d29ya3MgZm9yIG9wdGltYWwgcG9zaXRpb25pbmcuXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTEwMCByb3VuZGVkLWZ1bGwgdy0xNiBoLTE2IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgPFdhbGxldCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JlZW4tNjAwXCIgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5Qb3J0Zm9saW8gTWFuYWdlbWVudDwvaDM+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgVHJhY2sgeW91ciBwb3NpdGlvbnMgYWNyb3NzIHByb3RvY29scyBhbmQgb3B0aW1pemUgeW91ciBEZUZpIHBvcnRmb2xpbyBpbiBvbmUgcGxhY2UuXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L3NlY3Rpb24+XHJcblxyXG4gICAgICB7LyogRm9vdGVyICovfVxyXG4gICAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHRleHQtd2hpdGUgcHktOFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMlwiPvCfkIogQWxsaWdhdG9yPC9oMz5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPllvdXIgRGVGaSB5aWVsZCBvcHRpbWl6YXRpb24gY29tcGFuaW9uPC9wPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgc3BhY2UteC02XCI+XHJcbiAgICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGVcIj5BYm91dDwvYT5cclxuICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZVwiPkRvY3M8L2E+XHJcbiAgICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGVcIj5TdXBwb3J0PC9hPlxyXG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlXCI+R2l0SHViPC9hPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Zvb3Rlcj5cclxuXHJcbiAgICAgIHsvKiBUcmFkaW5nIE1vZGFsICovfVxyXG4gICAgICA8VHJhZGluZ01vZGFsXHJcbiAgICAgICAgaXNPcGVuPXt0cmFkaW5nTW9kYWwuaXNPcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9e2Nsb3NlVHJhZGluZ01vZGFsfVxyXG4gICAgICAgIHNlbGVjdGVkQXNzZXQ9e3RyYWRpbmdNb2RhbC5hc3NldH1cclxuICAgICAgICBkZWZhdWx0QWN0aW9uPXt0cmFkaW5nTW9kYWwuYWN0aW9ufVxyXG4gICAgICAvPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkRhc2hib2FyZCIsIkFQWUNvbXBhcmlzb25UYWJsZSIsIlBvcnRmb2xpbyIsIlRyYWRpbmdNb2RhbCIsIkNvbnRyYWN0VGVzdCIsIkNvbnRyYWN0U2V0dXAiLCJXYWxsZXRJbmZvIiwiQmFyQ2hhcnQzIiwiUGllQ2hhcnQiLCJXYWxsZXQiLCJUcmVuZGluZ1VwIiwiSG9tZSIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInRyYWRpbmdNb2RhbCIsInNldFRyYWRpbmdNb2RhbCIsImlzT3BlbiIsImFzc2V0IiwiYWN0aW9uIiwiaGFuZGxlQXNzZXRTZWxlY3QiLCJoYW5kbGVQb3J0Zm9saW9BY3Rpb24iLCJjbG9zZVRyYWRpbmdNb2RhbCIsInByZXYiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwiaDEiLCJzcGFuIiwicCIsIm5hdiIsImFyaWEtbGFiZWwiLCJidXR0b24iLCJvbkNsaWNrIiwic2l6ZSIsIm1haW4iLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJoMyIsIm9uQXNzZXRTZWxlY3QiLCJvbkFjdGlvbkNsaWNrIiwiaDIiLCJmb290ZXIiLCJhIiwiaHJlZiIsIm9uQ2xvc2UiLCJzZWxlY3RlZEFzc2V0IiwiZGVmYXVsdEFjdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});