"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/TradingModal.tsx":
/*!*****************************************!*\
  !*** ./app/components/TradingModal.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingModal: () => (/* binding */ TradingModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useAccount.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useBalance.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useWriteContract.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/unit/parseUnits.js\");\n/* harmony import */ var _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../blockchain/hooks/useAPYData */ \"(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\");\n/* harmony import */ var _CustomConnectButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CustomConnectButton */ \"(app-pages-browser)/./app/components/CustomConnectButton.tsx\");\n/* harmony import */ var _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../blockchain/abi/SimpleLendingAggregator */ \"(app-pages-browser)/./app/blockchain/abi/SimpleLendingAggregator.ts\");\n/* harmony import */ var _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../blockchain/config/wagmi */ \"(app-pages-browser)/./app/blockchain/config/wagmi.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownCircle,ArrowUpCircle,DollarSign,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownCircle,ArrowUpCircle,DollarSign,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownCircle,ArrowUpCircle,DollarSign,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownCircle,ArrowUpCircle,DollarSign,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownCircle,ArrowUpCircle,DollarSign,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownCircle,ArrowUpCircle,DollarSign,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* __next_internal_client_entry_do_not_use__ TradingModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction TradingModal(param) {\n    let { isOpen, onClose, selectedAsset, defaultAction = 'supply' } = param;\n    _s();\n    const { address, isConnected } = (0,wagmi__WEBPACK_IMPORTED_MODULE_6__.useAccount)();\n    const { getAPYForAsset } = (0,_blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData)();\n    const [action, setAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultAction);\n    const [protocol, setProtocol] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('aave');\n    const [amount, setAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Get user's ETH balance as an example\n    const { data: balance } = (0,wagmi__WEBPACK_IMPORTED_MODULE_7__.useBalance)({\n        address: address\n    });\n    // Contract interactions\n    const { writeContract, data: hash, isPending, error: writeError } = (0,wagmi__WEBPACK_IMPORTED_MODULE_8__.useWriteContract)();\n    // Wait for transaction receipt\n    const { isLoading: isConfirming, isSuccess: isConfirmed } = (0,wagmi__WEBPACK_IMPORTED_MODULE_9__.useWaitForTransactionReceipt)({\n        hash\n    });\n    // Get token address from supported tokens\n    const getTokenAddress = (symbol)=>{\n        const tokens = _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.SUPPORTED_TOKENS.fuji;\n        return tokens[symbol] || \"0x\".concat(symbol.toLowerCase().padEnd(40, '0'));\n    };\n    const tokenAddress = getTokenAddress(selectedAsset);\n    // Mock token balance and allowance for now (you can implement real token balance checks)\n    const tokenBalance = {\n        formatted: '1000.0',\n        symbol: selectedAsset\n    };\n    const allowance = {\n        formatted: '0'\n    };\n    // Real contract operations\n    const operations = {\n        approveToken: async (token, amount)=>{\n            try {\n                await writeContract({\n                    address: token,\n                    abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_4__.ERC20_ABI,\n                    functionName: 'approve',\n                    args: [\n                        _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS,\n                        (0,viem__WEBPACK_IMPORTED_MODULE_10__.parseUnits)(amount, 18)\n                    ]\n                });\n            } catch (err) {\n                console.error('Approve error:', err);\n                throw err;\n            }\n        },\n        supplyToAave: async (token, amount)=>{\n            try {\n                console.log('Supply to Aave called with:', {\n                    contractAddress: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS,\n                    tokenAddress: token,\n                    amount: amount,\n                    parsedAmount: (0,viem__WEBPACK_IMPORTED_MODULE_10__.parseUnits)(amount, 18),\n                    userAddress: address\n                });\n                await writeContract({\n                    address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS,\n                    abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_4__.SIMPLE_LENDING_AGGREGATOR_ABI,\n                    functionName: 'supplyToAave',\n                    args: [\n                        token,\n                        (0,viem__WEBPACK_IMPORTED_MODULE_10__.parseUnits)(amount, 18)\n                    ]\n                });\n            } catch (err) {\n                console.error('Supply to Aave error:', err);\n                throw err;\n            }\n        },\n        borrowFromAave: async (token, amount)=>{\n            try {\n                await writeContract({\n                    address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS,\n                    abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_4__.SIMPLE_LENDING_AGGREGATOR_ABI,\n                    functionName: 'borrowFromAave',\n                    args: [\n                        token,\n                        (0,viem__WEBPACK_IMPORTED_MODULE_10__.parseUnits)(amount, 18)\n                    ]\n                });\n            } catch (err) {\n                console.error('Borrow from Aave error:', err);\n                throw err;\n            }\n        },\n        withdrawFromAave: async (token, amount)=>{\n            try {\n                await writeContract({\n                    address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS,\n                    abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_4__.SIMPLE_LENDING_AGGREGATOR_ABI,\n                    functionName: 'withdrawFromAave',\n                    args: [\n                        token,\n                        (0,viem__WEBPACK_IMPORTED_MODULE_10__.parseUnits)(amount, 18)\n                    ]\n                });\n            } catch (err) {\n                console.error('Withdraw from Aave error:', err);\n                throw err;\n            }\n        },\n        repayToAave: async (token, amount)=>{\n            try {\n                await writeContract({\n                    address: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS,\n                    abi: _blockchain_abi_SimpleLendingAggregator__WEBPACK_IMPORTED_MODULE_4__.SIMPLE_LENDING_AGGREGATOR_ABI,\n                    functionName: 'repayToAave',\n                    args: [\n                        token,\n                        (0,viem__WEBPACK_IMPORTED_MODULE_10__.parseUnits)(amount, 18)\n                    ]\n                });\n            } catch (err) {\n                console.error('Repay to Aave error:', err);\n                throw err;\n            }\n        },\n        // Note: Morpho operations removed since SimpleLendingAggregator only supports Aave\n        supplyToMorpho: async (token, amount)=>{\n            throw new Error('Morpho operations not supported in this contract');\n        },\n        borrowFromMorpho: async (token, amount, user)=>{\n            throw new Error('Morpho operations not supported in this contract');\n        },\n        withdrawFromMorpho: async (token, amount, user)=>{\n            throw new Error('Morpho operations not supported in this contract');\n        },\n        repayToMorpho: async (token, amount)=>{\n            throw new Error('Morpho operations not supported in this contract');\n        }\n    };\n    const assetData = getAPYForAsset(selectedAsset);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingModal.useEffect\": ()=>{\n            setAction(defaultAction);\n        }\n    }[\"TradingModal.useEffect\"], [\n        defaultAction\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingModal.useEffect\": ()=>{\n            if (assetData) {\n                // Auto-select best protocol based on action\n                if (action === 'supply') {\n                    setProtocol(assetData.bestSupplyProtocol);\n                } else if (action === 'borrow') {\n                    setProtocol(assetData.bestBorrowProtocol);\n                }\n            }\n        }\n    }[\"TradingModal.useEffect\"], [\n        action,\n        assetData\n    ]);\n    const needsApproval = ()=>{\n        if (!amount || action !== 'supply' && action !== 'repay') return false;\n        return parseFloat(allowance.formatted) < parseFloat(amount);\n    };\n    const handleApprove = async ()=>{\n        if (!amount) return;\n        setError('');\n        try {\n            await operations.approveToken(tokenAddress, amount);\n        } catch (err) {\n            setError('Failed to approve token');\n            console.error('Approval error:', err);\n        }\n    };\n    const handleTransaction = async ()=>{\n        if (!amount || !isConnected) return;\n        setError('');\n        // Validation checks\n        if (!_blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS || _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS.includes('YOUR_ACTUAL_CONTRACT_ADDRESS_HERE')) {\n            setError('Contract address not configured. Please update .env.local with your deployed contract address.');\n            return;\n        }\n        try {\n            console.log('Transaction initiated:', {\n                action,\n                protocol,\n                selectedAsset,\n                tokenAddress,\n                amount,\n                contractAddress: _blockchain_config_wagmi__WEBPACK_IMPORTED_MODULE_5__.LENDING_APY_AGGREGATOR_ADDRESS\n            });\n            switch(action){\n                case 'supply':\n                    if (protocol === 'aave') {\n                        await operations.supplyToAave(tokenAddress, amount);\n                    } else {\n                        setError('Morpho operations not supported with current contract');\n                    }\n                    break;\n                case 'borrow':\n                    if (protocol === 'aave') {\n                        await operations.borrowFromAave(tokenAddress, amount);\n                    } else {\n                        setError('Morpho operations not supported with current contract');\n                    }\n                    break;\n                case 'withdraw':\n                    if (protocol === 'aave') {\n                        await operations.withdrawFromAave(tokenAddress, amount);\n                    } else {\n                        setError('Morpho operations not supported with current contract');\n                    }\n                    break;\n                case 'repay':\n                    if (protocol === 'aave') {\n                        await operations.repayToAave(tokenAddress, amount);\n                    } else {\n                        setError('Morpho operations not supported with current contract');\n                    }\n                    break;\n            }\n            // Reset form on success (after transaction is confirmed)\n            if (isConfirmed) {\n                setAmount('');\n                onClose();\n            }\n        } catch (err) {\n            var _err_message, _err_message1, _err_message2;\n            console.error('Transaction error:', err);\n            // Parse error messages for better UX\n            let errorMessage = \"Failed to \".concat(action);\n            if (err === null || err === void 0 ? void 0 : (_err_message = err.message) === null || _err_message === void 0 ? void 0 : _err_message.includes('UnsupportedAsset')) {\n                errorMessage = \"\".concat(selectedAsset, \" is not supported by the contract. Please add it as a supported asset.\");\n            } else if (err === null || err === void 0 ? void 0 : (_err_message1 = err.message) === null || _err_message1 === void 0 ? void 0 : _err_message1.includes('insufficient funds')) {\n                errorMessage = 'Insufficient funds in your wallet.';\n            } else if (err === null || err === void 0 ? void 0 : (_err_message2 = err.message) === null || _err_message2 === void 0 ? void 0 : _err_message2.includes('user rejected')) {\n                errorMessage = 'Transaction rejected by user.';\n            } else if (err === null || err === void 0 ? void 0 : err.shortMessage) {\n                errorMessage = err.shortMessage;\n            } else if (err === null || err === void 0 ? void 0 : err.message) {\n                errorMessage = err.message;\n            }\n            setError(errorMessage);\n        }\n    };\n    const getAPYForCurrentSelection = ()=>{\n        if (!assetData) return 0;\n        if (action === 'supply' || action === 'withdraw') {\n            return protocol === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY;\n        } else {\n            return protocol === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY;\n        }\n    };\n    const getProtocolColor = (protocolName)=>{\n        return protocolName === 'aave' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800';\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: [\n                                action.charAt(0).toUpperCase() + action.slice(1),\n                                \" \",\n                                selectedAsset\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-800\",\n                                        children: \"Please connect your wallet to continue\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Action\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: [\n                                        'supply',\n                                        'borrow',\n                                        'withdraw',\n                                        'repay'\n                                    ].map((actionType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setAction(actionType),\n                                            className: \"flex items-center justify-center space-x-2 py-3 px-4 rounded-lg border transition-colors \".concat(action === actionType ? 'border-green-500 bg-green-50 text-green-700' : 'border-gray-300 hover:border-gray-400'),\n                                            children: [\n                                                actionType === 'supply' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 47\n                                                }, this),\n                                                actionType === 'borrow' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 47\n                                                }, this),\n                                                actionType === 'withdraw' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 49\n                                                }, this),\n                                                actionType === 'repay' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 46\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"capitalize\",\n                                                    children: actionType\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, actionType, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Protocol\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: [\n                                        'aave',\n                                        'morpho'\n                                    ].map((protocolType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setProtocol(protocolType),\n                                            className: \"py-3 px-4 rounded-lg border transition-colors \".concat(protocol === protocolType ? \"border-\".concat(protocolType === 'aave' ? 'blue' : 'purple', \"-500 \").concat(getProtocolColor(protocolType)) : 'border-gray-300 hover:border-gray-400'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium capitalize\",\n                                                        children: protocolType\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    assetData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: action === 'supply' || action === 'withdraw' ? \"\".concat((protocolType === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY).toFixed(2), \"% APY\") : \"\".concat((protocolType === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY).toFixed(2), \"% APY\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, protocolType, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Amount\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: amount,\n                                            onChange: (e)=>setAmount(e.target.value),\n                                            placeholder: \"0.00\",\n                                            className: \"w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                            disabled: !isConnected\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 top-3 text-gray-500\",\n                                            children: selectedAsset\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-sm text-gray-600\",\n                                    children: [\n                                        \"Balance: \",\n                                        tokenBalance.formatted,\n                                        \" \",\n                                        selectedAsset,\n                                        balance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: [\n                                                \"• ETH: \",\n                                                parseFloat(balance.formatted).toFixed(4)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this),\n                        assetData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Current APY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-green-600\",\n                                            children: [\n                                                getAPYForCurrentSelection().toFixed(2),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this),\n                                protocol === 'morpho' && (action === 'supply' || action === 'borrow') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-xs text-gray-500\",\n                                    children: \"* Bridge fee required for Morpho transactions (~$2-5)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownCircle_ArrowUpCircle_DollarSign_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-800\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your wallet to start trading\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CustomConnectButton__WEBPACK_IMPORTED_MODULE_3__.CustomConnectButton, {}, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    needsApproval() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleApprove,\n                                        disabled: isPending || isConfirming,\n                                        className: \"w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-300 text-white py-3 rounded-lg font-medium transition-colors\",\n                                        children: isPending || isConfirming ? 'Approving...' : \"Approve \".concat(selectedAsset)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleTransaction,\n                                        disabled: isPending || isConfirming || !amount || needsApproval(),\n                                        className: \"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-300 text-white py-3 rounded-lg font-medium transition-colors\",\n                                        children: isPending ? 'Confirming...' : isConfirming ? 'Processing...' : \"\".concat(action.charAt(0).toUpperCase() + action.slice(1), \" \").concat(selectedAsset)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 17\n                                    }, this),\n                                    hash && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 text-center\",\n                                        children: isConfirming ? 'Waiting for confirmation...' : isConfirmed ? '✅ Transaction confirmed!' : '⏳ Transaction submitted'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Team-9-NightOfCode-\\\\ap-yieldz\\\\app\\\\components\\\\TradingModal.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingModal, \"APGZGXutgc6MRxAPhiUBvTgWomY=\", false, function() {\n    return [\n        wagmi__WEBPACK_IMPORTED_MODULE_6__.useAccount,\n        _blockchain_hooks_useAPYData__WEBPACK_IMPORTED_MODULE_2__.useAPYData,\n        wagmi__WEBPACK_IMPORTED_MODULE_7__.useBalance,\n        wagmi__WEBPACK_IMPORTED_MODULE_8__.useWriteContract,\n        wagmi__WEBPACK_IMPORTED_MODULE_9__.useWaitForTransactionReceipt\n    ];\n});\n_c = TradingModal;\nvar _c;\n$RefreshReg$(_c, \"TradingModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/TradingModal.tsx\n"));

/***/ })

});