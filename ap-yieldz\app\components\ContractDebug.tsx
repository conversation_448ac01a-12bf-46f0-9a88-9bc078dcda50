'use client';

import { useAccount, useReadContract, usePublicClient } from 'wagmi';
import { LENDING_APY_AGGREGATOR_ADDRESS, getContractInfo } from '../blockchain/config/wagmi';
import { SIMPLE_LENDING_AGGREGATOR_ABI } from '../blockchain/abi/SimpleLendingAggregator';
import { useEffect, useState } from 'react';

export default function ContractDebug() {
  const { address, isConnected, chain } = useAccount();
  const publicClient = usePublicClient();
  const [contractCode, setContractCode] = useState<string | null>(null);
  const [isCheckingContract, setIsCheckingContract] = useState(false);

  const contractInfo = getContractInfo();

  // Check if contract exists by getting its bytecode
  useEffect(() => {
    const checkContract = async () => {
      if (!publicClient) return;

      setIsCheckingContract(true);
      try {
        const code = await publicClient.getCode({
          address: LENDING_APY_AGGREGATOR_ADDRESS,
        });
        setContractCode(code || '0x');
      } catch (error) {
        console.error('Error checking contract:', error);
        setContractCode(null);
      } finally {
        setIsCheckingContract(false);
      }
    };

    checkContract();
  }, [publicClient]);

  // Try to read contract code to see if it exists
  const { data: ownerData, error: ownerError } = useReadContract({
    address: LENDING_APY_AGGREGATOR_ADDRESS,
    abi: SIMPLE_LENDING_AGGREGATOR_ABI,
    functionName: 'owner',
  });

  const { data: assetsData, error: assetsError } = useReadContract({
    address: LENDING_APY_AGGREGATOR_ADDRESS,
    abi: SIMPLE_LENDING_AGGREGATOR_ABI,
    functionName: 'getSupportedAssets',
  });

  return (
    <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
      <h3 className="text-lg font-semibold text-red-800 mb-4">Contract Debug Information</h3>
      
      <div className="space-y-4 text-sm">
        <div>
          <strong>Wallet Connected:</strong> {isConnected ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Wallet Address:</strong> {address || 'Not connected'}
        </div>
        
        <div>
          <strong>Current Network:</strong> {chain?.name || 'Unknown'} (ID: {chain?.id || 'Unknown'})
        </div>
        
        <div>
          <strong>Expected Network:</strong> Avalanche Fuji (ID: 43113)
        </div>
        
        <div>
          <strong>Network Match:</strong> {chain?.id === 43113 ? '✅ Correct' : '❌ Wrong Network'}
        </div>
        
        <div>
          <strong>Contract Address (from config):</strong> {contractInfo.address}
        </div>
        
        <div>
          <strong>Environment Variable:</strong> {contractInfo.envVar || 'Not set'}
        </div>
        
        <div>
          <strong>Owner Function Error:</strong> 
          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
            {ownerError ? JSON.stringify(ownerError, null, 2) : 'No error'}
          </pre>
        </div>
        
        <div>
          <strong>Assets Function Error:</strong>
          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
            {assetsError ? JSON.stringify(assetsError, null, 2) : 'No error'}
          </pre>
        </div>
        
        <div>
          <strong>Owner Data:</strong> {ownerData ? String(ownerData) : 'No data'}
        </div>
        
        <div>
          <strong>Assets Data:</strong> {assetsData ? JSON.stringify(assetsData) : 'No data'}
        </div>

        <div>
          <strong>Contract Exists:</strong>
          {isCheckingContract ? (
            'Checking...'
          ) : contractCode === null ? (
            '❌ Error checking contract'
          ) : contractCode === '0x' ? (
            '❌ No contract found at this address'
          ) : (
            '✅ Contract found'
          )}
        </div>

        <div>
          <strong>Contract Code Length:</strong> {contractCode ? contractCode.length : 'Unknown'}
        </div>
      </div>

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-semibold text-yellow-800 mb-2">Troubleshooting Steps:</h4>
        <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-700">
          <li>Verify you're on Avalanche Fuji testnet (Chain ID: 43113)</li>
          <li>Check the contract address is correct: <code className="bg-yellow-100 px-1 rounded">0xc63Ee3b2ceF4857ba3EA8250F41d073C88696F99</code></li>
          <li>Verify the contract was deployed successfully on Fuji</li>
          <li>Check if you deployed SimpleLendingAggregator or just the Aave Pool</li>
          <li>Visit <a href="https://testnet.snowtrace.io/" target="_blank" className="underline">Fuji Snowtrace</a> to verify the contract</li>
        </ol>
      </div>

      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
        <h4 className="font-semibold text-blue-800 mb-2">Quick Verification:</h4>
        <p className="text-sm text-blue-700 mb-2">
          Visit this link to check your contract on Fuji Snowtrace:
        </p>
        <a 
          href={`https://testnet.snowtrace.io/address/${LENDING_APY_AGGREGATOR_ADDRESS}`}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 underline text-sm break-all"
        >
          https://testnet.snowtrace.io/address/{LENDING_APY_AGGREGATOR_ADDRESS}
        </a>
      </div>
    </div>
  );
}
