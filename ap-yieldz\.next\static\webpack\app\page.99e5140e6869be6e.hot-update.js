"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/blockchain/config/wagmi.ts":
/*!****************************************!*\
  !*** ./app/blockchain/config/wagmi.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_NETWORK: () => (/* binding */ CURRENT_NETWORK),\n/* harmony export */   LENDING_APY_AGGREGATOR_ADDRESS: () => (/* binding */ LENDING_APY_AGGREGATOR_ADDRESS),\n/* harmony export */   SUPPORTED_TOKENS: () => (/* binding */ SUPPORTED_TOKENS),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getContractInfo: () => (/* binding */ getContractInfo),\n/* harmony export */   getCurrentTokens: () => (/* binding */ getCurrentTokens)\n/* harmony export */ });\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi/chains */ \"(app-pages-browser)/./node_modules/viem/_esm/chains/definitions/avalancheFuji.js\");\n/* harmony import */ var _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @rainbow-me/rainbowkit/wallets */ \"(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-RTDGOYZC.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/viem/_esm/clients/transports/http.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/cookie.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n\n\n\n\n\nif (false) {}\nconst { wallets } = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultWallets)();\nconst config = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_0__.getDefaultConfig)({\n    appName: 'Alligator',\n    projectId: \"b11d3071871bb2c95781af8517fd1cfe\",\n    wallets: [\n        {\n            groupName: \"Core Wallet\",\n            wallets: [\n                _rainbow_me_rainbowkit_wallets__WEBPACK_IMPORTED_MODULE_1__.injectedWallet\n            ]\n        }\n    ],\n    chains: [\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji\n    ],\n    transports: {\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_2__.avalancheFuji.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_3__.http)(\"https://api.avax-test.network/ext/bc/C/rpc\" || 0)\n    },\n    ssr: true,\n    storage: (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.createStorage)({\n        storage: wagmi__WEBPACK_IMPORTED_MODULE_5__.cookieStorage\n    })\n});\n// Contract address - properly checksummed\nconst CONTRACT_ADDRESS = \"******************************************\" || 0;\nconst LENDING_APY_AGGREGATOR_ADDRESS = (0,viem__WEBPACK_IMPORTED_MODULE_6__.getAddress)(CONTRACT_ADDRESS);\n// Debug function to check contract\nconst getContractInfo = ()=>{\n    console.log('Contract Address:', LENDING_APY_AGGREGATOR_ADDRESS);\n    console.log('Environment Variable:', \"******************************************\");\n    return {\n        address: LENDING_APY_AGGREGATOR_ADDRESS,\n        envVar: \"******************************************\"\n    };\n};\nconst SUPPORTED_TOKENS = {\n    // Avalanche Fuji Testnet addresses for testing\n    fuji: {\n        USDC: '******************************************',\n        WAVAX: '******************************************',\n        USDT: '******************************************'\n    },\n    // Avalanche Mainnet addresses (for future use)\n    avalanche: {\n        USDC: '******************************************',\n        USDT: '******************************************',\n        WETH: '******************************************',\n        WBTC: '******************************************',\n        WAVAX: '******************************************'\n    },\n    // Base addresses (for future Morpho integration)\n    base: {\n        USDC: '******************************************',\n        WETH: '******************************************'\n    }\n};\n// Current network configuration - change this to switch between testnet and mainnet\nconst CURRENT_NETWORK = 'fuji'; // 'fuji' for testnet, 'avalanche' for mainnet\n// Get tokens for current network\nconst getCurrentTokens = ()=>SUPPORTED_TOKENS[CURRENT_NETWORK];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blockchain/config/wagmi.ts\n"));

/***/ })

});