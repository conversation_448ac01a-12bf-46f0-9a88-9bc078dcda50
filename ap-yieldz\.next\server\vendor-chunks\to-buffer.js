"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/to-buffer";
exports.ids = ["vendor-chunks/to-buffer"];
exports.modules = {

/***/ "(ssr)/./node_modules/to-buffer/index.js":
/*!*****************************************!*\
  !*** ./node_modules/to-buffer/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\nvar isArray = __webpack_require__(/*! isarray */ \"(ssr)/./node_modules/isarray/index.js\");\nvar typedArrayBuffer = __webpack_require__(/*! typed-array-buffer */ \"(ssr)/./node_modules/typed-array-buffer/index.js\");\n\nvar isView = ArrayBuffer.isView || function isView(obj) {\n\ttry {\n\t\ttypedArrayBuffer(obj);\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\nvar useUint8Array = typeof Uint8Array !== 'undefined';\nvar useArrayBuffer = typeof ArrayBuffer !== 'undefined'\n\t&& typeof Uint8Array !== 'undefined';\nvar useFromArrayBuffer = useArrayBuffer && (Buffer.prototype instanceof Uint8Array || Buffer.TYPED_ARRAY_SUPPORT);\n\nmodule.exports = function toBuffer(data, encoding) {\n\t/*\n\t * No need to do anything for exact instance\n\t * This is only valid when safe-buffer.Buffer === buffer.Buffer, i.e. when Buffer.from/Buffer.alloc existed\n\t */\n\tif (data instanceof Buffer) {\n\t\treturn data;\n\t}\n\n\tif (typeof data === 'string') {\n\t\treturn Buffer.from(data, encoding);\n\t}\n\n\t/*\n\t * Wrap any TypedArray instances and DataViews\n\t * Makes sense only on engines with full TypedArray support -- let Buffer detect that\n\t */\n\tif (useArrayBuffer && isView(data)) {\n\t\t// Bug in Node.js <6.3.1, which treats this as out-of-bounds\n\t\tif (data.byteLength === 0) {\n\t\t\treturn Buffer.alloc(0);\n\t\t}\n\n\t\t// When Buffer is based on Uint8Array, we can just construct it from ArrayBuffer\n\t\tif (useFromArrayBuffer) {\n\t\t\tvar res = Buffer.from(data.buffer, data.byteOffset, data.byteLength);\n\t\t\t/*\n\t\t\t * Recheck result size, as offset/length doesn't work on Node.js <5.10\n\t\t\t * We just go to Uint8Array case if this fails\n\t\t\t */\n\t\t\tif (res.byteLength === data.byteLength) {\n\t\t\t\treturn res;\n\t\t\t}\n\t\t}\n\n\t\t// Convert to Uint8Array bytes and then to Buffer\n\t\tvar uint8 = data instanceof Uint8Array ? data : new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n\t\tvar result = Buffer.from(uint8);\n\n\t\t/*\n\t\t * Let's recheck that conversion succeeded\n\t\t * We have .length but not .byteLength when useFromArrayBuffer is false\n\t\t */\n\t\tif (result.length === data.byteLength) {\n\t\t\treturn result;\n\t\t}\n\t}\n\n\t/*\n\t * Uint8Array in engines where Buffer.from might not work with ArrayBuffer, just copy over\n\t * Doesn't make sense with other TypedArray instances\n\t */\n\tif (useUint8Array && data instanceof Uint8Array) {\n\t\treturn Buffer.from(data);\n\t}\n\n\tvar isArr = isArray(data);\n\tif (isArr) {\n\t\tfor (var i = 0; i < data.length; i += 1) {\n\t\t\tvar x = data[i];\n\t\t\tif (\n\t\t\t\ttypeof x !== 'number'\n\t\t\t\t|| x < 0\n\t\t\t\t|| x > 255\n\t\t\t\t|| ~~x !== x // NaN and integer check\n\t\t\t) {\n\t\t\t\tthrow new RangeError('Array items must be numbers in the range 0-255.');\n\t\t\t}\n\t\t}\n\t}\n\n\t/*\n\t * Old Buffer polyfill on an engine that doesn't have TypedArray support\n\t * Also, this is from a different Buffer polyfill implementation then we have, as instanceof check failed\n\t * Convert to our current Buffer implementation\n\t */\n\tif (\n\t\tisArr || (\n\t\t\tBuffer.isBuffer(data)\n\t\t\t\t&& data.constructor\n\t\t\t\t&& typeof data.constructor.isBuffer === 'function'\n\t\t\t\t&& data.constructor.isBuffer(data)\n\t\t)\n\t) {\n\t\treturn Buffer.from(data);\n\t}\n\n\tthrow new TypeError('The \"data\" argument must be a string, an Array, a Buffer, a Uint8Array, or a DataView.');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/to-buffer/index.js\n");

/***/ })

};
;