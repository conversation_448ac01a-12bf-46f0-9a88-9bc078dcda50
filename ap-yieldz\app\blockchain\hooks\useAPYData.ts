import { useState, useEffect } from 'react';
import { APYData } from './useAggregator';
import { fetchLiveAaveRates } from '../../services/aaveAPI';

// For Aave-only integration, we'll use mock data for now
// Real Aave API integration can be added later
// const AAVE_API_URL = 'https://aave-api-v2.aave.com/data/liquidity/v2';

// Mapping of asset symbols to their respective IDs on Aave V3
const AAVE_RESERVE_IDS: Record<string, string> = {
    'USDC': '******************************************',
    'USDT': '******************************************',
    'WETH': '******************************************',
    'WBTC': '******************************************',
};

// Aave-only APY data for Fuji testnet (mock data for demonstration)
const AAVE_ONLY_APY_DATA: APYData[] = [
    {
        asset: 'usdc',
        symbol: 'USDC',
        aaveSupplyAPY: 4.25,
        aaveBorrowAPY: 5.15,
        morphoSupplyAPY: 0, // Disabled for Aave-only
        morphoBorrowAPY: 0, // Disabled for Aave-only
        bestSupplyProtocol: 'aave',
        bestBorrowProtocol: 'aave',
    },
    {
        asset: 'wavax',
        symbol: 'WAVAX',
        aaveSupplyAPY: 2.85,
        aaveBorrowAPY: 4.25,
        morphoSupplyAPY: 0, // Disabled for Aave-only
        morphoBorrowAPY: 0, // Disabled for Aave-only
        bestSupplyProtocol: 'aave',
        bestBorrowProtocol: 'aave',
    },
    {
        asset: 'usdt',
        symbol: 'USDT',
        aaveSupplyAPY: 4.15,
        aaveBorrowAPY: 5.25,
        morphoSupplyAPY: 0, // Disabled for Aave-only
        morphoBorrowAPY: 0, // Disabled for Aave-only
        bestSupplyProtocol: 'aave',
        bestBorrowProtocol: 'aave',
    },
];

export function useAPYData() {
  const [apyData, setApyData] = useState<APYData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAPYData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Fetching live Aave APY data...');

        // TEMPORARY: Force fallback data for testing
        // Remove this after confirming the display works
        const FORCE_FALLBACK = true;
        if (FORCE_FALLBACK) {
          console.log('TEMPORARY: Using fallback data for testing');
          setApyData(AAVE_ONLY_APY_DATA);
          setError('Temporarily using fallback data for testing');
          return;
        }

        // Fetch live rates from Aave API
        const liveRates = await fetchLiveAaveRates();
        console.log('Raw live rates from API:', liveRates);

        // Check if we got valid rates
        const hasValidRates = Object.values(liveRates).some(token =>
          token.supplyAPY > 0 || token.borrowAPY > 0
        );

        if (!hasValidRates) {
          console.log('API returned zero rates, using fallback data');
          throw new Error('API returned invalid rates (all zeros)');
        }

        // Convert to our APYData format
        const liveAPYData: APYData[] = [
          {
            asset: 'usdc',
            symbol: 'USDC',
            aaveSupplyAPY: liveRates.USDC.supplyAPY,
            aaveBorrowAPY: liveRates.USDC.borrowAPY,
            morphoSupplyAPY: 0, // Disabled for Aave-only
            morphoBorrowAPY: 0, // Disabled for Aave-only
            bestSupplyProtocol: 'aave',
            bestBorrowProtocol: 'aave',
          },
          {
            asset: 'wavax',
            symbol: 'WAVAX',
            aaveSupplyAPY: liveRates.WAVAX.supplyAPY,
            aaveBorrowAPY: liveRates.WAVAX.borrowAPY,
            morphoSupplyAPY: 0, // Disabled for Aave-only
            morphoBorrowAPY: 0, // Disabled for Aave-only
            bestSupplyProtocol: 'aave',
            bestBorrowProtocol: 'aave',
          },
          {
            asset: 'usdt',
            symbol: 'USDT',
            aaveSupplyAPY: liveRates.USDT.supplyAPY,
            aaveBorrowAPY: liveRates.USDT.borrowAPY,
            morphoSupplyAPY: 0, // Disabled for Aave-only
            morphoBorrowAPY: 0, // Disabled for Aave-only
            bestSupplyProtocol: 'aave',
            bestBorrowProtocol: 'aave',
          },
        ];

        setApyData(liveAPYData);
        setError(null);
        console.log('Live APY data loaded successfully:', liveAPYData);

      } catch (err: any) {
        console.error('Error loading live APY data:', err);
        console.log('Falling back to mock data...');

        // Fallback to mock data if API fails
        setApyData(AAVE_ONLY_APY_DATA);
        setError('Using fallback APY data - live rates unavailable');
      } finally {
        setLoading(false);
      }
    };

    fetchAPYData();

    // Refresh data every 60 seconds for live rates
    const interval = setInterval(fetchAPYData, 60000);

    return () => clearInterval(interval);
  }, []);

  const getAPYForAsset = (asset: string): APYData | undefined => {
    const result = apyData.find(data => data.asset === asset.toLowerCase() || data.symbol === asset.toUpperCase());
    console.log(`Getting APY for ${asset}:`, result);
    return result;
  };

  return {
    apyData,
    loading,
    error,
    getAPYForAsset,
    refresh: async () => {
      setLoading(true);
      try {
        console.log('Manually refreshing APY data...');
        const liveRates = await fetchLiveAaveRates();
        console.log('Manual refresh - Raw live rates:', liveRates);

        // Check if we got valid rates
        const hasValidRates = Object.values(liveRates).some(token =>
          token.supplyAPY > 0 || token.borrowAPY > 0
        );

        if (!hasValidRates) {
          console.log('Manual refresh - API returned zero rates, using fallback data');
          throw new Error('API returned invalid rates (all zeros)');
        }

        const liveAPYData: APYData[] = [
          {
            asset: 'usdc',
            symbol: 'USDC',
            aaveSupplyAPY: liveRates.USDC.supplyAPY,
            aaveBorrowAPY: liveRates.USDC.borrowAPY,
            morphoSupplyAPY: 0,
            morphoBorrowAPY: 0,
            bestSupplyProtocol: 'aave',
            bestBorrowProtocol: 'aave',
          },
          {
            asset: 'wavax',
            symbol: 'WAVAX',
            aaveSupplyAPY: liveRates.WAVAX.supplyAPY,
            aaveBorrowAPY: liveRates.WAVAX.borrowAPY,
            morphoSupplyAPY: 0,
            morphoBorrowAPY: 0,
            bestSupplyProtocol: 'aave',
            bestBorrowProtocol: 'aave',
          },
          {
            asset: 'usdt',
            symbol: 'USDT',
            aaveSupplyAPY: liveRates.USDT.supplyAPY,
            aaveBorrowAPY: liveRates.USDT.borrowAPY,
            morphoSupplyAPY: 0,
            morphoBorrowAPY: 0,
            bestSupplyProtocol: 'aave',
            bestBorrowProtocol: 'aave',
          },
        ];

        setApyData(liveAPYData);
        setError(null);
      } catch (error) {
        console.error('Manual refresh failed:', error);
        setApyData(AAVE_ONLY_APY_DATA);
        setError('Refresh failed - using fallback data');
      } finally {
        setLoading(false);
      }
    },
  };
}

// Historical APY data for charts
export function useHistoricalAPY(asset: string, protocol: 'aave' | 'morpho', days: number = 30) {
  const [data, setData] = useState<Array<{ date: string; supplyAPY: number; borrowAPY: number }>>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const generateMockHistoricalData = () => {
      // Get base APY from Aave-only data
      const assetData = AAVE_ONLY_APY_DATA.find(d => d.asset === asset.toLowerCase() || d.symbol === asset.toUpperCase());
      if (!assetData) {
        setData([]);
        setLoading(false);
        return;
      }

      const baseSupplyAPY = protocol === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY;
      const baseBorrowAPY = protocol === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY;

      // Generate realistic historical data with small variations
      const historicalData = Array.from({ length: days }).map((_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (days - i));
        
        // Add small random variations to make it look realistic
        const supplyVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation
        const borrowVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation
        
        return {
          date: date.toISOString().split('T')[0],
          supplyAPY: Math.max(0, baseSupplyAPY + supplyVariation),
          borrowAPY: Math.max(0, baseBorrowAPY + borrowVariation),
        };
      });
      
      setData(historicalData);
      setLoading(false);
    };

    if (asset) {
      generateMockHistoricalData();
    }
  }, [asset, protocol, days]);

  return { data, loading };
}
