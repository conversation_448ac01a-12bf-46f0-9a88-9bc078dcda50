'use client';

import { useState } from 'react';
import { fetchLiveAaveRates, testAaveAPIConnection } from '../services/aaveAPI';
import { Play, CheckCircle, XCircle, Clock } from 'lucide-react';

export default function APITestPanel() {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runTest = async () => {
    setTesting(true);
    setError(null);
    setResults(null);

    try {
      console.log('Starting API test...');
      
      // Test connection first
      const connectionTest = await testAaveAPIConnection();
      console.log('Connection test result:', connectionTest);
      
      // Fetch rates
      const rates = await fetchLiveAaveRates();
      console.log('Rates result:', rates);
      
      setResults({
        connection: connectionTest,
        rates: rates,
        timestamp: new Date().toISOString(),
      });
      
    } catch (err: any) {
      console.error('Test failed:', err);
      setError(err.message || 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">API Test Panel</h3>
        <button
          onClick={runTest}
          disabled={testing}
          className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {testing ? (
            <>
              <Clock size={16} className="animate-spin" />
              <span>Testing...</span>
            </>
          ) : (
            <>
              <Play size={16} />
              <span>Run Test</span>
            </>
          )}
        </button>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <XCircle size={16} className="text-red-600" />
            <span className="text-red-800 font-medium">Test Failed</span>
          </div>
          <p className="text-red-700 text-sm mt-1">{error}</p>
        </div>
      )}

      {results && (
        <div className="space-y-4">
          {/* Connection Test Results */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2">Connection Test</h4>
            <div className="flex items-center space-x-2 mb-2">
              {results.connection.success ? (
                <CheckCircle size={16} className="text-green-600" />
              ) : (
                <XCircle size={16} className="text-red-600" />
              )}
              <span className={`font-medium ${results.connection.success ? 'text-green-800' : 'text-red-800'}`}>
                {results.connection.success ? 'Success' : 'Failed'}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <p><strong>Data Source:</strong> {results.connection.dataSource}</p>
              {results.connection.error && (
                <p><strong>Error:</strong> {results.connection.error}</p>
              )}
            </div>
          </div>

          {/* Rates Results */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-3">Current Rates</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(results.rates).map(([token, data]: [string, any]) => (
                <div key={token} className="bg-white rounded p-3 border">
                  <div className="font-medium text-gray-800 mb-2">{token}</div>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Supply:</span>
                      <span className="font-medium text-green-600">
                        {data.supplyAPY.toFixed(3)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Borrow:</span>
                      <span className="font-medium text-red-600">
                        {data.borrowAPY.toFixed(3)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Test Metadata */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2">Test Details</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>Timestamp:</strong> {new Date(results.timestamp).toLocaleString()}</p>
              <p><strong>Status:</strong> {results.connection.success ? 'All systems operational' : 'Using fallback data'}</p>
            </div>
          </div>
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500 border-t pt-3">
        <p>This panel tests the Aave API integration and displays detailed results.</p>
        <p>Check the browser console for additional debugging information.</p>
      </div>
    </div>
  );
}
