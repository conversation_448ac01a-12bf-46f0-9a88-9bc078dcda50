'use client';

import { useState, useEffect } from 'react';
import { useAccount, useWriteContract, useReadContract } from 'wagmi';
import { SIMPLE_LENDING_AGGREGATOR_ABI } from '../blockchain/abi/SimpleLendingAggregator';
import { LENDING_APY_AGGREGATOR_ADDRESS, getCurrentTokens } from '../blockchain/config/wagmi';

export default function ContractSetup() {
  const { address, isConnected } = useAccount();
  const { writeContract } = useWriteContract();
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState('');

  const tokens = getCurrentTokens();

  // Check current supported assets
  const { data: supportedAssets, refetch: refetchAssets } = useReadContract({
    address: LENDING_APY_AGGREGATOR_ADDRESS,
    abi: SIMPLE_LENDING_AGGREGATOR_ABI,
    functionName: 'getSupportedAssets',
  });

  const currentAssets = (supportedAssets as string[]) || [];
  const hasUSDC = currentAssets.includes(tokens.USDC);
  const hasWAVAX = currentAssets.includes(tokens.WAVAX);

  const addSupportedAsset = async (tokenAddress: string, tokenSymbol: string) => {
    if (!isConnected) {
      setStatus('Please connect your wallet first');
      return;
    }

    setIsLoading(true);
    setStatus(`Adding ${tokenSymbol} as supported asset...`);

    try {
      await writeContract({
        address: LENDING_APY_AGGREGATOR_ADDRESS,
        abi: SIMPLE_LENDING_AGGREGATOR_ABI,
        functionName: 'addSupportedAsset',
        args: [tokenAddress as `0x${string}`],
      });
      
      setStatus(`${tokenSymbol} added successfully! Please wait for transaction confirmation.`);
    } catch (error) {
      console.error('Error adding supported asset:', error);
      setStatus(`Failed to add ${tokenSymbol}. Check console for details.`);
    } finally {
      setIsLoading(false);
    }
  };

  const addAllAssets = async () => {
    if (!isConnected) {
      setStatus('Please connect your wallet first');
      return;
    }

    setIsLoading(true);
    setStatus('Adding all supported assets...');

    try {
      // Add USDC first
      setStatus('Adding USDC...');
      const usdcTx = await writeContract({
        address: LENDING_APY_AGGREGATOR_ADDRESS,
        abi: SIMPLE_LENDING_AGGREGATOR_ABI,
        functionName: 'addSupportedAsset',
        args: [tokens.USDC as `0x${string}`],
      });
      console.log('USDC transaction:', usdcTx);

      // Wait a bit between transactions
      setStatus('USDC added! Adding WAVAX...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Add WAVAX
      const wavaxTx = await writeContract({
        address: LENDING_APY_AGGREGATOR_ADDRESS,
        abi: SIMPLE_LENDING_AGGREGATOR_ABI,
        functionName: 'addSupportedAsset',
        args: [tokens.WAVAX as `0x${string}`],
      });
      console.log('WAVAX transaction:', wavaxTx);

      setStatus('✅ All assets added successfully! Please wait for transaction confirmations, then refresh the page.');

      // Refetch assets after a delay
      setTimeout(() => {
        refetchAssets();
      }, 5000);
    } catch (error: any) {
      console.error('Error adding assets:', error);
      setStatus(`❌ Failed to add assets: ${error.message || 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isConnected) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">Contract Setup Required</h3>
        <p className="text-yellow-700">Please connect your wallet to set up the contract with supported assets.</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 className="text-lg font-semibold text-blue-800 mb-4">Contract Setup</h3>
      
      <div className="mb-4">
        <p className="text-blue-700 mb-2">
          <strong>Contract Address:</strong> {LENDING_APY_AGGREGATOR_ADDRESS}
        </p>
        <p className="text-blue-700 mb-2">
          <strong>Connected as:</strong> {address}
        </p>
        <div className="text-blue-700 mb-4">
          <strong>Current Assets:</strong>
          <div className="mt-1 space-y-1">
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${hasUSDC ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>USDC {hasUSDC ? '✅' : '❌'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${hasWAVAX ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>WAVAX {hasWAVAX ? '✅' : '❌'}</span>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Total: {currentAssets.length} assets configured
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-3 mb-6">
        <h4 className="font-medium text-blue-800">Add Individual Assets:</h4>
        
        <button
          onClick={() => addSupportedAsset(tokens.USDC, 'USDC')}
          disabled={isLoading}
          className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Add USDC ({tokens.USDC})
        </button>

        <button
          onClick={() => addSupportedAsset(tokens.WAVAX, 'WAVAX')}
          disabled={isLoading}
          className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Add WAVAX ({tokens.WAVAX})
        </button>

        {tokens.USDT && (
          <button
            onClick={() => addSupportedAsset(tokens.USDT, 'USDT')}
            disabled={isLoading}
            className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Add USDT ({tokens.USDT})
          </button>
        )}
      </div>

      <div className="mb-6">
        <h4 className="font-medium text-blue-800 mb-2">Quick Setup:</h4>
        {hasUSDC && hasWAVAX ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-800 font-medium">✅ All assets are configured!</p>
            <p className="text-green-700 text-sm mt-1">Your contract is ready to use. You can now supply and borrow assets.</p>
          </div>
        ) : (
          <button
            onClick={addAllAssets}
            disabled={isLoading}
            className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 font-medium"
          >
            {isLoading ? 'Adding Assets...' : 'Add All Assets (Recommended)'}
          </button>
        )}
      </div>

      {status && (
        <div className="p-4 bg-gray-100 rounded border">
          <p className="text-sm"><strong>Status:</strong> {status}</p>
        </div>
      )}

      <div className="mt-6 text-sm text-blue-600">
        <h5 className="font-medium mb-2">Instructions:</h5>
        <ol className="list-decimal list-inside space-y-1">
          <li>Make sure you're the contract owner</li>
          <li>Click "Add All Assets" for quick setup</li>
          <li>Wait for transaction confirmations</li>
          <li>Refresh the page to see the assets in the dashboard</li>
          <li>You can then start using supply/borrow functions</li>
        </ol>
      </div>
    </div>
  );
}
