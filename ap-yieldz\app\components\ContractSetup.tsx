'use client';

import { useState } from 'react';
import { useAccount, useWriteContract } from 'wagmi';
import { SIMPLE_LENDING_AGGREGATOR_ABI } from '../blockchain/abi/SimpleLendingAggregator';
import { LENDING_APY_AGGREGATOR_ADDRESS, getCurrentTokens } from '../blockchain/config/wagmi';

export default function ContractSetup() {
  const { address, isConnected } = useAccount();
  const { writeContract } = useWriteContract();
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState('');
  
  const tokens = getCurrentTokens();

  const addSupportedAsset = async (tokenAddress: string, tokenSymbol: string) => {
    if (!isConnected) {
      setStatus('Please connect your wallet first');
      return;
    }

    setIsLoading(true);
    setStatus(`Adding ${tokenSymbol} as supported asset...`);

    try {
      await writeContract({
        address: LENDING_APY_AGGREGATOR_ADDRESS,
        abi: SIMPLE_LENDING_AGGREGATOR_ABI,
        functionName: 'addSupportedAsset',
        args: [tokenAddress as `0x${string}`],
      });
      
      setStatus(`${tokenSymbol} added successfully! Please wait for transaction confirmation.`);
    } catch (error) {
      console.error('Error adding supported asset:', error);
      setStatus(`Failed to add ${tokenSymbol}. Check console for details.`);
    } finally {
      setIsLoading(false);
    }
  };

  const addAllAssets = async () => {
    if (!isConnected) {
      setStatus('Please connect your wallet first');
      return;
    }

    setIsLoading(true);
    setStatus('Adding all supported assets...');

    try {
      // Add USDC
      await writeContract({
        address: LENDING_APY_AGGREGATOR_ADDRESS,
        abi: SIMPLE_LENDING_AGGREGATOR_ABI,
        functionName: 'addSupportedAsset',
        args: [tokens.USDC as `0x${string}`],
      });

      // Wait a bit between transactions
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Add WAVAX
      await writeContract({
        address: LENDING_APY_AGGREGATOR_ADDRESS,
        abi: SIMPLE_LENDING_AGGREGATOR_ABI,
        functionName: 'addSupportedAsset',
        args: [tokens.WAVAX as `0x${string}`],
      });

      setStatus('All assets added successfully! Please wait for transaction confirmations.');
    } catch (error) {
      console.error('Error adding assets:', error);
      setStatus('Failed to add some assets. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isConnected) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">Contract Setup Required</h3>
        <p className="text-yellow-700">Please connect your wallet to set up the contract with supported assets.</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 className="text-lg font-semibold text-blue-800 mb-4">Contract Setup</h3>
      
      <div className="mb-4">
        <p className="text-blue-700 mb-2">
          <strong>Contract Address:</strong> {LENDING_APY_AGGREGATOR_ADDRESS}
        </p>
        <p className="text-blue-700 mb-4">
          <strong>Connected as:</strong> {address}
        </p>
      </div>

      <div className="space-y-3 mb-6">
        <h4 className="font-medium text-blue-800">Add Individual Assets:</h4>
        
        <button
          onClick={() => addSupportedAsset(tokens.USDC, 'USDC')}
          disabled={isLoading}
          className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Add USDC ({tokens.USDC})
        </button>

        <button
          onClick={() => addSupportedAsset(tokens.WAVAX, 'WAVAX')}
          disabled={isLoading}
          className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Add WAVAX ({tokens.WAVAX})
        </button>

        {tokens.USDT && (
          <button
            onClick={() => addSupportedAsset(tokens.USDT, 'USDT')}
            disabled={isLoading}
            className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Add USDT ({tokens.USDT})
          </button>
        )}
      </div>

      <div className="mb-6">
        <h4 className="font-medium text-blue-800 mb-2">Quick Setup:</h4>
        <button
          onClick={addAllAssets}
          disabled={isLoading}
          className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 font-medium"
        >
          Add All Assets (Recommended)
        </button>
      </div>

      {status && (
        <div className="p-4 bg-gray-100 rounded border">
          <p className="text-sm"><strong>Status:</strong> {status}</p>
        </div>
      )}

      <div className="mt-6 text-sm text-blue-600">
        <h5 className="font-medium mb-2">Instructions:</h5>
        <ol className="list-decimal list-inside space-y-1">
          <li>Make sure you're the contract owner</li>
          <li>Click "Add All Assets" for quick setup</li>
          <li>Wait for transaction confirmations</li>
          <li>Refresh the page to see the assets in the dashboard</li>
          <li>You can then start using supply/borrow functions</li>
        </ol>
      </div>
    </div>
  );
}
