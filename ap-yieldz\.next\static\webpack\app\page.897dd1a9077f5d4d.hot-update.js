"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/services/aaveAPI.ts":
/*!*********************************!*\
  !*** ./app/services/aaveAPI.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchLiveAaveRates: () => (/* binding */ fetchLiveAaveRates),\n/* harmony export */   testAaveAPIConnection: () => (/* binding */ testAaveAPIConnection)\n/* harmony export */ });\n// Aave API service for fetching live rates\n// Documentation: https://docs.aave.com/developers/deployed-contracts/v3-mainnet\n// Avalanche Fuji testnet token addresses\nconst FUJI_TOKEN_ADDRESSES = {\n    USDC: '0x5425890298aed601595a70AB815c96711a31Bc65',\n    WAVAX: '0xd00ae08403B9bbb9124bB305C09058E32C39A48c',\n    USDT: '0x1f1E7c893855525b303f99bDF5c3c05BE09ca251'\n};\n// Aave V3 Subgraph for Avalanche Fuji (correct URL)\nconst AAVE_SUBGRAPH_URL = 'https://api.thegraph.com/subgraphs/name/aave/protocol-v3-avalanche';\n// Avalanche Fuji Pool Address (correct address)\nconst AAVE_POOL_ADDRESS = '0x794a61358D6845594F94dc1DB02A252b5b4814aD';\n// Convert Aave rate format (ray) to percentage\nfunction rayToPercentage(ray) {\n    const RAY = 10 ** 27;\n    const SECONDS_PER_YEAR = 31536000;\n    const ratePerSecond = parseInt(ray) / RAY;\n    const ratePerYear = ratePerSecond * SECONDS_PER_YEAR;\n    return ratePerYear * 100;\n}\n// Fetch reserve data from Aave subgraph with better error handling\nasync function fetchFromSubgraph() {\n    const query = '\\n    query GetReserves {\\n      reserves(\\n        where: {\\n          pool: \"0x794a61358d6845594f94dc1db02a252b5b4814ad\"\\n        }\\n        first: 10\\n      ) {\\n        id\\n        underlyingAsset\\n        name\\n        symbol\\n        decimals\\n        liquidityRate\\n        variableBorrowRate\\n        stableBorrowRate\\n        liquidityIndex\\n        variableBorrowIndex\\n        lastUpdateTimestamp\\n      }\\n    }\\n  ';\n    try {\n        var _data_data;\n        console.log('Attempting to fetch from subgraph:', AAVE_SUBGRAPH_URL);\n        const response = await fetch(AAVE_SUBGRAPH_URL, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            },\n            body: JSON.stringify({\n                query\n            })\n        });\n        console.log('Subgraph response status:', response.status);\n        console.log('Subgraph response headers:', response.headers);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Subgraph error response:', errorText);\n            throw new Error(\"Subgraph request failed: \".concat(response.status, \" - \").concat(errorText.substring(0, 200)));\n        }\n        const contentType = response.headers.get('content-type');\n        if (!contentType || !contentType.includes('application/json')) {\n            const responseText = await response.text();\n            console.error('Non-JSON response from subgraph:', responseText.substring(0, 500));\n            throw new Error(\"Expected JSON response but got: \".concat(contentType));\n        }\n        const data = await response.json();\n        console.log('Subgraph response data:', data);\n        if (data.errors) {\n            throw new Error(\"Subgraph errors: \".concat(JSON.stringify(data.errors)));\n        }\n        return ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.reserves) || [];\n    } catch (error) {\n        console.error('Error fetching from subgraph:', error);\n        throw error;\n    }\n}\n// Alternative: Use a more reliable API or create mock data based on typical Fuji rates\nasync function fetchFromAlternativeSource() {\n    // Since testnet APIs are unreliable, we'll create realistic mock data\n    // based on typical Aave V3 rates on testnets\n    console.log('Using alternative data source (realistic testnet rates)');\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n    // Return realistic testnet rates (these change frequently on testnets)\n    return [\n        {\n            id: 'usdc-fuji',\n            underlyingAsset: FUJI_TOKEN_ADDRESSES.USDC,\n            name: 'USD Coin',\n            symbol: 'USDC',\n            decimals: 6,\n            liquidityRate: '42500000000000000000000000',\n            variableBorrowRate: '51500000000000000000000000',\n            stableBorrowRate: '55000000000000000000000000',\n            liquidityIndex: '1000000000000000000000000000',\n            variableBorrowIndex: '1000000000000000000000000000',\n            lastUpdateTimestamp: Math.floor(Date.now() / 1000)\n        },\n        {\n            id: 'wavax-fuji',\n            underlyingAsset: FUJI_TOKEN_ADDRESSES.WAVAX,\n            name: 'Wrapped AVAX',\n            symbol: 'WAVAX',\n            decimals: 18,\n            liquidityRate: '28500000000000000000000000',\n            variableBorrowRate: '42500000000000000000000000',\n            stableBorrowRate: '45000000000000000000000000',\n            liquidityIndex: '1000000000000000000000000000',\n            variableBorrowIndex: '1000000000000000000000000000',\n            lastUpdateTimestamp: Math.floor(Date.now() / 1000)\n        },\n        {\n            id: 'usdt-fuji',\n            underlyingAsset: FUJI_TOKEN_ADDRESSES.USDT,\n            name: 'Tether USD',\n            symbol: 'USDT',\n            decimals: 6,\n            liquidityRate: '41500000000000000000000000',\n            variableBorrowRate: '52500000000000000000000000',\n            stableBorrowRate: '56000000000000000000000000',\n            liquidityIndex: '1000000000000000000000000000',\n            variableBorrowIndex: '1000000000000000000000000000',\n            lastUpdateTimestamp: Math.floor(Date.now() / 1000)\n        }\n    ];\n}\n// Get live rates for supported tokens\nasync function fetchLiveAaveRates() {\n    try {\n        console.log('Fetching live Aave rates...');\n        // Try subgraph first, then fallback to API\n        let reserves;\n        try {\n            reserves = await fetchFromSubgraph();\n            console.log('Successfully fetched from subgraph');\n        } catch (subgraphError) {\n            console.log('Subgraph failed, trying Aave API...');\n            reserves = await fetchFromAaveAPI();\n            console.log('Successfully fetched from Aave API');\n        }\n        // Process the reserves data\n        const rates = {\n            USDC: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            },\n            WAVAX: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            },\n            USDT: {\n                supplyAPY: 0,\n                borrowAPY: 0\n            }\n        };\n        reserves.forEach((reserve)=>{\n            const address = reserve.underlyingAsset.toLowerCase();\n            // Match by address\n            if (address === FUJI_TOKEN_ADDRESSES.USDC.toLowerCase()) {\n                rates.USDC.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.USDC.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            } else if (address === FUJI_TOKEN_ADDRESSES.WAVAX.toLowerCase()) {\n                rates.WAVAX.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.WAVAX.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            } else if (address === FUJI_TOKEN_ADDRESSES.USDT.toLowerCase()) {\n                rates.USDT.supplyAPY = rayToPercentage(reserve.liquidityRate);\n                rates.USDT.borrowAPY = rayToPercentage(reserve.variableBorrowRate);\n            }\n        });\n        console.log('Live Aave rates:', rates);\n        return rates;\n    } catch (error) {\n        console.error('Failed to fetch live rates, using fallback:', error);\n        // Fallback to reasonable default rates if API fails\n        return {\n            USDC: {\n                supplyAPY: 4.25,\n                borrowAPY: 5.15\n            },\n            WAVAX: {\n                supplyAPY: 2.85,\n                borrowAPY: 4.25\n            },\n            USDT: {\n                supplyAPY: 4.15,\n                borrowAPY: 5.25\n            }\n        };\n    }\n}\n// Test function to verify API connectivity\nasync function testAaveAPIConnection() {\n    try {\n        await fetchLiveAaveRates();\n        return true;\n    } catch (error) {\n        console.error('Aave API connection test failed:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/services/aaveAPI.ts\n"));

/***/ })

});