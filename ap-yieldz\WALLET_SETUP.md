# Wallet Setup Guide

## Supported Wallets

Your DeFi application now supports the following wallets:

### Popular Wallets
1. **Core Wallet** 🔥 **(NEW)**
   - Native Avalanche wallet
   - Best for Avalanche ecosystem
   - Download: [core.app](https://core.app/)
   - Supports: Avalanche C-Chain, X-Chain, P-Chain

2. **MetaMask**
   - Most popular Ethereum wallet
   - Requires manual Avalanche network setup
   - Download: [metamask.io](https://metamask.io/)

3. **Browser Injected Wallets**
   - Automatically detects installed browser wallets
   - Includes Core Wallet if installed as browser extension

### Other Wallets
1. **WalletConnect**
   - Connect mobile wallets
   - Supports 100+ mobile wallets
   - Scan QR code to connect

## Network Configuration

### Avalanche Fuji Testnet
- **Network Name**: Avalanche Fuji C-Chain
- **RPC URL**: `https://api.avax-test.network/ext/bc/C/rpc`
- **Chain ID**: `43113`
- **Currency Symbol**: `AVAX`
- **Block Explorer**: `https://testnet.snowtrace.io/`

### Core Wallet Setup
1. **Install Core Wallet** from [core.app](https://core.app/)
2. **Create or import** your wallet
3. **Switch to Fuji testnet** in Core Wallet settings
4. **Get test AVAX** from [Avalanche Faucet](https://faucet.avax.network/)
5. **Connect to the app** using the Core Wallet option

### MetaMask Setup for Avalanche
1. **Add Avalanche Fuji** network manually:
   - Go to Settings → Networks → Add Network
   - Enter the network details above
2. **Get test AVAX** from the faucet
3. **Connect to the app**

## Troubleshooting

### Core Wallet Not Appearing
- Make sure Core Wallet is installed and unlocked
- Refresh the page
- Try the "Browser Injected Wallets" option

### Connection Issues
- Check you're on the correct network (Avalanche Fuji)
- Make sure your wallet is unlocked
- Clear browser cache and try again

### Transaction Failures
- Ensure you have enough AVAX for gas fees
- Check if the contract has supported assets configured
- Verify you're on Avalanche Fuji testnet

## Getting Test Tokens

### AVAX (for gas fees)
- Visit: [Avalanche Faucet](https://faucet.avax.network/)
- Enter your wallet address
- Request test AVAX

### Test USDC/USDT (for trading)
- Use the Avalanche Fuji faucet
- Or swap some test AVAX for test tokens on DEXs

## Contract Addresses (Fuji Testnet)

- **SimpleLendingAggregator**: `******************************************`
- **USDC**: `******************************************`
- **WAVAX**: `******************************************`
- **USDT**: `******************************************`

## Support

If you encounter any wallet connection issues:
1. Check this guide first
2. Try a different wallet
3. Ensure you're on the correct network
4. Contact support with specific error messages
