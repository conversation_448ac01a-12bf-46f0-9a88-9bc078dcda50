"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts":
/*!********************************************!*\
  !*** ./app/blockchain/hooks/useAPYData.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAPYData: () => (/* binding */ useAPYData),\n/* harmony export */   useHistoricalAPY: () => (/* binding */ useHistoricalAPY)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/aaveAPI */ \"(app-pages-browser)/./app/services/aaveAPI.ts\");\n\n\n// For Aave-only integration, we'll use mock data for now\n// Real Aave API integration can be added later\n// const AAVE_API_URL = 'https://aave-api-v2.aave.com/data/liquidity/v2';\n// Mapping of asset symbols to their respective IDs on Aave V3\nconst AAVE_RESERVE_IDS = {\n    'USDC': '******************************************',\n    'USDT': '******************************************',\n    'WETH': '******************************************',\n    'WBTC': '******************************************'\n};\n// Aave-only APY data for Fuji testnet (mock data for demonstration)\nconst AAVE_ONLY_APY_DATA = [\n    {\n        asset: 'usdc',\n        symbol: 'USDC',\n        aaveSupplyAPY: 4.25,\n        aaveBorrowAPY: 5.15,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    },\n    {\n        asset: 'wavax',\n        symbol: 'WAVAX',\n        aaveSupplyAPY: 2.85,\n        aaveBorrowAPY: 4.25,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    },\n    {\n        asset: 'usdt',\n        symbol: 'USDT',\n        aaveSupplyAPY: 4.15,\n        aaveBorrowAPY: 5.25,\n        morphoSupplyAPY: 0,\n        morphoBorrowAPY: 0,\n        bestSupplyProtocol: 'aave',\n        bestBorrowProtocol: 'aave'\n    }\n];\nfunction useAPYData() {\n    const [apyData, setApyData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAPYData.useEffect\": ()=>{\n            const fetchAPYData = {\n                \"useAPYData.useEffect.fetchAPYData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        console.log('Fetching live Aave APY data...');\n                        // TEMPORARY: Force fallback data for testing\n                        // Remove this after confirming the display works\n                        const FORCE_FALLBACK = true;\n                        if (FORCE_FALLBACK) {\n                            console.log('TEMPORARY: Using fallback data for testing');\n                            setApyData(AAVE_ONLY_APY_DATA);\n                            setError('Temporarily using fallback data for testing');\n                            return;\n                        }\n                        // Fetch live rates from Aave API\n                        const liveRates = await (0,_services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__.fetchLiveAaveRates)();\n                        console.log('Raw live rates from API:', liveRates);\n                        // Check if we got valid rates\n                        const hasValidRates = Object.values(liveRates).some({\n                            \"useAPYData.useEffect.fetchAPYData.hasValidRates\": (token)=>token.supplyAPY > 0 || token.borrowAPY > 0\n                        }[\"useAPYData.useEffect.fetchAPYData.hasValidRates\"]);\n                        if (!hasValidRates) {\n                            console.log('API returned zero rates, using fallback data');\n                            throw new Error('API returned invalid rates (all zeros)');\n                        }\n                        // Convert to our APYData format\n                        const liveAPYData = [\n                            {\n                                asset: 'usdc',\n                                symbol: 'USDC',\n                                aaveSupplyAPY: liveRates.USDC.supplyAPY,\n                                aaveBorrowAPY: liveRates.USDC.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            },\n                            {\n                                asset: 'wavax',\n                                symbol: 'WAVAX',\n                                aaveSupplyAPY: liveRates.WAVAX.supplyAPY,\n                                aaveBorrowAPY: liveRates.WAVAX.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            },\n                            {\n                                asset: 'usdt',\n                                symbol: 'USDT',\n                                aaveSupplyAPY: liveRates.USDT.supplyAPY,\n                                aaveBorrowAPY: liveRates.USDT.borrowAPY,\n                                morphoSupplyAPY: 0,\n                                morphoBorrowAPY: 0,\n                                bestSupplyProtocol: 'aave',\n                                bestBorrowProtocol: 'aave'\n                            }\n                        ];\n                        setApyData(liveAPYData);\n                        setError(null);\n                        console.log('Live APY data loaded successfully:', liveAPYData);\n                    } catch (err) {\n                        console.error('Error loading live APY data:', err);\n                        console.log('Falling back to mock data...');\n                        // Fallback to mock data if API fails\n                        setApyData(AAVE_ONLY_APY_DATA);\n                        setError('Using fallback APY data - live rates unavailable');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useAPYData.useEffect.fetchAPYData\"];\n            fetchAPYData();\n            // Refresh data every 60 seconds for live rates\n            const interval = setInterval(fetchAPYData, 60000);\n            return ({\n                \"useAPYData.useEffect\": ()=>clearInterval(interval)\n            })[\"useAPYData.useEffect\"];\n        }\n    }[\"useAPYData.useEffect\"], []);\n    const getAPYForAsset = (asset)=>{\n        const result = apyData.find((data)=>data.asset === asset.toLowerCase() || data.symbol === asset.toUpperCase());\n        console.log(\"Getting APY for \".concat(asset, \":\"), result);\n        return result;\n    };\n    return {\n        apyData,\n        loading,\n        error,\n        getAPYForAsset,\n        refresh: async ()=>{\n            setLoading(true);\n            try {\n                console.log('Manually refreshing APY data...');\n                const liveRates = await (0,_services_aaveAPI__WEBPACK_IMPORTED_MODULE_1__.fetchLiveAaveRates)();\n                console.log('Manual refresh - Raw live rates:', liveRates);\n                // Check if we got valid rates\n                const hasValidRates = Object.values(liveRates).some((token)=>token.supplyAPY > 0 || token.borrowAPY > 0);\n                if (!hasValidRates) {\n                    console.log('Manual refresh - API returned zero rates, using fallback data');\n                    throw new Error('API returned invalid rates (all zeros)');\n                }\n                const liveAPYData = [\n                    {\n                        asset: 'usdc',\n                        symbol: 'USDC',\n                        aaveSupplyAPY: liveRates.USDC.supplyAPY,\n                        aaveBorrowAPY: liveRates.USDC.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    },\n                    {\n                        asset: 'wavax',\n                        symbol: 'WAVAX',\n                        aaveSupplyAPY: liveRates.WAVAX.supplyAPY,\n                        aaveBorrowAPY: liveRates.WAVAX.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    },\n                    {\n                        asset: 'usdt',\n                        symbol: 'USDT',\n                        aaveSupplyAPY: liveRates.USDT.supplyAPY,\n                        aaveBorrowAPY: liveRates.USDT.borrowAPY,\n                        morphoSupplyAPY: 0,\n                        morphoBorrowAPY: 0,\n                        bestSupplyProtocol: 'aave',\n                        bestBorrowProtocol: 'aave'\n                    }\n                ];\n                setApyData(liveAPYData);\n                setError(null);\n            } catch (error) {\n                console.error('Manual refresh failed:', error);\n                setApyData(AAVE_ONLY_APY_DATA);\n                setError('Refresh failed - using fallback data');\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n}\n// Historical APY data for charts\nfunction useHistoricalAPY(asset, protocol) {\n    let days = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useHistoricalAPY.useEffect\": ()=>{\n            const generateMockHistoricalData = {\n                \"useHistoricalAPY.useEffect.generateMockHistoricalData\": ()=>{\n                    // Get base APY from Aave-only data\n                    const assetData = AAVE_ONLY_APY_DATA.find({\n                        \"useHistoricalAPY.useEffect.generateMockHistoricalData.assetData\": (d)=>d.asset === asset.toLowerCase() || d.symbol === asset.toUpperCase()\n                    }[\"useHistoricalAPY.useEffect.generateMockHistoricalData.assetData\"]);\n                    if (!assetData) {\n                        setData([]);\n                        setLoading(false);\n                        return;\n                    }\n                    const baseSupplyAPY = protocol === 'aave' ? assetData.aaveSupplyAPY : assetData.morphoSupplyAPY;\n                    const baseBorrowAPY = protocol === 'aave' ? assetData.aaveBorrowAPY : assetData.morphoBorrowAPY;\n                    // Generate realistic historical data with small variations\n                    const historicalData = Array.from({\n                        length: days\n                    }).map({\n                        \"useHistoricalAPY.useEffect.generateMockHistoricalData.historicalData\": (_, i)=>{\n                            const date = new Date();\n                            date.setDate(date.getDate() - (days - i));\n                            // Add small random variations to make it look realistic\n                            const supplyVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation\n                            const borrowVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation\n                            return {\n                                date: date.toISOString().split('T')[0],\n                                supplyAPY: Math.max(0, baseSupplyAPY + supplyVariation),\n                                borrowAPY: Math.max(0, baseBorrowAPY + borrowVariation)\n                            };\n                        }\n                    }[\"useHistoricalAPY.useEffect.generateMockHistoricalData.historicalData\"]);\n                    setData(historicalData);\n                    setLoading(false);\n                }\n            }[\"useHistoricalAPY.useEffect.generateMockHistoricalData\"];\n            if (asset) {\n                generateMockHistoricalData();\n            }\n        }\n    }[\"useHistoricalAPY.useEffect\"], [\n        asset,\n        protocol,\n        days\n    ]);\n    return {\n        data,\n        loading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blockchain/hooks/useAPYData.ts\n"));

/***/ })

});