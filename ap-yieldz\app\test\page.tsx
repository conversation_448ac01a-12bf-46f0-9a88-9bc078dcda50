import AaveTestComponent from '../components/AaveTestComponent';
import ContractSetup from '../components/ContractSetup';
import ContractDebug from '../components/ContractDebug';
import APYStatus from '../components/APYStatus';
import APITestPanel from '../components/APITestPanel';

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Aave Integration Test</h1>

        <div className="mb-8">
          <ContractDebug />
        </div>

        <div className="mb-8">
          <ContractSetup />
        </div>

        <div className="mb-8">
          <APYStatus />
        </div>

        <div className="mb-8">
          <APITestPanel />
        </div>

        <AaveTestComponent />
      </div>
    </div>
  );
}
